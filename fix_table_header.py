#!/usr/bin/env python3
"""
修复HTML表格标题对齐问题的脚本
主要修复：依赖变量标题的colspan值，使其正确跨越所有数据列
"""

import re
import os
from pathlib import Path

def fix_table_header_alignment(html_content):
    """
    修复表格标题的colspan值，使依赖变量标题正确居中
    """
    lines = html_content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # 查找包含"Dependent variable"的标题行
        if 'Dependent variable' in line and 'colspan=' in line:
            # 计算实际的列数
            # 查找下一行（Predictors行）来确定总列数
            if i + 2 < len(lines):
                predictors_line = lines[i + 2]  # 通常Predictors行在标题行后2行
                if 'Predictors' in predictors_line:
                    # 计算这一行的td数量
                    td_count = predictors_line.count('<td')
                    # 依赖变量标题应该跨越除第一列外的所有列
                    correct_colspan = td_count - 1
                    
                    # 替换colspan值
                    line = re.sub(r'colspan="\d+"', f'colspan="{correct_colspan}"', line)
                    
                    # 确保标题居中对齐
                    if 'text-align: center' not in line:
                        # 在style属性中添加text-align: center
                        line = re.sub(r'style="([^"]*)"', r'style="\1 text-align: center;"', line)
        
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def process_html_files():
    """
    处理所有的HTML表格文件
    """
    # 查找所有的HTML文件
    html_files = [
        "B2_p3_extended_inspection_OE_style.html",
        "B3_p4_extended_inspection_OE_style.html",
        "B4_SOE_result_p1_OE_style.html",
        "B5_SOE_result_p2_OE_style.html",
        "B6_plm_p3_OE_style.html",
        "B7_plm_p4_OE_style.html"
    ]
    
    for filename in html_files:
        if os.path.exists(filename):
            print(f"处理文件: {filename}")
            
            # 读取文件
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修复标题对齐
            fixed_content = fix_table_header_alignment(content)
            
            # 写回文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            
            print(f"已修复: {filename}")
        else:
            print(f"文件不存在: {filename}")

if __name__ == "__main__":
    process_html_files()
    print("所有表格标题对齐修复完成！")
