#!/usr/bin/env python3
"""
修复HTML表格排版问题的脚本
主要修复：
1. 表格头部列数匹配
2. 添加缺失的结束标签
3. 修正colspan属性
4. 确保表格结构一致
"""

import re
import os
from pathlib import Path

def count_columns_in_data_row(html_content):
    """计算数据行的列数"""
    lines = html_content.split('\n')
    max_columns = 0

    # 查找包含"Predictors"的行，这通常是表头行
    for i, line in enumerate(lines):
        if '<td' in line and 'Predictors' in line:
            # 计算这一行的td数量
            td_count = line.count('<td')
            max_columns = max(max_columns, td_count)

            # 检查接下来几行的列数
            for j in range(i + 1, min(i + 10, len(lines))):
                if '<td' in lines[j] and not any(keyword in lines[j] for keyword in ['Random Effects', 'σ', 'τ', 'ICC', 'Observations']):
                    td_count = lines[j].count('<td')
                    max_columns = max(max_columns, td_count)
                elif '<tr>' in lines[j] and j > i + 1:
                    break

    # 如果还是没找到，尝试其他方法
    if max_columns == 0:
        for line in lines:
            if '<td' in line and not any(keyword in line for keyword in ['Random Effects', 'σ', 'τ', 'ICC', 'Observations', 'Marginal R', 'AIC', 'log-Likelihood']):
                td_count = line.count('<td')
                max_columns = max(max_columns, td_count)

    return max(max_columns, 4)  # 至少4列

def fix_table_structure(html_content):
    """修复表格结构"""
    lines = html_content.split('\n')
    fixed_lines = []
    
    # 计算实际的列数
    actual_columns = count_columns_in_data_row(html_content)
    print(f"检测到的列数: {actual_columns}")
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # 修复表头第一行 - 调整colspan
        if '<th' in line and 'colspan="2"' in line:
            # 计算正确的colspan值（总列数-1）
            correct_colspan = actual_columns - 1
            line = line.replace('colspan="2"', f'colspan="{correct_colspan}"')
        
        # 修复Random Effects部分的colspan
        if 'Random Effects' in line:
            line = line.replace('colspan="7"', f'colspan="{actual_columns}"')
        
        # 修复其他colspan="2"的情况
        if '<td' in line and 'colspan="2"' in line and 'Random Effects' not in line:
            # 对于统计数据行，使用实际列数-1
            if any(stat in line for stat in ['σ', 'τ', 'ICC', 'Observations', 'Marginal R', 'AIC', 'log-Likelihood']):
                correct_colspan = actual_columns - 1
                line = line.replace('colspan="2"', f'colspan="{correct_colspan}"')
        
        fixed_lines.append(line)
        
        # 检查是否缺少结束标签
        if '<tr>' in line or ('<td' in line and i < len(lines) - 1):
            # 查看接下来的行，如果下一行是新的<tr>或者是表格结束，则需要添加</tr>
            needs_closing = True
            
            # 向前查找，看是否已经有</tr>
            for j in range(i + 1, min(i + 10, len(lines))):
                if j >= len(lines):
                    break
                next_line = lines[j].strip()
                if '</tr>' in next_line:
                    needs_closing = False
                    break
                elif '<tr>' in next_line or '</table>' in next_line:
                    break
            
            # 如果需要添加结束标签
            if needs_closing and ('<tr>' in line or '<td' in line):
                # 查找这个tr块的结束位置
                j = i + 1
                while j < len(lines) and '</tr>' not in lines[j] and '<tr>' not in lines[j] and '</table>' not in lines[j]:
                    j += 1
                
                # 在适当位置插入</tr>
                if j < len(lines) and '</tr>' not in lines[j-1]:
                    fixed_lines.append('</tr>')
        
        i += 1
    
    return '\n'.join(fixed_lines)

def fix_html_file(file_path):
    """修复单个HTML文件"""
    print(f"修复文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复表格结构
    fixed_content = fix_table_structure(content)
    
    # 创建备份
    backup_path = file_path.with_suffix('.html.backup')
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"创建备份: {backup_path}")
    
    # 写入修复后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"修复完成: {file_path}")

def main():
    """主函数"""
    # 获取当前目录下的所有HTML文件
    html_files = list(Path('.').glob('*.html'))
    
    if not html_files:
        print("未找到HTML文件")
        return
    
    print(f"找到 {len(html_files)} 个HTML文件")
    
    for html_file in html_files:
        try:
            fix_html_file(html_file)
        except Exception as e:
            print(f"修复文件 {html_file} 时出错: {e}")
    
    print("所有文件修复完成")

if __name__ == "__main__":
    main()
