
---
title: "Robustness Check"
output: html_notebook
---

# Package Installation and Setup
```{r setup, include=FALSE}
# Install required packages if not already installed
if (!require(lme4)) install.packages("lme4")
if (!require(sjPlot)) install.packages("sjPlot")
if (!require(clubSandwich)) install.packages("clubSandwich")
if (!require(modelsummary)) install.packages("modelsummary")
if (!require(gt)) install.packages("gt")
if (!require(flextable)) install.packages("flextable")
if (!require(tibble)) install.packages("tibble")

library(lme4)
library(sjPlot)
library(clubSandwich)
library(modelsummary)
library(gt)
library(flextable)
library(plm)
library(tibble)
```

```{r load_functions}
# Load the Organization & Environment style function
source("modelsummary_OE_style.R")
```

# Data loading
```{r Data analysis}

# Load the data file
load("dta1_20240903.RData")
# You can add a quick check to confirm the data loaded correctly

# 重新计算Age变量，使其随年份变化
library(lubridate)
library(dplyr)
dta1 <- dta1 %>%
  mutate(
    EndYear = ymd(EndYear),
    Year = year(EndYear),
    Age = Year - EstablishYear
  )

```



# Robustness test

## Short-term Impact of Inspection
Inspected 后三年标记为1, inspection当年标记为0
We assign a code of '1' to firms within the three-year period following their initial inspection to assess the short-term effects of regulatory pressure.
Table B2. Short-term Impact of Inspection  

```{r}
# # 创建一个新的数据框，只包含所需的列
# new_df <- select(dta1, Symbol, EndYear, inspection_year, first_inspection, after_first_inspection, extended_inspection)

# na_rows <- dta1 %>% filter(is.na(first_inspection))
# new_df <- select(na_rows, Symbol, EndYear, inspection_year, first_inspection, after_first_inspection, extended_inspection)

```

### P3 connection (continuous)
```{r}
# 加载必要的库
library(lubridate)
library(dplyr)
library(tidyr)

dta3 = dta1

# 创建新变量 extended_inspection
dta3 <- dta3 %>%
  group_by(Symbol) %>%  # 按照单位分组
  mutate(
    # 找到每个单位 first_inspection 为1的年份
    inspection_year = ifelse(first_inspection == 1, Year, NA),
    # 使用 coalesce 处理 NA 值，确保每个单位都有一个 inspection_year
    inspection_year = coalesce(inspection_year, lag(inspection_year, default = NA))
  ) %>%
  # 使用 fill 函数确保 inspection_year 被赋值到所有行
  fill(inspection_year, .direction = "downup") %>%
  ungroup()  # 解除分组

# 标记 inspection_year 及之后的两年
dta3$extended_inspection <- as.numeric(dta3$Year > dta3$inspection_year & dta3$Year <= (dta3$inspection_year + 3))
dta3 <- dta3 %>% mutate(extended_inspection = ifelse(is.na(extended_inspection), 0, extended_inspection))
#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure

library(lme4)
p3ext1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)


p3ext2 <- lmer(Environmental_Information_Disclosure ~ Age + extended_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

p3ext3 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + extended_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + connection_num:extended_inspection + (1|PROVINCE/CITY), data=dta3)

# Create Organization & Environment style table with detailed mixed model statistics
library(sjPlot)

# Define custom CSS for Organization & Environment journal style
oe_css <- list(
  css.table = "border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px; margin-top: 10px;",
  css.thead = "border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; padding: 4px 8px;",
  css.tdata = "border: none; padding: 3px 8px; text-align: center;",
  css.arc = "border-top: 1px solid black;",
  css.caption = "font-weight: bold; text-align: left; padding-bottom: 15px; margin-bottom: 5px;",
  css.subtitle = "font-style: italic; text-align: center; padding-bottom: 8px; margin-bottom: 8px;",
  css.firsttablecol = "text-align: left; padding-left: 0px; border: none;",
  css.leftalign = "text-align: left;",
  css.centeralign = "text-align: center;",
  css.footnote = "font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;",
  css.depvarheader = "text-align: center; font-weight: normal; padding: 8px; border-bottom: 1px solid #ccc; background-color: #f9f9f9;"
)

tab_model(p3ext1, p3ext2, p3ext3,
          title = "Table B2. Short-term Impact of Inspection",
          dv.labels = "Dependent variable:<br>Environmental Information Disclosure",
          show.aic = TRUE,
          show.loglik = TRUE,
          show.obs = TRUE,
          show.ngroups = TRUE,
          show.re.var = TRUE,
          show.icc = TRUE,
          show.intercept = FALSE, # Hide intercept
          show.p = FALSE,        # Hide p-values column
          show.se = TRUE,        # Show standard errors in parentheses
          show.ci = FALSE,       # Hide confidence intervals
          p.style = "stars",     # Display significance as stars
          p.threshold = c(0.1, 0.05, 0.01),  # Set significance thresholds
          digits = 3,            # 3 decimal places
          digits.re = 2,         # 2 decimal places for random effects
          pred.labels = c("Age", "Connections", "Inspection influence", "ESG Rating",
                         "Log(Registered Capital)", "Return on Assets", "Leverage",
                         "Connections × Inspection influence"),
          CSS = oe_css,
          file = "B2_p3_extended_inspection_OE_style.html")

# Fix table header alignment
fix_table_header <- function(filename) {
  if (file.exists(filename)) {
    content <- readLines(filename, warn = FALSE)
    # Fix colspan for dependent variable header
    content <- gsub('colspan="2"([^>]*>Dependent variable)', 'colspan="6"\\1', content)
    # Ensure center alignment for the header
    content <- gsub('(colspan="6"[^>]*style="[^"]*)(">Dependent variable)', '\\1 text-align: center;\\2', content)
    writeLines(content, filename)
  }
}

fix_table_header("B2_p3_extended_inspection_OE_style.html")


```

### P4 contral/local connection (continuous)
Table B3. Short-term Impact of Inspection (central and local connections)
```{r}

library(lme4)

p4ext1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

p4ext2 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + extended_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + central_connection:extended_inspection + (1|PROVINCE/CITY), data=dta3)

p4ext3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

p4ext4 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + extended_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + local_connection:extended_inspection + (1|PROVINCE/CITY), data=dta3)

# Create Organization & Environment style table for central/local connections
tab_model(p4ext1, p4ext2, p4ext3, p4ext4,
          title = "Table B3. Short-term Impact of Inspection (Central and Local Connections)",
          dv.labels = "Dependent variable:<br>Environmental Information Disclosure",
          rm.terms = c("as.factor(IndustryName)"),
          show.aic = TRUE,
          show.loglik = TRUE,
          show.obs = TRUE,
          show.ngroups = TRUE,
          show.re.var = TRUE,
          show.icc = TRUE,
          show.intercept = FALSE, # Hide intercept
          show.p = FALSE,        # Hide p-values column
          show.se = TRUE,        # Show standard errors in parentheses
          show.ci = FALSE,       # Hide confidence intervals
          p.style = "stars",     # Display significance as stars
          p.threshold = c(0.1, 0.05, 0.01),  # Set significance thresholds
          digits = 3,            # 3 decimal places
          digits.re = 2,         # 2 decimal places for random effects
          pred.labels = c("Age", "Central Connections", "Local Connections", "Inspection influence",
                         "ESG Rating", "Log(Registered Capital)", "Return on Assets", "Leverage",
                         "Central Connections × Inspection influence", "Local Connections × Inspection influence"),
          CSS = oe_css,
          file = "B3_p4_extended_inspection_OE_style.html")

# Fix table header alignment
fix_table_header <- function(filename) {
  if (file.exists(filename)) {
    content <- readLines(filename, warn = FALSE)
    # Fix colspan for dependent variable header
    content <- gsub('colspan="2"([^>]*>Dependent variable)', 'colspan="8"\\1', content)
    # Ensure center alignment for the header
    content <- gsub('(colspan="8"[^>]*style="[^"]*)(">Dependent variable)', '\\1 text-align: center;\\2', content)
    writeLines(content, filename)
  }
}

fix_table_header("B3_p4_extended_inspection_OE_style.html")

```



## State-owned Enterprises
### P3 SOE new dataset
Table B4. State-owned Enterprises
We labelled firms where the actual controllers are state-owned institutions or state-owned enterprises as politically connected. 
```{r P3 SOE new dataset 1}
#P3

library(lme4)
# Model 1: SOE main effect with controls
p1soe1 <- lmer(Environmental_Information_Disclosure ~ SOE_new + after_first_inspection +
               Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
               (1|PROVINCE/CITY), data=dta1)

# Model 2: Policy effect baseline
p1soe2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection +
               Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
               (1|PROVINCE/CITY), data=dta1)

# Model 3: SOE-Policy interaction
p1soe3 <- lmer(Environmental_Information_Disclosure ~ SOE_new * after_first_inspection +
               Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
               (1|PROVINCE/CITY), data=dta1)

# Create Organization & Environment style table for SOE analysis
tab_model(p1soe1, p1soe2, p1soe3,
          title = "Table B4. State-owned Enterprises and Environmental Disclosure",
          dv.labels = "Dependent variable:<br>Environmental Information Disclosure",
          pred.labels = c("SOE", "Inspection",
                         "Age", "ROA", "Leverage",
                         "Log(Registered Capital)", "ESG Rating",
                         "SOE × Inspection"),
          rm.terms = c("as.factor(IndustryName)"),
          show.aic = TRUE,
          show.loglik = TRUE,
          show.obs = TRUE,
          show.ngroups = TRUE,
          show.re.var = TRUE,
          show.icc = TRUE,
          show.intercept = FALSE, # Hide intercept
          show.p = FALSE,        # Hide p-values column
          show.se = TRUE,        # Show standard errors in parentheses
          show.ci = FALSE,       # Hide confidence intervals
          p.style = "stars",     # Display significance as stars
          p.threshold = c(0.1, 0.05, 0.01),  # Set significance thresholds
          digits = 3,            # 3 decimal places
          digits.re = 2,         # 2 decimal places for random effects
          CSS = oe_css,
          file = "B4_SOE_result_p3_OE_style.html")


# Fix table header alignment
fix_table_header <- function(filename) {
  if (file.exists(filename)) {
    content <- readLines(filename, warn = FALSE)
    # Fix colspan for dependent variable header
    content <- gsub('colspan="2"([^>]*>Dependent variable)', 'colspan="6"\\1', content)
    # Ensure center alignment for the header
    content <- gsub('(colspan="6"[^>]*style="[^"]*)(">Dependent variable)', '\\1 text-align: center;\\2', content)
    writeLines(content, filename)
  }
}

fix_table_header("B4_SOE_result_p3_OE_style.html")


```


### P4 central/local SOE Level
```{r P4 central/local SEO level 0}
# P4 Central/Local SOE Analysis - Optimized variable ordering

library(lme4)
# Model 1: Central SOE main effect
p2soe1 <- lmer(Environmental_Information_Disclosure ~ SOE_new_central + after_first_inspection +
               Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
               (1|PROVINCE/CITY), data=dta1)

# Model 2: Central SOE-Policy interaction
p2soe2 <- lmer(Environmental_Information_Disclosure ~ SOE_new_central * after_first_inspection +
               Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
               (1|PROVINCE/CITY), data=dta1)

# Model 3: Local SOE main effect
p2soe3 <- lmer(Environmental_Information_Disclosure ~ SOE_new_local + after_first_inspection +
               Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
               (1|PROVINCE/CITY), data=dta1)

# Model 4: Local SOE-Policy interaction
p2soe4 <- lmer(Environmental_Information_Disclosure ~ SOE_new_local * after_first_inspection +
               Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
               (1|PROVINCE/CITY), data=dta1)

# Create Organization & Environment style table for central/local SOE analysis
tab_model(p2soe1, p2soe2, p2soe3, p2soe4,
          title = "Table B5. Central vs Local State-owned Enterprises",
          dv.labels = "Dependent variable:<br>Environmental Information Disclosure",
          pred.labels = c("Central SOE", "Inspection", "Age",
                         "ROA", "Leverage", "Log(Registered Capital)",
                         "ESG Rating", "Local SOE", "Central SOE × Inspection",
                         "Local SOE × Inspection"),
          rm.terms = c("as.factor(IndustryName)"),
          show.aic = TRUE,
          show.loglik = TRUE,
          show.obs = TRUE,
          show.ngroups = TRUE,
          show.re.var = TRUE,
          show.icc = TRUE,
          show.intercept = FALSE, # Hide intercept
          show.p = FALSE,        # Hide p-values column
          show.se = TRUE,        # Show standard errors in parentheses
          show.ci = FALSE,       # Hide confidence intervals
          p.style = "stars",     # Display significance as stars
          p.threshold = c(0.1, 0.05, 0.01),  # Set significance thresholds
          digits = 3,            # 3 decimal places
          digits.re = 2,         # 2 decimal places for random effects
          CSS = oe_css,
          file = "B5_SOE_result_p4_OE_style.html")

# Fix table header alignment
fix_table_header <- function(filename) {
  if (file.exists(filename)) {
    content <- readLines(filename, warn = FALSE)
    # Fix colspan for dependent variable header
    content <- gsub('colspan="2"([^>]*>Dependent variable)', 'colspan="8"\\1', content)
    # Ensure center alignment for the header
    content <- gsub('(colspan="8"[^>]*style="[^"]*)(">Dependent variable)', '\\1 text-align: center;\\2', content)
    writeLines(content, filename)
  }
}

fix_table_header("B5_SOE_result_p4_OE_style.html")


```

## Plm model
### P3 connection (continuous)

```{r industry_classification_11}
# 创建11类行业分类
dta1$industry_type11 <- case_when(
  # 1 Energy
  dta1$IndustryName %in% c("石油和天然气开采业",
                           "石油加工、炼焦及核燃料加工业", "开发辅助活动", 
                           "煤炭开采和洗选业")                                   ~ "Energy",
  
  # 2 Materials
  dta1$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业",
                           "黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业",
                           "黑色金属矿采选业","有色金属矿采选业","非金属矿采选业",
                           "非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业",
                           "木材加工及木、竹、藤、棕、草制品业", "开采辅助活动", "林业",
                           "金属制品业")                                         ~ "Materials",
  
  # 3 Industrials
  dta1$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业",
                           "电气机械及器材制造业","通用设备制造业",
                           "装卸搬运和运输代理业","道路运输业","水上运输业",
                           "航空运输业","铁路运输业","仓储业","其他制造业",
                           "专业技术服务业","其他服务业",
                           "废弃资源综合利用业", "综合", "金属制品、机械和设备修理业",
                           "邮政业", "仪器仪表制造业",
                           # 建筑／环保
                           "房屋建筑业","建筑安装业","土木工程建筑业",
                           "建筑装饰和其他建筑业","生态保护和环境治理业",
                           "公共设施管理业")                                     ~ "Industrials",
  
  # 4 Consumer Discretionary
  dta1$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业",
                           "教育","纺织业","零售业","纺织服装、服饰业",
                           "文教、工美、体育和娱乐用品制造业","文化艺术业",
                           "皮革、毛皮、羽毛及其制品和制鞋业","家具制造业",
                           "机动车、电子产品和日用产品修理业",
                           "汽车制造业")                                         ~ "Consumer Discretionary",
  
  # 5 Consumer Staples
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业",
                           "农业","畜牧业","渔业",
                           "农、林、牧、渔服务业")                               ~ "Consumer Staples",
  
  # 6 Health Care
  dta1$IndustryName %in% c("医药制造业","卫生")                                 ~ "Health Care",
  
  # 7 Financials
  dta1$IndustryName %in% c("货币金融服务","资本市场服务","保险业",
                           "其他金融业","租赁业","商务服务业")                   ~ "Financials",
  
  # 8 Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业",
                           "计算机、通信和其他电子设备制造业",
                           "印刷和记录媒介复制业",
                           "研究和试验发展","科技推广和应用服务业")               ~ "Information Technology",
  
  # 9 Communication Services
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务",
                           "广播、电视、电影和影视录音制作业",
                           "新闻和出版业","互联网和相关服务")                     ~ "Communication Services",
  
  # 10 Utilities
  dta1$IndustryName %in% c("电力、热力生产和供应业",
                           "燃气生产和供应业",
                           "水的生产和供应业")                                   ~ "Utilities",
  
  # 11 Real Estate
  dta1$IndustryName %in% c("房地产业")                            ~ "Real Estate",
  
  TRUE ~ NA_character_
)


# 显示分类结果
cat("11类分类结果:\n")
industry_table <- table(dta1$industry_type11, useNA = "always")
print(industry_table)

# 分析NA值的详细情况
na_data <- dta1 %>% filter(is.na(industry_type11))
cat("\n=== NA值分析 ===\n")
cat("NA观测数:", nrow(na_data), "\n")
cat("NA占总观测数的比例:", round(nrow(na_data)/nrow(dta1)*100, 2), "%\n")
cat("NA涉及的唯一公司数:", length(unique(na_data$Symbol)), "\n")
cat("NA涉及的唯一行业数:", length(unique(na_data$IndustryName)), "\n")

# 显示所有未分类的行业名称
cat("\n未分类的行业名称:\n")
na_industries <- na_data %>%
  group_by(IndustryName) %>%
  summarise(count = n(), .groups = 'drop') %>%
  arrange(desc(count))
print(na_industries)
```

```{r P3 connection (continuous)}
# P3: Political connections (continuous) and policy pressure - Fixed Effects Models
library(plm)

# Model 1: Two-way fixed effects (firm + year)
p3plm1 <- plm(Environmental_Information_Disclosure ~ connection_num + after_first_inspection +
              Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
              as.factor(industry_type11) + as.factor(PROVINCE),
              data=dta1, index=c("Symbol", "EndYear"), model="within", effect="twoways")

# Model 2: Individual fixed effects only
p3plm2 <- plm(Environmental_Information_Disclosure ~ after_first_inspection +
              Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
              as.factor(industry_type11) + as.factor(PROVINCE),
              data=dta1, index=c("Symbol"), model="within")

# Model 3: Connection-Policy interaction
p3plm3 <- plm(Environmental_Information_Disclosure ~ connection_num * after_first_inspection +
              Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
              as.factor(industry_type11) + as.factor(PROVINCE),
              data=dta1, index=c("Symbol"), model="within")


# Create Organization & Environment style table using stargazer
library(stargazer)

# Create stargazer table with AIC and log-Likelihood
stargazer(p3plm1, p3plm2, p3plm3,
          type = "html",
          title = "Table B6. Fixed Effects Models: Political Connections",
          dep.var.labels = "Environmental Information Disclosure",
          covariate.labels = c("Connections", "Inspection",
                              "Age", "ROA", "Leverage",
                              "Log(Registered Capital)", "ESG Rating",
                              "Connections × Inspection"),
          omit = c("as.factor", "Constant"),
          add.lines = list(c("Firm FE", "Yes", "Yes", "Yes"),
                          c("Year FE", "Yes", "No", "No"),
                          c("Industry FE", "Yes", "Yes", "Yes"),
                          c("Province FE", "Yes", "Yes", "Yes"),
                          c("AIC", round(AIC(p3plm1), 1), round(AIC(p3plm2), 1), round(AIC(p3plm3), 1)),
                          c("Log-Likelihood", round(logLik(p3plm1), 1), round(logLik(p3plm2), 1), round(logLik(p3plm3), 1))),
          digits = 3,
          digits.extra = 0,
          out = "B6_plm_p3_OE_style.html")
```

### P4 contral/local connection (continuous)
```{r P4 contral/local connection (continuous)}
# P4: Central and local political connections - Fixed Effects Models
library(plm)

# Model 1: Central connections (two-way FE)
p4plm1 <- plm(Environmental_Information_Disclosure ~ central_connection + after_first_inspection +
              Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
              as.factor(industry_type11) + as.factor(PROVINCE),
              data=dta1, index=c("Symbol", "EndYear"), model="within", effect="twoways")

# Model 2: Central connections interaction
p4plm2 <- plm(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
              Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
              as.factor(industry_type11) + as.factor(PROVINCE),
              data=dta1, index=c("Symbol"), model="within")

# Model 3: Local connections (two-way FE)
p4plm3 <- plm(Environmental_Information_Disclosure ~ local_connection + after_first_inspection +
              Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
              as.factor(industry_type11) + as.factor(PROVINCE),
              data=dta1, index=c("Symbol", "EndYear"), model="within", effect="twoways")

# Model 4: Local connections interaction
p4plm4 <- plm(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
              Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
              as.factor(industry_type11) + as.factor(PROVINCE),
              data=dta1, index=c("Symbol"), model="within")

# Create Organization & Environment style table using stargazer
stargazer(p4plm1, p4plm2, p4plm3, p4plm4,
          type = "html",
          title = "Table B7. Fixed Effects Models: Central vs Local Political Connections",
          dep.var.labels = "Environmental Information Disclosure",
          covariate.labels = c("Central Connections", "Local Connections", "Inspection", "Age",
                              "ROA", "Leverage", "Log(Registered Capital)",
                              "ESG Rating", "Central Connections × Inspection",
                              "Local Connections × Inspection"),
          omit = c("as.factor", "Constant"),
          add.lines = list(c("Firm FE", "Yes", "Yes", "Yes", "Yes"),
                          c("Year FE", "Yes", "No", "Yes", "No"),
                          c("Industry FE", "Yes", "Yes", "Yes", "Yes"),
                          c("Province FE", "Yes", "Yes", "Yes", "Yes"),
                          c("AIC", round(AIC(p4plm1), 1), round(AIC(p4plm2), 1), 
                            round(AIC(p4plm3), 1), round(AIC(p4plm4), 1)),
                          c("Log-Likelihood", round(logLik(p4plm1), 1), round(logLik(p4plm2), 1), 
                            round(logLik(p4plm3), 1), round(logLik(p4plm4), 1))),
          digits = 3,
          digits.extra = 0,
          out = "B7_plm_p4_OE_style.html")
```

## Add industry_type11

### P3 connection (continuous) with industry_type11 controls
```{r}
library(lme4)
library(sjPlot)

# Model 1: Basic connection model with industry controls
p3industry1 <- lmer(Environmental_Information_Disclosure ~ connection_num + after_first_inspection +
                   Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
                   as.factor(industry_type11) + (1|PROVINCE/CITY), data=dta1)

# Model 2: Policy pressure model with industry controls
p3industry2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection +
                   Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
                   as.factor(industry_type11) + (1|PROVINCE/CITY), data=dta1)

# Model 3: Connection-Policy interaction with industry controls
p3industry3 <- lmer(Environmental_Information_Disclosure ~ connection_num * after_first_inspection +
                   Age + ROA + Leverage + RegisterCapital_log + ESG_Rate +
                   as.factor(industry_type11) + (1|PROVINCE/CITY), data=dta1)


# Create Organization & Environment style table for industry analysis
tab_model(p3industry1, p3industry2, p3industry3,
          title = "Table B8. Political Connections with Industry Type Controls",
          dv.labels = "Dependent variable:<br>Environmental Information Disclosure",
          # pred.labels = c("Connections", "Inspection", "Age",
          #                "ROA", "Leverage", "Log(Registered Capital)",
          #                "ESG Rating", "Connections × Inspection"),
          rm.terms = c("as.factor(industry_type11)"),
          show.aic = TRUE,
          show.loglik = TRUE,
          show.obs = TRUE,
          show.ngroups = TRUE,
          show.re.var = TRUE,
          show.icc = TRUE,
          show.intercept = FALSE, # Hide intercept
          show.p = FALSE,        # Hide p-values column
          show.se = TRUE,        # Show standard errors in parentheses
          show.ci = FALSE,       # Hide confidence intervals
          p.style = "stars",     # Display significance as stars
          p.threshold = c(0.1, 0.05, 0.01),  # Set significance thresholds
          digits = 3,            # 3 decimal places
          digits.re = 2,         # 2 decimal places for random effects
          CSS = oe_css,
          file = "B8_industry_result_p3_OE_style.html")

# Fix table header alignment
fix_table_header <- function(filename) {
  if (file.exists(filename)) {
    content <- readLines(filename, warn = FALSE)
    # Fix colspan for dependent variable header
    content <- gsub('colspan="2"([^>]*>Dependent variable)', 'colspan="6"\\1', content)
    # Ensure center alignment for the header
    content <- gsub('(colspan="6"[^>]*style="[^"]*)(">Dependent variable)', '\\1 text-align: center;\\2', content)
    writeLines(content, filename)
  }
}

fix_table_header("B8_industry_result_p3_OE_style.html")
```


### P4 contral/local connection (continuous)
```{r P4 contral/local connection (continuous)}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1

library(lme4)

p4m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + as.factor(industry_type11) + (1|PROVINCE/CITY), data=dta1)

p4m2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + as.factor(industry_type11) + (1|PROVINCE/CITY), data=dta1)

p4m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + as.factor(industry_type11) + (1|PROVINCE/CITY), data=dta1)

p4m4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + as.factor(industry_type11) + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p4m1, p4m2, p4m3, p4m4,
          title = "Table B9. Central vs Local Political Connections with Industry Type Controls",
          dv.labels = "Dependent variable:<br>Environmental Information Disclosure",
          # pred.labels = c("Connections", "Inspection", "Age",
          #                "ROA", "Leverage", "Log(Registered Capital)",
          #                "ESG Rating", "Connections × Inspection"),
          rm.terms = c("as.factor(industry_type11)"),
          show.aic = TRUE,
          show.loglik = TRUE,
          show.obs = TRUE,
          show.ngroups = TRUE,
          show.re.var = TRUE,
          show.icc = TRUE,
          show.intercept = FALSE, # Hide intercept
          show.p = FALSE,        # Hide p-values column
          show.se = TRUE,        # Show standard errors in parentheses
          show.ci = FALSE,       # Hide confidence intervals
          p.style = "stars",     # Display significance as stars
          p.threshold = c(0.1, 0.05, 0.01),  # Set significance thresholds
          digits = 3,            # 3 decimal places
          digits.re = 2,         # 2 decimal places for random effects
          CSS = oe_css,
          file = "B9_central_local_industry_p4_OE_style.html")

# Fix table header alignment
fix_table_header <- function(filename) {
  if (file.exists(filename)) {
    content <- readLines(filename, warn = FALSE)
    # Fix colspan for dependent variable header
    content <- gsub('colspan="2"([^>]*>Dependent variable)', 'colspan="8"\\1', content)
    # Ensure center alignment for the header
    content <- gsub('(colspan="8"[^>]*style="[^"]*)(">Dependent variable)', '\\1 text-align: center;\\2', content)
    writeLines(content, filename)
  }
}

fix_table_header("B9_central_local_industry_p4_OE_style.html")
```


## Main
### P3 connection (continuous)
```{r}
library(lme4)
p3way1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p3way2 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p3way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p3way1, p3way2, p3way3,
          title = "Table B10. Political Connections (Continuous) - Main Results",
          dv.labels = "Dependent variable:<br>Environmental Information Disclosure",
          show.aic = TRUE,
          show.loglik = TRUE,
          show.obs = TRUE,
          show.ngroups = TRUE,
          show.re.var = TRUE,
          show.icc = TRUE,
          show.intercept = FALSE, # Hide intercept
          show.p = FALSE,        # Hide p-values column
          show.se = TRUE,        # Show standard errors in parentheses
          show.ci = FALSE,       # Hide confidence intervals
          p.style = "stars",     # Display significance as stars
          p.threshold = c(0.1, 0.05, 0.01),  # Set significance thresholds
          digits = 3,            # 3 decimal places
          digits.re = 2,         # 2 decimal places for random effects
          CSS = oe_css,
          file = "B10_p3_continuous_main_OE_style.html")

# Fix table header alignment
fix_table_header <- function(filename) {
  if (file.exists(filename)) {
    content <- readLines(filename, warn = FALSE)
    # Fix colspan for dependent variable header
    content <- gsub('colspan="2"([^>]*>Dependent variable)', 'colspan="6"\\1', content)
    # Ensure center alignment for the header
    content <- gsub('(colspan="6"[^>]*style="[^"]*)(">Dependent variable)', '\\1 text-align: center;\\2', content)
    writeLines(content, filename)
  }
}

fix_table_header("B10_p3_continuous_main_OE_style.html")



```


### P4 contral/local connection (continuous)
```{r P4 contral/local connection (continuous)}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1

library(lme4)

p4m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p4m1, p4m2, p4m3, p4m4,
          file = "p4_continuous.html",
          title = "Mixed-Effects Models",
          dv.labels = c("Central: Two Ways", "Central: Individual", "Local: Two Ways", "Local: Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)
```
