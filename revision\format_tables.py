#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to reformat HTML tables to put standard errors in parentheses on the next line
"""

import re
import os
from pathlib import Path

def process_html_table(html_content):
    """
    Process HTML table to combine coefficients and standard errors using regex
    """
    lines = html_content.split('\n')
    new_lines = []
    i = 0

    while i < len(lines):
        line = lines[i]

        # Check if this is the header row with Estimates and std. Error
        if 'Estimates' in line and 'std. Error' in line:
            # Replace header row to show (1), (2), (3) instead of Estimates/std. Error pairs
            new_header = line
            model_num = 1

            # Replace each Estimates/std. Error pair with model number
            while 'Estimates' in new_header and 'std. Error' in new_header:
                # Find and replace the first occurrence
                pattern = r'<td[^>]*>Estimates</td>\s*<td[^>]*>std\. Error</td>'
                replacement = f'<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">({model_num})</td>'
                new_header = re.sub(pattern, replacement, new_header, count=1)
                model_num += 1

            new_lines.append(new_header)

        # Check if this is a data row (has variable name and coefficients)
        elif '<tr>' in line and i + 1 < len(lines):
            # Look for the pattern of a data row
            next_line = lines[i + 1] if i + 1 < len(lines) else ""

            # Check if this row contains coefficient data
            if ('sup>' in next_line or re.search(r'>\d+\.\d+', next_line) or re.search(r'>&#45;\d+\.\d+', next_line)):
                # This is a data row, process it
                var_name_match = re.search(r'<td[^>]*>([^<]+)</td>', next_line)
                if var_name_match:
                    var_name = var_name_match.group(1)

                    # Extract all coefficient and std error pairs from the row
                    # Pattern to match coefficient and std error cells
                    cell_pattern = r'<td[^>]*>([^<]*(?:<sup>[^<]*</sup>)?[^<]*)</td>'
                    cells = re.findall(cell_pattern, next_line)

                    if len(cells) > 1:
                        # First cell is variable name, rest are coefficient/std error pairs
                        data_cells = cells[1:]

                        # Create coefficient row
                        coef_row = f'<tr>\n<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">{var_name}</td>'

                        # Create std error row
                        std_row = '<tr>\n<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; "></td>'

                        # Process coefficient/std error pairs
                        for j in range(0, len(data_cells), 2):
                            if j + 1 < len(data_cells):
                                coef = data_cells[j].strip()
                                std_err = data_cells[j + 1].strip()

                                # Add coefficient cell
                                coef_row += f'\n<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">{coef}</td>'

                                # Add std error cell with parentheses
                                if std_err:
                                    std_row += f'\n<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">({std_err})</td>'
                                else:
                                    std_row += '\n<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>'

                        coef_row += '\n</tr>'
                        std_row += '\n</tr>'

                        new_lines.append(line)  # <tr>
                        new_lines.append(coef_row)
                        new_lines.append(std_row)

                        # Skip the original data row
                        i += 2
                        continue

        new_lines.append(line)
        i += 1

    return '\n'.join(new_lines)

def process_file(file_path):
    """Process a single HTML file"""
    print(f"Processing {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Process the content
    new_content = process_html_table(content)
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Completed {file_path}")

def main():
    """Main function to process all HTML files in revision folder"""
    revision_dir = Path('.')
    
    # Find all HTML files
    html_files = list(revision_dir.glob('*.html'))
    
    if not html_files:
        print("No HTML files found in revision directory")
        return
    
    print(f"Found {len(html_files)} HTML files to process")
    
    for html_file in html_files:
        try:
            process_file(html_file)
        except Exception as e:
            print(f"Error processing {html_file}: {e}")

if __name__ == "__main__":
    main()
