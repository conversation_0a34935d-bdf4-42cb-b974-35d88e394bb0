#!/usr/bin/env python3
"""
全面修复HTML表格结构
"""

import os
import re

def fix_table_comprehensive(filename):
    print(f"全面修复文件: {filename}")
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建备份
    backup_name = filename + '.backup_comprehensive'
    with open(backup_name, 'w', encoding='utf-8') as f:
        f.write(content)
    
    lines = content.split('\n')
    fixed_lines = []
    
    # 确定列数
    if 'B3_' in filename:
        data_cols = 5  # Predictors + 4 data columns
        header_colspan = 4
    else:
        data_cols = 4  # Predictors + 3 data columns  
        header_colspan = 3
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # 修复表头
        if '<th' in line and 'colspan=' in line:
            line = re.sub(r'colspan="\d+"', f'colspan="{header_colspan}"', line)
        
        # 修复Random Effects标题行
        if 'Random Effects' in line:
            fixed_lines.append(f'<tr>')
            fixed_lines.append(f'<td colspan="{data_cols}" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>')
            fixed_lines.append('</tr>')
            i += 1
            continue
        
        # 处理统计数据行
        if any(stat in line for stat in ['σ', 'τ', 'ICC', 'Observations', 'Marginal R', 'AIC', 'log-Likelihood']):
            # 这是统计数据的标签行
            if '<td' in line:
                # 提取标签文本
                label_match = re.search(r'>([^<]+)<', line)
                if label_match:
                    label = label_match.group(1)
                    fixed_lines.append('<tr>')
                    fixed_lines.append(f'<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">{label}</td>')
                    
                    # 查找对应的数据值
                    data_values = []
                    j = i + 1
                    while j < len(lines) and len(data_values) < header_colspan:
                        next_line = lines[j].strip()
                        if '<td' in next_line and 'colspan=' in next_line:
                            # 提取数据值
                            value_match = re.search(r'>([^<]+)<', next_line)
                            if value_match:
                                data_values.append(value_match.group(1))
                        elif '<tr>' in next_line or any(stat in next_line for stat in ['σ', 'τ', 'ICC', 'Observations', 'Marginal R', 'AIC', 'log-Likelihood']):
                            break
                        j += 1
                    
                    # 添加数据单元格
                    for value in data_values:
                        fixed_lines.append(f'<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;">{value}</td>')
                    
                    fixed_lines.append('</tr>')
                    i = j
                    continue
        
        # 跳过已经处理过的行
        if any(keyword in line for keyword in ['</tr>', 'colspan=']) and 'Random Effects' not in line:
            i += 1
            continue
        
        # 普通行处理
        if line and not line.startswith('</tr>'):
            fixed_lines.append(line)
        
        i += 1
    
    # 写入修复后的内容
    with open(filename, 'w', encoding='utf-8') as f:
        f.write('\n'.join(fixed_lines))
    
    print(f"全面修复完成: {filename}")

def main():
    # 只修复一个文件作为测试
    test_file = 'B2_p3_extended_inspection_OE_style.html'
    if os.path.exists(test_file):
        fix_table_comprehensive(test_file)
    else:
        print(f"文件 {test_file} 不存在")

if __name__ == "__main__":
    main()
