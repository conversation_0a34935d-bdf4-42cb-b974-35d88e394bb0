# 快速验证Age变量
load("dta1_20240903.RData")
library(dplyr)
library(lubridate)

# 重新计算Age
dta1 <- dta1 %>%
  mutate(
    EndYear = ymd(EndYear),
    Year = year(EndYear),
    Age = Year - EstablishYear
  )

# 选择一个公司查看Age变化
sample_company <- dta1 %>%
  group_by(Symbol) %>%
  summarise(count = n()) %>%
  filter(count > 5) %>%
  slice(1) %>%
  pull(Symbol)

cat("样本公司:", sample_company, "\n")

# 查看该公司的Age变化
company_data <- dta1 %>%
  filter(Symbol == sample_company) %>%
  select(Symbol, Year, EstablishYear, Age) %>%
  arrange(Year)

print(company_data)

# 检查Age是否正确递增
if(nrow(company_data) > 1) {
  age_diff <- diff(company_data$Age)
  year_diff <- diff(company_data$Year)
  cat("年份差值:", year_diff, "\n")
  cat("Age差值:", age_diff, "\n")
  cat("Age是否正确递增:", all(age_diff == year_diff), "\n")
}
