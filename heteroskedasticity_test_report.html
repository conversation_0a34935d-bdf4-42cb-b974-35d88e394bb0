<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heteroskedasticity Test Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background-color: #f4f4f4;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-result {
            background-color: #e8f4fd;
            padding: 15px;
            border-left: 4px solid #2196F3;
            margin: 15px 0;
        }
        .significant {
            background-color: #ffebee;
            border-left-color: #f44336;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .comparison-table th {
            background-color: #f2f2f2;
        }
        .highlight {
            background-color: #fff3cd;
            font-weight: bold;
        }
        .conclusion {
            background-color: #d4edda;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Heteroskedasticity Test Report</h1>
        <p><strong>Model:</strong> Environmental Information Disclosure Regression</p>
        <p><strong>Date:</strong> July 31, 2025</p>
        <p><strong>Sample Size:</strong> 10,777 observations</p>
    </div>

    <h2>1. Heteroskedasticity Test Results</h2>
    
    <div class="test-result significant">
        <h3>Method 1: Studentized Breusch-Pagan Test</h3>
        <ul>
            <li><strong>Test Statistic (BP):</strong> 930.58</li>
            <li><strong>Degrees of Freedom:</strong> 8</li>
            <li><strong>P-value:</strong> < 2.2e-16</li>
            <li><strong>Result:</strong> <span style="color: red;">Significant heteroskedasticity detected</span></li>
        </ul>
    </div>

    <div class="test-result significant">
        <h3>Method 2: Non-constant Variance Score Test</h3>
        <ul>
            <li><strong>Chi-square Statistic:</strong> 2185.746</li>
            <li><strong>Degrees of Freedom:</strong> 1</li>
            <li><strong>P-value:</strong> < 2.22e-16</li>
            <li><strong>Result:</strong> <span style="color: red;">Significant heteroskedasticity detected</span></li>
        </ul>
    </div>

    <div class="test-result significant">
        <h3>Method 3: Manual White Test</h3>
        <ul>
            <li><strong>White Statistic:</strong> 940.69</li>
            <li><strong>Degrees of Freedom:</strong> 2</li>
            <li><strong>P-value:</strong> ≈ 0 (< 2.2e-16)</li>
            <li><strong>Result:</strong> <span style="color: red;">Significant heteroskedasticity detected</span></li>
        </ul>
    </div>

    <h2>2. Standard Error Comparison: OLS vs. White Robust</h2>
    
    <table class="comparison-table">
        <thead>
            <tr>
                <th>Variable</th>
                <th>OLS Std. Error</th>
                <th>White Robust Std. Error</th>
                <th>Change (%)</th>
                <th>OLS p-value</th>
                <th>Robust p-value</th>
                <th>Significance Change</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Intercept</td>
                <td>2.193</td>
                <td>2.455</td>
                <td>+12%</td>
                <td>< 2e-16</td>
                <td>< 2.2e-16</td>
                <td>No change</td>
            </tr>
            <tr>
                <td>after_first_inspection</td>
                <td>0.281</td>
                <td>0.253</td>
                <td>-10%</td>
                <td>< 2e-16</td>
                <td>< 2.2e-16</td>
                <td>No change</td>
            </tr>
            <tr>
                <td>connection_num</td>
                <td>0.058</td>
                <td>0.042</td>
                <td>-28%</td>
                <td>0.000153</td>
                <td>1.682e-07</td>
                <td>No change</td>
            </tr>
            <tr>
                <td>ESG_Rate</td>
                <td>0.014</td>
                <td>0.014</td>
                <td>+3%</td>
                <td>< 2e-16</td>
                <td>< 2.2e-16</td>
                <td>No change</td>
            </tr>
            <tr>
                <td>ROA</td>
                <td>0.103</td>
                <td>0.037</td>
                <td>-64%</td>
                <td>0.628</td>
                <td>0.181</td>
                <td>No change</td>
            </tr>
            <tr class="highlight">
                <td>Leverage</td>
                <td>0.0017</td>
                <td>0.0006</td>
                <td>-65%</td>
                <td>0.420</td>
                <td>0.018</td>
                <td><strong>Not Sig. → Significant</strong></td>
            </tr>
            <tr>
                <td>RegisterCapital_log</td>
                <td>0.105</td>
                <td>0.121</td>
                <td>+15%</td>
                <td>< 2e-16</td>
                <td>< 2.2e-16</td>
                <td>No change</td>
            </tr>
            <tr>
                <td>Interaction Term</td>
                <td>0.084</td>
                <td>0.084</td>
                <td>0%</td>
                <td>0.020</td>
                <td>0.021</td>
                <td>No change</td>
            </tr>
        </tbody>
    </table>

    <h2>3. Key Findings</h2>
    
    <div class="test-result">
        <h3>Heteroskedasticity Detection</h3>
        <p>All three heteroskedasticity tests consistently indicate the presence of <strong>significant heteroskedasticity</strong> in the model residuals (all p-values < 0.001).</p>
    </div>

    <div class="test-result">
        <h3>Impact on Inference</h3>
        <ul>
            <li><strong>Most variables:</strong> Robust standard errors do not change statistical significance</li>
            <li><strong>Leverage variable:</strong> Changes from non-significant (p=0.420) to significant (p=0.018)</li>
            <li><strong>Standard error changes:</strong> Range from -65% to +15%, indicating substantial heteroskedasticity effects</li>
        </ul>
    </div>

    <div class="conclusion">
        <h2>4. Conclusions and Recommendations</h2>
        <ol>
            <li><strong>Heteroskedasticity Confirmed:</strong> The model exhibits significant heteroskedasticity, violating the homoskedasticity assumption of OLS regression.</li>
            <li><strong>Use Robust Standard Errors:</strong> White heteroskedasticity-consistent (HC1) standard errors should be reported for valid statistical inference.</li>
            <li><strong>Key Finding:</strong> The Leverage variable becomes statistically significant when using robust standard errors, suggesting its importance was masked by heteroskedasticity.</li>
            <li><strong>Model Robustness:</strong> Core findings for main variables (after_first_inspection, ESG_Rate, RegisterCapital_log) remain robust to heteroskedasticity correction.</li>
            <li><strong>Reporting:</strong> Future analyses should routinely employ heteroskedasticity-robust standard errors given the clear evidence of non-constant variance.</li>
        </ol>
    </div>

    <div style="margin-top: 30px; font-size: 0.9em; color: #666;">
        <p><strong>Note:</strong> This report is based on White heteroskedasticity-consistent standard errors (HC1 estimator) applied to the Environmental Information Disclosure regression model.</p>
    </div>
</body>
</html>
