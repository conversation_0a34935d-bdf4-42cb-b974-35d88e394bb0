#reading the file

: dta1=dta1_20240903.RData

```{r load_data}
# Load the data file
load("dta1_20240903.RData")
# You can add a quick check to confirm the data loaded correctly

```

#model 1
```{r P3 connection (continuous)}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(lme4)
library(lmerTest)
p3way1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + RegisterCapital_log + ESG_Rate + (1 | PROVINCE/CITY), data=dta1)


p3way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate  +  RegisterCapital_log + ROA + Leverage +ESG_Rate + (1 | PROVINCE/CITY), data=dta1)

p3way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate  +  RegisterCapital_log + ROA + Leverage + ESG_Rate + (1 | PROVINCE/CITY), data=dta1)

library(texreg)
screenreg(list(p3way1, p3way2, p3way3),
          custom.model.names = c("Model 1", "Model 2", "Model 3"),
          stars = c(0.05, 0.01, 0.001),
          title = "Mixed-Effects Models for Political Connection"
)


```

#model 2
```{r P4 contral/local connection (continuous)}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1   

library(lme4)
library(lmerTest)

p4m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate  + RegisterCapital_log +  ROA + Leverage + (1 | PROVINCE/CITY), data=dta1)

p4m2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate  +  RegisterCapital_log + ROA + Leverage + (1 | PROVINCE/CITY), data=dta1)

p4m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate  + RegisterCapital_log +  ROA + Leverage + (1 | PROVINCE/CITY), data=dta1)

p4m4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate  +  RegisterCapital_log + ROA + Leverage + (1 | PROVINCE/CITY), data=dta1)

library(texreg)
screenreg(list(p4m1, p4m2, p4m3, p4m4),
          custom.model.names = c("Central 1", "Central 2", "Local 1", "Local 2"),
          stars = c(0.05, 0.01, 0.001),
          title = "Mixed-Effects Models for Central vs Local Political Connection"
)
```

#correlation matrix of key variables
```{r}
# Create a correlation matrix of key variables
library(corrplot)
library(ggplot2)
library(reshape2)

# Select variables for correlation analysis
corr_vars <- dta1[, c("Environmental_Information_Disclosure", "connection_num", 
                      "central_connection", "local_connection", "after_first_inspection",
                      "Age", "ROA", "ESG_Rate", "Leverage", "RegisterCapital_log")]

# 创建更易读的变量名
var_names <- c("Environmental\nDisclosure", "Connection\nNumber", 
               "Central\nConnection", "Local\nConnection", "After First\nInspection",
               "Age", "ROA", "ESG Rate", "Leverage", "Register\nCapital (log)")

# 设置列名以便在图中使用
colnames(corr_vars) <- var_names

# Calculate correlation matrix
cor_matrix <- cor(corr_vars, use = "pairwise.complete.obs")

# Print correlation matrix with scientific formatting
print(round(cor_matrix, 3), quote = FALSE)

# Create publication-quality correlation plot
# Calculate p-values
library(Hmisc)
cor_test <- rcorr(as.matrix(corr_vars))

# Melt the correlation matrix for ggplot
melted_cor <- melt(cor_matrix)

# Add p-values to the melted dataframe
melted_cor$p_value <- melt(cor_test$P)$value

# Create the ggplot visualization with grayscale color scheme
p <- ggplot(data = melted_cor, aes(x = Var1, y = Var2, fill = value)) +
  geom_tile(color = "white") +
  geom_text(aes(label = ifelse(p_value < 0.05, 
                              sprintf("%.2f*", value), 
                              sprintf("%.2f", value))), 
            size = 3) +
  scale_fill_gradient2(low = "gray20", high = "gray80", mid = "white", 
                       midpoint = 0, limit = c(-1,1), name = "Correlation") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, vjust = 1, hjust = 1, size = 8),
        axis.text.y = element_text(size = 8),
        axis.title = element_blank(),
        panel.grid.major = element_blank(),
        legend.position = "bottom") +
  coord_fixed() +
  labs(caption = "* indicates p < 0.05")

# Display the plot
print(p)

# Save as SVG for publication (vector format, maintains quality at any size)
ggsave("d:/Research/ESG/ESG_China/R-ESG China/correlation_matrix_gray.svg", 
       plot = p, width = 8, height = 7, dpi = 300)
```



# After loading the data
```{r sample_description}
# Calculate number of unique firms and total firm-year observations
n_firms <- length(unique(dta1$Symbol))
n_observations <- nrow(dta1)

# Create a publication-quality table for sample description
library(kableExtra)
library(dplyr)

# Create sample description dataframe
sample_df <- data.frame(
  Description = c("Number of unique firms", "Number of firm-year observations"),
  Count = c(n_firms, n_observations)
)

# Print the table in a clean format suitable for Science journal
kable(sample_df, format = "html", col.names = c("Description", "Count"), 
      align = c("l", "r"), caption = "Sample Composition") %>%
  kable_styling(bootstrap_options = c("striped", "condensed"), 
                full_width = FALSE, position = "left",
                font_size = 12) %>%
  row_spec(0, bold = TRUE)

# Additional sample information (optional)
# Time period
year_range <- range(dta1$EndYear, na.rm = TRUE)
cat(sprintf("The sample covers %d firms over the period %d to %d, resulting in %d firm-year observations.",
            n_firms, year_range[1], year_range[2], n_observations))
```



# Multilevel models
```{r multilevel_models}
library(lme4)
library(lmerTest)
library(texreg)  # For handling mixed models in tables

# Create multilevel models for political connections
p3mix1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + 
               RegisterCapital_log + (1 | PROVINCE), data = dta1)

p3mix2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | PROVINCE), data = dta1)

p3mix3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | PROVINCE), data = dta1)

# Use texreg to create output for mixed models
screenreg(list(p3mix1, p3mix2, p3mix3), 
          custom.model.names = c("Model 1", "Model 2", "Model 3"),
          stars = c(0.05, 0.01, 0.001),
          custom.coef.names = c("Intercept", "Age", "Connection Number", "ROA", "ESG Rate", "Leverage", 
                               "Register Capital (log)", "After First Inspection", 
                               "Connection Number × After First Inspection"),
          title = "Multilevel Models for Political Connection Effects")

# Add multilevel models for central/local connections
p4mix1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | PROVINCE), data = dta1)

p4mix2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | PROVINCE), data = dta1)

p4mix3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | PROVINCE), data = dta1)

p4mix4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | PROVINCE), data = dta1)

# Use texreg for central/local connection models
screenreg(list(p4mix1, p4mix2, p4mix3, p4mix4), 
          custom.model.names = c("Central 1", "Central 2", "Local 1", "Local 2"),
          stars = c(0.05, 0.01, 0.001),
          title = "Multilevel Models for Central vs Local Political Connection Effects")

# If you want HTML output for publication, use htmlreg instead
htmlreg(list(p3mix1, p3mix2, p3mix3), file = "d:/Research/ESG/ESG_China/R-ESG China/multilevel_models_p1.html")
htmlreg(list(p4mix1, p4mix2, p4mix3, p4mix4), file = "d:/Research/ESG/ESG_China/R-ESG China/multilevel_models_p2.html")


```