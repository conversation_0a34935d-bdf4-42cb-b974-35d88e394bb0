
## Compare the Effect Sizes of Central and Local Connection within One Model (Del)
P5 contral + local connection (continuous)
Table B1. Compare the Effect Sizes of Central and Local Connection within One Model

```{r P5 contral + local connection (continuous)}

library(lme4)

p5m2 <- lmer(Environmental_Information_Disclosure ~ central_connection + local_connection + ESG_Rate + Age + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p5m4 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + local_connection * after_first_inspection + ESG_Rate + Age + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p5m2, p5m4,
          file = "B1.html",
          title = "Mixed-Effects Models",
          dv.labels = c("(1)", "(2)"),
          show.re.var = TRUE,
          show.icc = TRUE)
```



```{r P5 contral + local connection (continuous) province}

library(lme4)

p5m2 <- lmer(Environmental_Information_Disclosure ~ central_connection + local_connection + ESG_Rate + Age + RegisterCapital_log + ROA + Leverage + (1|PROVINCE), data=dta1)

p5m4 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + local_connection * after_first_inspection + ESG_Rate + Age + RegisterCapital_log + ROA + Leverage + (1|PROVINCE), data=dta1)

library(sjPlot)
tab_model(p5m2, p5m4,
          file = "B1_province.html",
          title = "Mixed-Effects Models",
          dv.labels = c("(1)", "(2)"),
          show.re.var = TRUE,
          show.icc = TRUE)
```



## State-owned Enterprises
### P3 SOE new dataset
Table B4. State-owned Enterprises
We labelled firms where the actual controllers are state-owned institutions or state-owned enterprises as politically connected. 
```{r P3 SOE new dataset 1}
#P3

library(lme4)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + SOE_new + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)

p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * SOE_new + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "B4_SOE_result_p1.html",
          title = "Mixed-Effects Models",
          show.re.var = TRUE,
          show.icc = TRUE)

```


### P4 central/local SEO Level
```{r P4 central/local SEO level 0}
library(lme4)
p2m1 <- lmer(Environmental_Information_Disclosure ~ SOE_new_central + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m2 <- lmer(Environmental_Information_Disclosure ~ SOE_new_central * after_first_inspection + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ SOE_new_local + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ Age + SOE_new_local * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "B5_SOE_result_p2.html",
          title = "Mixed-Effects Models",
          show.re.var = TRUE,
          show.icc = TRUE)
```

### P3 SOI
```{r P3 SOE new dataset}
#P3

library(lme4)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + SOI + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * SOI + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "SOI_result_p3.html",
          title = "Mixed-Effects Models",
          show.re.var = TRUE,
          show.icc = TRUE)

```

### P4 central/local SOI Level
```{r P4 central/local SEO level 1}
#p4

library(lme4)
p2m1 <- lmer(Environmental_Information_Disclosure ~ SOI_central + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m2 <- lmer(Environmental_Information_Disclosure ~ SOI_central * after_first_inspection + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ SOI_local + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ Age + SOI_local * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "SOI_result_p4.html",
          title = "Mixed-Effects Models",
          show.re.var = TRUE,
          show.icc = TRUE)
```
 

## GMM
```{r}
library(dplyr)

# 假设dta1是你的数据框
# 对数据框按Symbol和EndYear排序，确保滞后计算正确
dta1 <- dta1 %>%
  arrange(Symbol, EndYear)

# 计算after_first_inspection的滞后项，并添加为新列after_first_inspection_lag
dta1 <- dta1 %>%
  mutate(after_first_inspection_lag = lag(after_first_inspection))
```

### P3 GMM
```{r P3 GMM}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure

# p3 model 1

library(lme4)
library(clubSandwich)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection_lag * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

# Cluster-robust standard errors
c_p1way1 <- coef_test(p1way1, vcov = "CR2", cluster = dta1$PROVINCE)
c_p1way2 <- coef_test(p1way2, vcov = "CR2", cluster = dta1$PROVINCE)
c_p1way3 <- coef_test(p1way3, vcov = "CR2", cluster = dta1$PROVINCE)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "gmm_p3.html",
          title = "Mixed-Effects Models (P3 GMM)",
          dv.labels = c("Year", "Individual", "Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)


```

### P4 GMM
```{r P4 GMM}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1

library(lme4)
library(clubSandwich)

p2m2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

# Cluster-robust standard errors
c_p2m1 <- coef_test(p2m1, vcov = "CR2", cluster = dta1$PROVINCE)
c_p2m2 <- coef_test(p2m2, vcov = "CR2", cluster = dta1$PROVINCE)
c_p2m3 <- coef_test(p2m3, vcov = "CR2", cluster = dta1$PROVINCE)
c_p2m4 <- coef_test(p2m4, vcov = "CR2", cluster = dta1$PROVINCE)

library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "gmm_p4.html",
          title = "Mixed-Effects Models (P4 GMM)",
          dv.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)
```


## Selection bias

### P3 Selection bias
```{r P3 Selection bias}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(plm)
p1way1_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + connection_num + ROA + Leverage + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)



p1way2_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    after_first_inspection + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


p1way3_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + after_first_inspection + connection_num + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


library(stargazer)
stargazer(p1way1_binary, p1way2_binary, p1way3_binary,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)


```

### P4 Selection bias
```{r P4 contral/local connection (continuous) Selection bias}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1   

library(plm)

p2m2_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    central_connection + after_first_inspection + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


p2m1_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + central_connection + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE) + ROA + Leverage,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)


p2m3_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + local_connection + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE) + ROA + Leverage,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)


p2m4_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    local_connection + after_first_inspection + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)



library(stargazer)
stargazer(p2m1_binary, p2m2_binary, p2m3_binary,p2m4_binary,
          type="text", 
          column.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```


## Selection bias (lag)
```{r}
library(dplyr)

# 假设dta1是你的数据框
# 对数据框按Symbol和EndYear排序，确保滞后计算正确
dta1 <- dta1 %>%
  arrange(Symbol, EndYear)

# 计算after_first_inspection的滞后项，并添加为新列after_first_inspection_lag
dta1 <- dta1 %>%
  mutate(after_first_inspection_lag = lag(after_first_inspection))
```

### P3 Selection bias (lag)
```{r P3 Selection bias (lag)}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(plm)
p1way1_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + connection_num + ROA + ESG_Rate + Leverage + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ESG_Rate,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)



p1way2_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    after_first_inspection_lag + ESG_Rate + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


p1way3_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + after_first_inspection_lag * connection_num + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


library(stargazer)
stargazer(p1way1_binary, p1way2_binary, p1way3_binary,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)


```

### P4 Selection bias (lag)
```{r P4 contral/local connection (continuous) Selection bias (lag)}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1   

library(plm)

p2m2_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    central_connection * after_first_inspection_lag + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


p2m1_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + central_connection + ESG_Rate + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE) + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)


p2m3_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + local_connection + ESG_Rate + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE) + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)


p2m4_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    local_connection * after_first_inspection_lag + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)



library(stargazer)
stargazer(p2m1_binary, p2m2_binary, p2m3_binary,p2m4_binary,
          type="text", 
          column.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```



## lag
### lag t-1 data
```{r}
if("package:plm" %in% search()) {
  detach("package:plm", unload = TRUE)
}
library(dplyr)

# 假设dta1是你的数据框
# 对数据框按Symbol和EndYear排序，确保滞后计算正确
# 计算after_first_inspection的滞后项，并添加为新列after_first_inspection_lag

dta1 <- dta1 %>%
  arrange(Symbol, EndYear) %>%
  mutate(after_first_inspection_lag = lag(after_first_inspection, n = 2, default = NA))

```

### P3 lag t-1
```{r P3 connection (continuous) lag}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(lme4)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection_lag * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "p3_lag.html",
          title = "Mixed-Effects Models",
          dv.labels = c("Year", "Individual", "Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)


```
### P4 lag t-1
```{r P4 contral/local connection (continuous) lag}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1

library(lme4)

p2m2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)


library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "p4_lag.html",
          title = "Mixed-Effects Models",
          dv.labels = c("Central: Two Ways", "Central: Individual", "Local: Two Ways", "Local: Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)
```


## Cluster SE at province level
### Cluster SE at province level P3
```{r P3 connection (continuous) Cluster}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure

# p3 model 1

library(lme4)
p3way1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p3way2 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p3way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

# Cluster-robust standard errors using clubSandwich package
library(clubSandwich)
c_p3m1 = coef_test(p3way1, vcov = "CR2", cluster = dta1$PROVINCE)
c_p3m2 = coef_test(p3way2, vcov = "CR2", cluster = dta1$PROVINCE)
c_p3m3 = coef_test(p3way3, vcov = "CR2", cluster = dta1$PROVINCE)



library(sjPlot)
tab_model(p3way1, p3way2, p3way3,
          file = "P3_Cluster_SE.html",
          title = "Mixed-Effects Models with Clustered SE",
          #dv.labels = c("Year", "Individual", "Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)

```

### Cluster SE at province level P4
```{r P4 contral/local connection (continuous) Cluster}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1

library(lme4)

p4m2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)


# Cluster-robust standard errors using clubSandwich package
library(clubSandwich)
c_p4m1 = coef_test(p4m2, vcov = "CR2", cluster = dta1$PROVINCE)
c_p4m2 = coef_test(p4m1, vcov = "CR2", cluster = dta1$PROVINCE)
c_p4m3 = coef_test(p4m4, vcov = "CR2", cluster = dta1$PROVINCE)
c_p4m4 = coef_test(p4m3, vcov = "CR2", cluster = dta1$PROVINCE)



library(sjPlot)
tab_model(p4m1, p4m2, p4m3, p4m4,
          file = "P4_Cluster_SE.html",
          title = "Mixed-Effects Models with Clustered SE",
          dv.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)

```


### B8
Effects of Central vs. Local Political Connections and Regulatory Pressure on ESG Information Disclosure
```{r}
library(lme4)
library(lmerTest)
p3way1 <- lmer(ESG_Information_Disclosure ~ Age + connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + ESG_Rate + (1|PROVINCE/CITY), data=dta2)

p3way2 <- lmer(ESG_Information_Disclosure ~ Age + after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage +ESG_Rate + (1|PROVINCE/CITY), data=dta2)

p3way3 <- lmer(ESG_Information_Disclosure ~ Age + connection_num * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage +ESG_Rate + (1|PROVINCE/CITY), data=dta2)

# 使用sjPlot生成表格，输出HTML格式
library(sjPlot)
tab_model(p3way1, p3way2, p3way3,
          #title = "Final Multilevel Models Results",
          dv.labels = "ESG Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE,
          file = "B8.html"
)

```


## E -> ESG
E这个指标换成ESG这三个的指标

```{r}
library(readxl)
library(dplyr)

# 使用na参数指定如何处理#N/A值，并使用col_types参数指定列类型
bloomberg_data <- read_excel("esg_bloomberg.xlsx", 
                            sheet = "Data", 
                            col_names = FALSE,
                            na = c("", "NA", "#N/A"), # 将#N/A识别为NA值
                            col_types = "text") # 将所有列都作为文本读入，避免类型转换问题

bloomberg_data = bloomberg_data[-1,]
names(bloomberg_data) = c("stockID", "type", "2011", "2012", "2013", "2014", "2015", "2016", "2017", "2018", "2019", "2020", "2021")

# 将年份列转换为数值型
year_cols <- c("2011", "2012", "2013", "2014", "2015", "2016", "2017", "2018", "2019", "2020", "2021")
bloomberg_data[year_cols] <- lapply(bloomberg_data[year_cols], function(x) as.numeric(x))

# melt bloomberg_data
library(data.table)
bloomberg_data_long = melt(setDT(bloomberg_data), id.vars = c("stockID","type"), variable.name = "year")

library(stringr)
bloomberg_data_long$stockID <- str_replace(bloomberg_data_long$stockID, " CH Equity", "")
names(bloomberg_data_long) = c("Symbol", "Type", "EndYear", "BloobergESGValue")

## merge
bloomberg_data_long$EndYear = as.character(bloomberg_data_long$EndYear)
bloomberg_data_long$EndYear <- as.Date(paste(bloomberg_data_long$EndYear, "-12-31", sep = ""))
bloomberg_data_long = bloomberg_data_long[bloomberg_data_long$Type=="ESG_DISCLOSURE_SCORE", ]

# 确保BloobergESGValue是数值型
bloomberg_data_long$BloobergESGValue <- as.numeric(bloomberg_data_long$BloobergESGValue)

dta2 = left_join(bloomberg_data_long, dta1, by = c("Symbol", "EndYear"))

missing_esg = dta1[!dta1$Symbol %in% dta2$Symbol ,]  # 没有ESG分数的公司

colnames(dta2)[colnames(dta2) == "BloobergESGValue"] <- "ESG_Information_Disclosure"
dta2 <- subset(dta2, select = -c(Type.x, Type.y))

# Note: Add robustness tests using ESG_Information_Disclosure as dependent variable
# You can replicate the above models using dta2 and ESG_Information_Disclosure
# Example:
# p3way1_esg <- plm(ESG_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage +
#                   as.factor(IndustryName) + as.factor(PROVINCE) + RegisterCapital_log,
#                   data=dta2, index=c("Symbol", "EndYear"), model="within", effect="twoways")
#
# esg_table <- create_OE_table(
#   models = list("ESG Model" = p3way1_esg),
#   title = "Table B8. Robustness Check: ESG Information Disclosure",
#   output_format = "gt",
#   filename = "B8_ESG_robustness_OE_style.html"
# )

```



