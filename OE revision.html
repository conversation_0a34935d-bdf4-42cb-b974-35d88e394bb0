<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.39">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>oe-revision</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
/* CSS for syntax highlighting */
pre > code.sourceCode { white-space: pre; position: relative; }
pre > code.sourceCode > span { line-height: 1.25; }
pre > code.sourceCode > span:empty { height: 1.2em; }
.sourceCode { overflow: visible; }
code.sourceCode > span { color: inherit; text-decoration: inherit; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
pre > code.sourceCode { white-space: pre-wrap; }
pre > code.sourceCode > span { display: inline-block; text-indent: -5em; padding-left: 5em; }
}
pre.numberSource code
  { counter-reset: source-line 0; }
pre.numberSource code > span
  { position: relative; left: -4em; counter-increment: source-line; }
pre.numberSource code > span > a:first-child::before
  { content: counter(source-line);
    position: relative; left: -1em; text-align: right; vertical-align: baseline;
    border: none; display: inline-block;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
  }
pre.numberSource { margin-left: 3em;  padding-left: 4px; }
div.sourceCode
  {   }
@media screen {
pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
}
</style>


<script src="OE revision_files/libs/clipboard/clipboard.min.js"></script>
<script src="OE revision_files/libs/quarto-html/quarto.js"></script>
<script src="OE revision_files/libs/quarto-html/popper.min.js"></script>
<script src="OE revision_files/libs/quarto-html/tippy.umd.min.js"></script>
<script src="OE revision_files/libs/quarto-html/anchor.min.js"></script>
<link href="OE revision_files/libs/quarto-html/tippy.css" rel="stylesheet">
<link href="OE revision_files/libs/quarto-html/quarto-syntax-highlighting-e26003cea8cd680ca0c55a263523d882.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="OE revision_files/libs/bootstrap/bootstrap.min.js"></script>
<link href="OE revision_files/libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="OE revision_files/libs/bootstrap/bootstrap-8a79a254b8e706d3c925cde0a310d4f0.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">
<script src="OE revision_files/libs/kePrint-0.0.1/kePrint.js"></script>

<link href="OE revision_files/libs/lightable-0.0.1/lightable.css" rel="stylesheet">



</head>

<body class="fullcontent">

<div id="quarto-content" class="page-columns page-rows-contents page-layout-article">

<main class="content" id="quarto-document-content"><header id="title-block-header" class="quarto-title-block"></header>




<dl>
<dt>#reading the file</dt>
<dd>
<p>dta1=dta1_20240903.RData</p>
</dd>
</dl>
<div class="cell">
<div class="sourceCode cell-code" id="cb1"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Load the data file</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="fu">load</span>(<span class="st">"dta1_20240903.RData"</span>)</span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a><span class="co"># You can add a quick check to confirm the data loaded correctly</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</div>
<p>#model 1</p>
<div class="cell">
<div class="sourceCode cell-code" id="cb2"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co">#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a><span class="co"># p3 model 1</span></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>Loading required package: Matrix</code></pre>
</div>
<div class="sourceCode cell-code" id="cb4"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lmerTest)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'lmerTest'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following object is masked from 'package:lme4':

    lmer</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following object is masked from 'package:stats':

    step</code></pre>
</div>
<div class="sourceCode cell-code" id="cb8"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a>p3way1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> connection_num <span class="sc">+</span> ROA <span class="sc">+</span> ESG_Rate <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ESG_Rate <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a>p3way2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span>  RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span>ESG_Rate <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a>p3way3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">*</span> connection_num <span class="sc">+</span> ESG_Rate  <span class="sc">+</span>  RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> ESG_Rate <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-8"><a href="#cb8-8" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(texreg)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>Version:  1.39.4
Date:     2024-07-23
Author:   Philip Leifeld (University of Manchester)

Consider submitting praise using the praise or praise_interactive functions.
Please cite the JSS article in your publications -- see citation("texreg").</code></pre>
</div>
<div class="sourceCode cell-code" id="cb10"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="fu">screenreg</span>(<span class="fu">list</span>(p3way1, p3way2, p3way3),</span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a>          <span class="at">custom.model.names =</span> <span class="fu">c</span>(<span class="st">"Model 1"</span>, <span class="st">"Model 2"</span>, <span class="st">"Model 3"</span>),</span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a>          <span class="at">stars =</span> <span class="fu">c</span>(<span class="fl">0.05</span>, <span class="fl">0.01</span>, <span class="fl">0.001</span>),</span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Mixed-Effects Models for Political Connection"</span></span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
==================================================================================
                                       Model 1        Model 2        Model 3      
----------------------------------------------------------------------------------
(Intercept)                               -72.12 ***     -63.32 ***     -62.99 ***
                                           (2.38)         (2.19)         (2.31)   
Age                                        -0.17 ***                     -0.12 ***
                                           (0.02)                        (0.02)   
connection_num                             -0.32 ***                     -0.23 ***
                                           (0.05)                        (0.06)   
ROA                                         0.02           0.04           0.04    
                                           (0.10)         (0.10)         (0.10)   
ESG_Rate                                    0.28 ***       0.26 ***       0.27 ***
                                           (0.01)         (0.01)         (0.01)   
Leverage                                    0.00           0.00           0.00    
                                           (0.00)         (0.00)         (0.00)   
RegisterCapital_log                         3.26 ***       2.50 ***       2.63 ***
                                           (0.11)         (0.10)         (0.11)   
after_first_inspection                                     6.61 ***       6.01 ***
                                                          (0.22)         (0.28)   
after_first_inspection:connection_num                                     0.19 *  
                                                                         (0.08)   
----------------------------------------------------------------------------------
AIC                                     83130.26       82407.56       82319.94    
BIC                                     83203.10       82473.13       82407.36    
Log Likelihood                         -41555.13      -41194.78      -41147.97    
Num. obs.                               10771          10777          10771       
Num. groups: CITY:PROVINCE                252            252            252       
Num. groups: PROVINCE                      32             32             32       
Var: CITY:PROVINCE (Intercept)             11.84          11.28          11.15    
Var: PROVINCE (Intercept)                   1.59           1.51           1.68    
Var: Residual                             127.72         118.99         118.35    
==================================================================================
*** p &lt; 0.001; ** p &lt; 0.01; * p &lt; 0.05</code></pre>
</div>
</div>
<p>#model 2</p>
<div class="cell">
<div class="sourceCode cell-code" id="cb12"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="co">#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure</span></span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a><span class="co"># p4 model 1   </span></span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lmerTest)</span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-8"><a href="#cb12-8" aria-hidden="true" tabindex="-1"></a>p4m1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> central_connection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb12-9"><a href="#cb12-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-10"><a href="#cb12-10" aria-hidden="true" tabindex="-1"></a>p4m2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span>  RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb12-11"><a href="#cb12-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-12"><a href="#cb12-12" aria-hidden="true" tabindex="-1"></a>p4m3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> local_connection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb12-13"><a href="#cb12-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-14"><a href="#cb12-14" aria-hidden="true" tabindex="-1"></a>p4m4 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span>  RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb12-15"><a href="#cb12-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-16"><a href="#cb12-16" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(texreg)</span>
<span id="cb12-17"><a href="#cb12-17" aria-hidden="true" tabindex="-1"></a><span class="fu">screenreg</span>(<span class="fu">list</span>(p4m1, p4m2, p4m3, p4m4),</span>
<span id="cb12-18"><a href="#cb12-18" aria-hidden="true" tabindex="-1"></a>          <span class="at">custom.model.names =</span> <span class="fu">c</span>(<span class="st">"Central 1"</span>, <span class="st">"Central 2"</span>, <span class="st">"Local 1"</span>, <span class="st">"Local 2"</span>),</span>
<span id="cb12-19"><a href="#cb12-19" aria-hidden="true" tabindex="-1"></a>          <span class="at">stars =</span> <span class="fu">c</span>(<span class="fl">0.05</span>, <span class="fl">0.01</span>, <span class="fl">0.001</span>),</span>
<span id="cb12-20"><a href="#cb12-20" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Mixed-Effects Models for Central vs Local Political Connection"</span></span>
<span id="cb12-21"><a href="#cb12-21" aria-hidden="true" tabindex="-1"></a>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
=====================================================================================================
                                           Central 1      Central 2      Local 1        Local 2      
-----------------------------------------------------------------------------------------------------
(Intercept)                                   -70.92 ***     -64.39 ***     -71.07 ***     -65.22 ***
                                               (2.36)         (2.28)         (2.36)         (2.26)   
Age                                            -0.17 ***                     -0.17 ***               
                                               (0.02)                        (0.02)                  
central_connection                             -0.99 ***      -0.61 **                               
                                               (0.18)         (0.21)                                 
ESG_Rate                                        0.28 ***       0.26 ***       0.28 ***       0.26 ***
                                               (0.01)         (0.01)         (0.01)         (0.01)   
RegisterCapital_log                             3.18 ***       2.56 ***       3.21 ***       2.62 ***
                                               (0.11)         (0.11)         (0.11)         (0.11)   
ROA                                             0.02           0.04           0.02           0.04    
                                               (0.10)         (0.10)         (0.10)         (0.10)   
Leverage                                        0.00           0.00           0.00           0.00    
                                               (0.00)         (0.00)         (0.00)         (0.00)   
after_first_inspection                                         6.30 ***                      6.19 ***
                                                              (0.24)                        (0.28)   
central_connection:after_first_inspection                      1.08 ***                              
                                                              (0.33)                                 
local_connection                                                             -0.32 ***      -0.26 ***
                                                                             (0.05)         (0.07)   
local_connection:after_first_inspection                                                      0.19 *  
                                                                                            (0.09)   
-----------------------------------------------------------------------------------------------------
AIC                                         83144.40       82401.39       83139.96       82404.06    
BIC                                         83217.25       82481.53       83212.80       82484.20    
Log Likelihood                             -41562.20      -41189.70      -41559.98      -41191.03    
Num. obs.                                   10771          10777          10771          10777       
Num. groups: CITY:PROVINCE                    252            252            252            252       
Num. groups: PROVINCE                          32             32             32             32       
Var: CITY:PROVINCE (Intercept)                 12.05          11.39          11.78          11.23    
Var: PROVINCE (Intercept)                       1.35           1.55           1.57           1.65    
Var: Residual                                 127.91         118.86         127.85         118.85    
=====================================================================================================
*** p &lt; 0.001; ** p &lt; 0.01; * p &lt; 0.05</code></pre>
</div>
</div>
<p>#correlation matrix of key variables</p>
<div class="cell">
<div class="sourceCode cell-code" id="cb14"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb14-1"><a href="#cb14-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Create a correlation matrix of key variables</span></span>
<span id="cb14-2"><a href="#cb14-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(corrplot)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>corrplot 0.95 loaded</code></pre>
</div>
<div class="sourceCode cell-code" id="cb16"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb16-1"><a href="#cb16-1" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(ggplot2)</span>
<span id="cb16-2"><a href="#cb16-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(reshape2)</span>
<span id="cb16-3"><a href="#cb16-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb16-4"><a href="#cb16-4" aria-hidden="true" tabindex="-1"></a><span class="co"># Select variables for correlation analysis</span></span>
<span id="cb16-5"><a href="#cb16-5" aria-hidden="true" tabindex="-1"></a>corr_vars <span class="ot">&lt;-</span> dta1[, <span class="fu">c</span>(<span class="st">"Environmental_Information_Disclosure"</span>, <span class="st">"connection_num"</span>, </span>
<span id="cb16-6"><a href="#cb16-6" aria-hidden="true" tabindex="-1"></a>                      <span class="st">"central_connection"</span>, <span class="st">"local_connection"</span>, <span class="st">"after_first_inspection"</span>,</span>
<span id="cb16-7"><a href="#cb16-7" aria-hidden="true" tabindex="-1"></a>                      <span class="st">"Age"</span>, <span class="st">"ROA"</span>, <span class="st">"ESG_Rate"</span>, <span class="st">"Leverage"</span>, <span class="st">"RegisterCapital_log"</span>)]</span>
<span id="cb16-8"><a href="#cb16-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb16-9"><a href="#cb16-9" aria-hidden="true" tabindex="-1"></a><span class="co"># 创建更易读的变量名</span></span>
<span id="cb16-10"><a href="#cb16-10" aria-hidden="true" tabindex="-1"></a>var_names <span class="ot">&lt;-</span> <span class="fu">c</span>(<span class="st">"Environmental</span><span class="sc">\n</span><span class="st">Disclosure"</span>, <span class="st">"Connection</span><span class="sc">\n</span><span class="st">Number"</span>, </span>
<span id="cb16-11"><a href="#cb16-11" aria-hidden="true" tabindex="-1"></a>               <span class="st">"Central</span><span class="sc">\n</span><span class="st">Connection"</span>, <span class="st">"Local</span><span class="sc">\n</span><span class="st">Connection"</span>, <span class="st">"After First</span><span class="sc">\n</span><span class="st">Inspection"</span>,</span>
<span id="cb16-12"><a href="#cb16-12" aria-hidden="true" tabindex="-1"></a>               <span class="st">"Age"</span>, <span class="st">"ROA"</span>, <span class="st">"ESG Rate"</span>, <span class="st">"Leverage"</span>, <span class="st">"Register</span><span class="sc">\n</span><span class="st">Capital (log)"</span>)</span>
<span id="cb16-13"><a href="#cb16-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb16-14"><a href="#cb16-14" aria-hidden="true" tabindex="-1"></a><span class="co"># 设置列名以便在图中使用</span></span>
<span id="cb16-15"><a href="#cb16-15" aria-hidden="true" tabindex="-1"></a><span class="fu">colnames</span>(corr_vars) <span class="ot">&lt;-</span> var_names</span>
<span id="cb16-16"><a href="#cb16-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb16-17"><a href="#cb16-17" aria-hidden="true" tabindex="-1"></a><span class="co"># Calculate correlation matrix</span></span>
<span id="cb16-18"><a href="#cb16-18" aria-hidden="true" tabindex="-1"></a>cor_matrix <span class="ot">&lt;-</span> <span class="fu">cor</span>(corr_vars, <span class="at">use =</span> <span class="st">"pairwise.complete.obs"</span>)</span>
<span id="cb16-19"><a href="#cb16-19" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb16-20"><a href="#cb16-20" aria-hidden="true" tabindex="-1"></a><span class="co"># Print correlation matrix with scientific formatting</span></span>
<span id="cb16-21"><a href="#cb16-21" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(<span class="fu">round</span>(cor_matrix, <span class="dv">3</span>), <span class="at">quote =</span> <span class="cn">FALSE</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>                          Environmental\nDisclosure Connection\nNumber
Environmental\nDisclosure                     1.000              0.082
Connection\nNumber                            0.082              1.000
Central\nConnection                           0.087              0.541
Local\nConnection                             0.069              0.968
After First\nInspection                       0.293             -0.051
Age                                          -0.028              0.102
ROA                                          -0.004             -0.004
ESG Rate                                      0.234              0.068
Leverage                                      0.004              0.006
Register\nCapital (log)                       0.316              0.343
                          Central\nConnection Local\nConnection
Environmental\nDisclosure               0.087             0.069
Connection\nNumber                      0.541             0.968
Central\nConnection                     1.000             0.313
Local\nConnection                       0.313             1.000
After First\nInspection                -0.037            -0.047
Age                                     0.025             0.108
ROA                                    -0.003            -0.003
ESG Rate                                0.081             0.052
Leverage                               -0.003             0.008
Register\nCapital (log)                 0.263             0.309
                          After First\nInspection    Age    ROA ESG Rate
Environmental\nDisclosure                   0.293 -0.028 -0.004    0.234
Connection\nNumber                         -0.051  0.102 -0.004    0.068
Central\nConnection                        -0.037  0.025 -0.003    0.081
Local\nConnection                          -0.047  0.108 -0.003    0.052
After First\nInspection                     1.000 -0.140 -0.034    0.064
Age                                        -0.140  1.000 -0.009   -0.020
ROA                                        -0.034 -0.009  1.000   -0.001
ESG Rate                                    0.064 -0.020 -0.001    1.000
Leverage                                   -0.002 -0.002 -0.005    0.005
Register\nCapital (log)                     0.065  0.214 -0.017    0.192
                          Leverage Register\nCapital (log)
Environmental\nDisclosure    0.004                   0.316
Connection\nNumber           0.006                   0.343
Central\nConnection         -0.003                   0.263
Local\nConnection            0.008                   0.309
After First\nInspection     -0.002                   0.065
Age                         -0.002                   0.214
ROA                         -0.005                  -0.017
ESG Rate                     0.005                   0.192
Leverage                     1.000                   0.013
Register\nCapital (log)      0.013                   1.000</code></pre>
</div>
<div class="sourceCode cell-code" id="cb18"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb18-1"><a href="#cb18-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Create publication-quality correlation plot</span></span>
<span id="cb18-2"><a href="#cb18-2" aria-hidden="true" tabindex="-1"></a><span class="co"># Calculate p-values</span></span>
<span id="cb18-3"><a href="#cb18-3" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(Hmisc)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'Hmisc'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:base':

    format.pval, units</code></pre>
</div>
<div class="sourceCode cell-code" id="cb21"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb21-1"><a href="#cb21-1" aria-hidden="true" tabindex="-1"></a>cor_test <span class="ot">&lt;-</span> <span class="fu">rcorr</span>(<span class="fu">as.matrix</span>(corr_vars))</span>
<span id="cb21-2"><a href="#cb21-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-3"><a href="#cb21-3" aria-hidden="true" tabindex="-1"></a><span class="co"># Melt the correlation matrix for ggplot</span></span>
<span id="cb21-4"><a href="#cb21-4" aria-hidden="true" tabindex="-1"></a>melted_cor <span class="ot">&lt;-</span> <span class="fu">melt</span>(cor_matrix)</span>
<span id="cb21-5"><a href="#cb21-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-6"><a href="#cb21-6" aria-hidden="true" tabindex="-1"></a><span class="co"># Add p-values to the melted dataframe</span></span>
<span id="cb21-7"><a href="#cb21-7" aria-hidden="true" tabindex="-1"></a>melted_cor<span class="sc">$</span>p_value <span class="ot">&lt;-</span> <span class="fu">melt</span>(cor_test<span class="sc">$</span>P)<span class="sc">$</span>value</span>
<span id="cb21-8"><a href="#cb21-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-9"><a href="#cb21-9" aria-hidden="true" tabindex="-1"></a><span class="co"># Create the ggplot visualization with grayscale color scheme</span></span>
<span id="cb21-10"><a href="#cb21-10" aria-hidden="true" tabindex="-1"></a>p <span class="ot">&lt;-</span> <span class="fu">ggplot</span>(<span class="at">data =</span> melted_cor, <span class="fu">aes</span>(<span class="at">x =</span> Var1, <span class="at">y =</span> Var2, <span class="at">fill =</span> value)) <span class="sc">+</span></span>
<span id="cb21-11"><a href="#cb21-11" aria-hidden="true" tabindex="-1"></a>  <span class="fu">geom_tile</span>(<span class="at">color =</span> <span class="st">"white"</span>) <span class="sc">+</span></span>
<span id="cb21-12"><a href="#cb21-12" aria-hidden="true" tabindex="-1"></a>  <span class="fu">geom_text</span>(<span class="fu">aes</span>(<span class="at">label =</span> <span class="fu">ifelse</span>(p_value <span class="sc">&lt;</span> <span class="fl">0.05</span>, </span>
<span id="cb21-13"><a href="#cb21-13" aria-hidden="true" tabindex="-1"></a>                              <span class="fu">sprintf</span>(<span class="st">"%.2f*"</span>, value), </span>
<span id="cb21-14"><a href="#cb21-14" aria-hidden="true" tabindex="-1"></a>                              <span class="fu">sprintf</span>(<span class="st">"%.2f"</span>, value))), </span>
<span id="cb21-15"><a href="#cb21-15" aria-hidden="true" tabindex="-1"></a>            <span class="at">size =</span> <span class="dv">3</span>) <span class="sc">+</span></span>
<span id="cb21-16"><a href="#cb21-16" aria-hidden="true" tabindex="-1"></a>  <span class="fu">scale_fill_gradient2</span>(<span class="at">low =</span> <span class="st">"gray20"</span>, <span class="at">high =</span> <span class="st">"gray80"</span>, <span class="at">mid =</span> <span class="st">"white"</span>, </span>
<span id="cb21-17"><a href="#cb21-17" aria-hidden="true" tabindex="-1"></a>                       <span class="at">midpoint =</span> <span class="dv">0</span>, <span class="at">limit =</span> <span class="fu">c</span>(<span class="sc">-</span><span class="dv">1</span>,<span class="dv">1</span>), <span class="at">name =</span> <span class="st">"Correlation"</span>) <span class="sc">+</span></span>
<span id="cb21-18"><a href="#cb21-18" aria-hidden="true" tabindex="-1"></a>  <span class="fu">theme_minimal</span>() <span class="sc">+</span></span>
<span id="cb21-19"><a href="#cb21-19" aria-hidden="true" tabindex="-1"></a>  <span class="fu">theme</span>(<span class="at">axis.text.x =</span> <span class="fu">element_text</span>(<span class="at">angle =</span> <span class="dv">45</span>, <span class="at">vjust =</span> <span class="dv">1</span>, <span class="at">hjust =</span> <span class="dv">1</span>, <span class="at">size =</span> <span class="dv">8</span>),</span>
<span id="cb21-20"><a href="#cb21-20" aria-hidden="true" tabindex="-1"></a>        <span class="at">axis.text.y =</span> <span class="fu">element_text</span>(<span class="at">size =</span> <span class="dv">8</span>),</span>
<span id="cb21-21"><a href="#cb21-21" aria-hidden="true" tabindex="-1"></a>        <span class="at">axis.title =</span> <span class="fu">element_blank</span>(),</span>
<span id="cb21-22"><a href="#cb21-22" aria-hidden="true" tabindex="-1"></a>        <span class="at">panel.grid.major =</span> <span class="fu">element_blank</span>(),</span>
<span id="cb21-23"><a href="#cb21-23" aria-hidden="true" tabindex="-1"></a>        <span class="at">legend.position =</span> <span class="st">"bottom"</span>) <span class="sc">+</span></span>
<span id="cb21-24"><a href="#cb21-24" aria-hidden="true" tabindex="-1"></a>  <span class="fu">coord_fixed</span>() <span class="sc">+</span></span>
<span id="cb21-25"><a href="#cb21-25" aria-hidden="true" tabindex="-1"></a>  <span class="fu">labs</span>(<span class="at">caption =</span> <span class="st">"* indicates p &lt; 0.05"</span>)</span>
<span id="cb21-26"><a href="#cb21-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-27"><a href="#cb21-27" aria-hidden="true" tabindex="-1"></a><span class="co"># Display the plot</span></span>
<span id="cb21-28"><a href="#cb21-28" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(p)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>Warning: Removed 10 rows containing missing values or values outside the scale range
(`geom_text()`).</code></pre>
</div>
<div class="cell-output-display">
<div>
<figure class="figure">
<p><img src="OE-revision_files/figure-html/unnamed-chunk-1-1.png" class="img-fluid figure-img" width="672"></p>
</figure>
</div>
</div>
<div class="sourceCode cell-code" id="cb23"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb23-1"><a href="#cb23-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Save as SVG for publication (vector format, maintains quality at any size)</span></span>
<span id="cb23-2"><a href="#cb23-2" aria-hidden="true" tabindex="-1"></a><span class="fu">ggsave</span>(<span class="st">"d:/Research/ESG/ESG_China/R-ESG China/correlation_matrix_gray.svg"</span>, </span>
<span id="cb23-3"><a href="#cb23-3" aria-hidden="true" tabindex="-1"></a>       <span class="at">plot =</span> p, <span class="at">width =</span> <span class="dv">8</span>, <span class="at">height =</span> <span class="dv">7</span>, <span class="at">dpi =</span> <span class="dv">300</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>Warning: Removed 10 rows containing missing values or values outside the scale range
(`geom_text()`).</code></pre>
</div>
</div>
<section id="after-loading-the-data" class="level1">
<h1>After loading the data</h1>
<div class="cell">
<div class="sourceCode cell-code" id="cb25"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb25-1"><a href="#cb25-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Calculate number of unique firms and total firm-year observations</span></span>
<span id="cb25-2"><a href="#cb25-2" aria-hidden="true" tabindex="-1"></a>n_firms <span class="ot">&lt;-</span> <span class="fu">length</span>(<span class="fu">unique</span>(dta1<span class="sc">$</span>Symbol))</span>
<span id="cb25-3"><a href="#cb25-3" aria-hidden="true" tabindex="-1"></a>n_observations <span class="ot">&lt;-</span> <span class="fu">nrow</span>(dta1)</span>
<span id="cb25-4"><a href="#cb25-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb25-5"><a href="#cb25-5" aria-hidden="true" tabindex="-1"></a><span class="co"># Create a publication-quality table for sample description</span></span>
<span id="cb25-6"><a href="#cb25-6" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(kableExtra)</span>
<span id="cb25-7"><a href="#cb25-7" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(dplyr)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'dplyr'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following object is masked from 'package:kableExtra':

    group_rows</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:Hmisc':

    src, summarize</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:stats':

    filter, lag</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:base':

    intersect, setdiff, setequal, union</code></pre>
</div>
<div class="sourceCode cell-code" id="cb31"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb31-1"><a href="#cb31-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Create sample description dataframe</span></span>
<span id="cb31-2"><a href="#cb31-2" aria-hidden="true" tabindex="-1"></a>sample_df <span class="ot">&lt;-</span> <span class="fu">data.frame</span>(</span>
<span id="cb31-3"><a href="#cb31-3" aria-hidden="true" tabindex="-1"></a>  <span class="at">Description =</span> <span class="fu">c</span>(<span class="st">"Number of unique firms"</span>, <span class="st">"Number of firm-year observations"</span>),</span>
<span id="cb31-4"><a href="#cb31-4" aria-hidden="true" tabindex="-1"></a>  <span class="at">Count =</span> <span class="fu">c</span>(n_firms, n_observations)</span>
<span id="cb31-5"><a href="#cb31-5" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb31-6"><a href="#cb31-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb31-7"><a href="#cb31-7" aria-hidden="true" tabindex="-1"></a><span class="co"># Print the table in a clean format suitable for Science journal</span></span>
<span id="cb31-8"><a href="#cb31-8" aria-hidden="true" tabindex="-1"></a><span class="fu">kable</span>(sample_df, <span class="at">format =</span> <span class="st">"html"</span>, <span class="at">col.names =</span> <span class="fu">c</span>(<span class="st">"Description"</span>, <span class="st">"Count"</span>), </span>
<span id="cb31-9"><a href="#cb31-9" aria-hidden="true" tabindex="-1"></a>      <span class="at">align =</span> <span class="fu">c</span>(<span class="st">"l"</span>, <span class="st">"r"</span>), <span class="at">caption =</span> <span class="st">"Sample Composition"</span>) <span class="sc">%&gt;%</span></span>
<span id="cb31-10"><a href="#cb31-10" aria-hidden="true" tabindex="-1"></a>  <span class="fu">kable_styling</span>(<span class="at">bootstrap_options =</span> <span class="fu">c</span>(<span class="st">"striped"</span>, <span class="st">"condensed"</span>), </span>
<span id="cb31-11"><a href="#cb31-11" aria-hidden="true" tabindex="-1"></a>                <span class="at">full_width =</span> <span class="cn">FALSE</span>, <span class="at">position =</span> <span class="st">"left"</span>,</span>
<span id="cb31-12"><a href="#cb31-12" aria-hidden="true" tabindex="-1"></a>                <span class="at">font_size =</span> <span class="dv">12</span>) <span class="sc">%&gt;%</span></span>
<span id="cb31-13"><a href="#cb31-13" aria-hidden="true" tabindex="-1"></a>  <span class="fu">row_spec</span>(<span class="dv">0</span>, <span class="at">bold =</span> <span class="cn">TRUE</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table class="table table-striped table-condensed caption-top table-sm small" data-quarto-postprocess="true">
<caption>Sample Composition</caption>
<thead>
<tr class="header">
<th data-quarto-table-cell-role="th" style="text-align: left; font-weight: bold;">Description</th>
<th data-quarto-table-cell-role="th" style="text-align: right; font-weight: bold;">Count</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;">Number of unique firms</td>
<td style="text-align: right;">4973</td>
</tr>
<tr class="even">
<td style="text-align: left;">Number of firm-year observations</td>
<td style="text-align: right;">54703</td>
</tr>
</tbody>
</table>


</div>
<div class="sourceCode cell-code" id="cb32"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb32-1"><a href="#cb32-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Additional sample information (optional)</span></span>
<span id="cb32-2"><a href="#cb32-2" aria-hidden="true" tabindex="-1"></a><span class="co"># Time period</span></span>
<span id="cb32-3"><a href="#cb32-3" aria-hidden="true" tabindex="-1"></a>year_range <span class="ot">&lt;-</span> <span class="fu">range</span>(dta1<span class="sc">$</span>EndYear, <span class="at">na.rm =</span> <span class="cn">TRUE</span>)</span>
<span id="cb32-4"><a href="#cb32-4" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="fu">sprintf</span>(<span class="st">"The sample covers %d firms over the period %d to %d, resulting in %d firm-year observations."</span>,</span>
<span id="cb32-5"><a href="#cb32-5" aria-hidden="true" tabindex="-1"></a>            n_firms, year_range[<span class="dv">1</span>], year_range[<span class="dv">2</span>], n_observations))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>The sample covers 4973 firms over the period 15339 to 18992, resulting in 54703 firm-year observations.</code></pre>
</div>
</div>
</section>
<section id="multilevel-models" class="level1">
<h1>Multilevel models</h1>
<div class="cell">
<div class="sourceCode cell-code" id="cb34"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb34-1"><a href="#cb34-1" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span>
<span id="cb34-2"><a href="#cb34-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lmerTest)</span>
<span id="cb34-3"><a href="#cb34-3" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(texreg)  <span class="co"># For handling mixed models in tables</span></span>
<span id="cb34-4"><a href="#cb34-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb34-5"><a href="#cb34-5" aria-hidden="true" tabindex="-1"></a><span class="co"># Create multilevel models for political connections</span></span>
<span id="cb34-6"><a href="#cb34-6" aria-hidden="true" tabindex="-1"></a>p3mix1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> connection_num <span class="sc">+</span> ROA <span class="sc">+</span> ESG_Rate <span class="sc">+</span> Leverage <span class="sc">+</span> </span>
<span id="cb34-7"><a href="#cb34-7" aria-hidden="true" tabindex="-1"></a>               RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE), <span class="at">data =</span> dta1)</span>
<span id="cb34-8"><a href="#cb34-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb34-9"><a href="#cb34-9" aria-hidden="true" tabindex="-1"></a>p3mix2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> </span>
<span id="cb34-10"><a href="#cb34-10" aria-hidden="true" tabindex="-1"></a>               RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE), <span class="at">data =</span> dta1)</span>
<span id="cb34-11"><a href="#cb34-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb34-12"><a href="#cb34-12" aria-hidden="true" tabindex="-1"></a>p3mix3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">*</span> connection_num <span class="sc">+</span> ESG_Rate <span class="sc">+</span> </span>
<span id="cb34-13"><a href="#cb34-13" aria-hidden="true" tabindex="-1"></a>               ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE), <span class="at">data =</span> dta1)</span>
<span id="cb34-14"><a href="#cb34-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb34-15"><a href="#cb34-15" aria-hidden="true" tabindex="-1"></a><span class="co"># Use texreg to create output for mixed models</span></span>
<span id="cb34-16"><a href="#cb34-16" aria-hidden="true" tabindex="-1"></a><span class="fu">screenreg</span>(<span class="fu">list</span>(p3mix1, p3mix2, p3mix3), </span>
<span id="cb34-17"><a href="#cb34-17" aria-hidden="true" tabindex="-1"></a>          <span class="at">custom.model.names =</span> <span class="fu">c</span>(<span class="st">"Model 1"</span>, <span class="st">"Model 2"</span>, <span class="st">"Model 3"</span>),</span>
<span id="cb34-18"><a href="#cb34-18" aria-hidden="true" tabindex="-1"></a>          <span class="at">stars =</span> <span class="fu">c</span>(<span class="fl">0.05</span>, <span class="fl">0.01</span>, <span class="fl">0.001</span>),</span>
<span id="cb34-19"><a href="#cb34-19" aria-hidden="true" tabindex="-1"></a>          <span class="at">custom.coef.names =</span> <span class="fu">c</span>(<span class="st">"Intercept"</span>, <span class="st">"Age"</span>, <span class="st">"Connection Number"</span>, <span class="st">"ROA"</span>, <span class="st">"ESG Rate"</span>, <span class="st">"Leverage"</span>, </span>
<span id="cb34-20"><a href="#cb34-20" aria-hidden="true" tabindex="-1"></a>                               <span class="st">"Register Capital (log)"</span>, <span class="st">"After First Inspection"</span>, </span>
<span id="cb34-21"><a href="#cb34-21" aria-hidden="true" tabindex="-1"></a>                               <span class="st">"Connection Number × After First Inspection"</span>),</span>
<span id="cb34-22"><a href="#cb34-22" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Multilevel Models for Political Connection Effects"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
=======================================================================================
                                            Model 1        Model 2        Model 3      
---------------------------------------------------------------------------------------
Intercept                                      -71.94 ***     -63.78 ***     -63.38 ***
                                                (2.35)         (2.17)         (2.28)   
Age                                             -0.17 ***                     -0.12 ***
                                                (0.02)                        (0.02)   
Connection Number                               -0.32 ***                     -0.23 ***
                                                (0.05)                        (0.06)   
ROA                                              0.03           0.04           0.04    
                                                (0.11)         (0.10)         (0.10)   
ESG Rate                                         0.28 ***       0.26 ***       0.27 ***
                                                (0.01)         (0.01)         (0.01)   
Leverage                                         0.00           0.00           0.00    
                                                (0.00)         (0.00)         (0.00)   
Register Capital (log)                           3.24 ***       2.50 ***       2.63 ***
                                                (0.11)         (0.10)         (0.11)   
After First Inspection                                          6.67 ***       6.11 ***
                                                               (0.22)         (0.28)   
Connection Number × After First Inspection                                     0.17 *  
                                                                              (0.08)   
---------------------------------------------------------------------------------------
AIC                                          83289.74       82568.86       82479.91    
BIC                                          83355.30       82627.14       82560.04    
Log Likelihood                              -41635.87      -41276.43      -41228.96    
Num. obs.                                    10771          10777          10771       
Num. groups: PROVINCE                           32             32             32       
Var: PROVINCE (Intercept)                        4.17           3.98           4.02    
Var: Residual                                  132.25         123.27         122.58    
=======================================================================================
*** p &lt; 0.001; ** p &lt; 0.01; * p &lt; 0.05</code></pre>
</div>
<div class="sourceCode cell-code" id="cb36"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb36-1"><a href="#cb36-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Add multilevel models for central/local connections</span></span>
<span id="cb36-2"><a href="#cb36-2" aria-hidden="true" tabindex="-1"></a>p4mix1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> central_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> </span>
<span id="cb36-3"><a href="#cb36-3" aria-hidden="true" tabindex="-1"></a>               RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE), <span class="at">data =</span> dta1)</span>
<span id="cb36-4"><a href="#cb36-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb36-5"><a href="#cb36-5" aria-hidden="true" tabindex="-1"></a>p4mix2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> </span>
<span id="cb36-6"><a href="#cb36-6" aria-hidden="true" tabindex="-1"></a>               ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE), <span class="at">data =</span> dta1)</span>
<span id="cb36-7"><a href="#cb36-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb36-8"><a href="#cb36-8" aria-hidden="true" tabindex="-1"></a>p4mix3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> local_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> </span>
<span id="cb36-9"><a href="#cb36-9" aria-hidden="true" tabindex="-1"></a>               RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE), <span class="at">data =</span> dta1)</span>
<span id="cb36-10"><a href="#cb36-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb36-11"><a href="#cb36-11" aria-hidden="true" tabindex="-1"></a>p4mix4 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> </span>
<span id="cb36-12"><a href="#cb36-12" aria-hidden="true" tabindex="-1"></a>               ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE), <span class="at">data =</span> dta1)</span>
<span id="cb36-13"><a href="#cb36-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb36-14"><a href="#cb36-14" aria-hidden="true" tabindex="-1"></a><span class="co"># Use texreg for central/local connection models</span></span>
<span id="cb36-15"><a href="#cb36-15" aria-hidden="true" tabindex="-1"></a><span class="fu">screenreg</span>(<span class="fu">list</span>(p4mix1, p4mix2, p4mix3, p4mix4), </span>
<span id="cb36-16"><a href="#cb36-16" aria-hidden="true" tabindex="-1"></a>          <span class="at">custom.model.names =</span> <span class="fu">c</span>(<span class="st">"Central 1"</span>, <span class="st">"Central 2"</span>, <span class="st">"Local 1"</span>, <span class="st">"Local 2"</span>),</span>
<span id="cb36-17"><a href="#cb36-17" aria-hidden="true" tabindex="-1"></a>          <span class="at">stars =</span> <span class="fu">c</span>(<span class="fl">0.05</span>, <span class="fl">0.01</span>, <span class="fl">0.001</span>),</span>
<span id="cb36-18"><a href="#cb36-18" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Multilevel Models for Central vs Local Political Connection Effects"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
=====================================================================================================
                                           Central 1      Central 2      Local 1        Local 2      
-----------------------------------------------------------------------------------------------------
(Intercept)                                   -70.80 ***     -64.82 ***     -70.95 ***     -65.66 ***
                                               (2.34)         (2.25)         (2.33)         (2.23)   
Age                                            -0.17 ***                     -0.17 ***               
                                               (0.02)                        (0.02)                  
central_connection                             -0.98 ***      -0.60 **                               
                                               (0.18)         (0.21)                                 
ESG_Rate                                        0.28 ***       0.26 ***       0.28 ***       0.26 ***
                                               (0.01)         (0.01)         (0.01)         (0.01)   
ROA                                             0.03           0.04           0.03           0.04    
                                               (0.11)         (0.10)         (0.11)         (0.10)   
Leverage                                       -0.00           0.00           0.00           0.00    
                                               (0.00)         (0.00)         (0.00)         (0.00)   
RegisterCapital_log                             3.16 ***       2.56 ***       3.20 ***       2.62 ***
                                               (0.11)         (0.11)         (0.11)         (0.11)   
after_first_inspection                                         6.38 ***                      6.28 ***
                                                              (0.24)                        (0.28)   
central_connection:after_first_inspection                      1.03 **                               
                                                              (0.33)                                 
local_connection                                                             -0.33 ***      -0.26 ***
                                                                             (0.05)         (0.07)   
local_connection:after_first_inspection                                                      0.17    
                                                                                            (0.09)   
-----------------------------------------------------------------------------------------------------
AIC                                         83306.10       82563.89       83299.05       82564.58    
BIC                                         83371.66       82636.75       83364.61       82637.43    
Log Likelihood                             -41644.05      -41271.95      -41640.53      -41272.29    
Num. obs.                                   10771          10777          10771          10777       
Num. groups: PROVINCE                          32             32             32             32       
Var: PROVINCE (Intercept)                       3.94           4.02           4.15           4.09    
Var: Residual                                 132.51         123.16         132.37         123.11    
=====================================================================================================
*** p &lt; 0.001; ** p &lt; 0.01; * p &lt; 0.05</code></pre>
</div>
<div class="sourceCode cell-code" id="cb38"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb38-1"><a href="#cb38-1" aria-hidden="true" tabindex="-1"></a><span class="co"># If you want HTML output for publication, use htmlreg instead</span></span>
<span id="cb38-2"><a href="#cb38-2" aria-hidden="true" tabindex="-1"></a><span class="fu">htmlreg</span>(<span class="fu">list</span>(p3mix1, p3mix2, p3mix3), <span class="at">file =</span> <span class="st">"d:/Research/ESG/ESG_China/R-ESG China/multilevel_models_p1.html"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>The table was written to the file 'd:/Research/ESG/ESG_China/R-ESG China/multilevel_models_p1.html'.</code></pre>
</div>
<div class="sourceCode cell-code" id="cb40"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb40-1"><a href="#cb40-1" aria-hidden="true" tabindex="-1"></a><span class="fu">htmlreg</span>(<span class="fu">list</span>(p4mix1, p4mix2, p4mix3, p4mix4), <span class="at">file =</span> <span class="st">"d:/Research/ESG/ESG_China/R-ESG China/multilevel_models_p2.html"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>The table was written to the file 'd:/Research/ESG/ESG_China/R-ESG China/multilevel_models_p2.html'.</code></pre>
</div>
</div>
</section>

</main>
<!-- /main column -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
</div> <!-- /content -->




</body></html>