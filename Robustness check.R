## 模型robustness检测

# p1 model 1

library(plm)
plm1way <- plm(BloobergValue ~ Age + after_first_inspection + ROA + ESG_Rate + Leverage + RegisterCapital_log + ESG_Rate, data=data9,
               index=c("EndYear"),model="within")


plm1way2 <- plm(BloobergValue ~ after_first_inspection  + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
                index=c("Symbol"),model="within")

plm2way <- plm(BloobergValue ~ Age + after_first_inspection  + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
               index=c("Symbol", "EndYear"),model="within",effect="twoways")

library(stargazer)
stargazer(plm1way, plm1way2, plm2way,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)


# p1 model 2

library(plm)
plm1way <- plm(BloobergValue ~ Age + ROA + ESG_Rate + Leverage + RegisterCapital_log + connections + ESG_Rate, data=data9,
               index=c("EndYear"),model="within")


plm1way2 <- plm(BloobergValue ~ ESG_Rate  + RegisterCapital_log + ROA + Leverage + connections +ESG_Rate, data=data9,
                index=c("Symbol"),model="within")

plm2way <- plm(BloobergValue ~ Age + ESG_Rate  + RegisterCapital_log + ROA + Leverage + connections+ESG_Rate, data=data9,
               index=c("Symbol", "EndYear"),model="within",effect="twoways")

library(stargazer)
stargazer(plm1way, plm1way2, plm2way,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)



# p1 model 3

library(plm)
plm1way <- plm(BloobergValue ~ Age + ROA + ESG_Rate + Leverage + RegisterCapital_log + after_first_inspection*connections + ESG_Rate, data=data9,
               index=c("EndYear"),model="within")


plm1way2 <- plm(BloobergValue ~ ESG_Rate  + RegisterCapital_log + ROA + Leverage + after_first_inspection*connections +ESG_Rate, data=data9,
                index=c("Symbol"),model="within")

plm2way <- plm(BloobergValue ~ Age + ESG_Rate  + RegisterCapital_log + ROA + Leverage + after_first_inspection*connections+ESG_Rate, data=data9,
               index=c("Symbol", "EndYear"),model="within",effect="twoways")

library(stargazer)
stargazer(plm1way, plm1way2, plm2way,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)


# p2 model 1

library(plm)
plm1way <- plm(BloobergValue ~ Age + central_connection+ ROA + ESG_Rate + Leverage + RegisterCapital_log + ESG_Rate, data=data9,
               index=c("EndYear"),model="within")


plm1way2 <- plm(BloobergValue ~ central_connection  + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
                index=c("Symbol"),model="within")

plm2way <- plm(BloobergValue ~ Age + central_connection  + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
               index=c("Symbol", "EndYear"),model="within",effect="twoways")

library(stargazer)
stargazer(plm1way, plm1way2, plm2way,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)


# p2 model 2

library(plm)
plm1way <- plm(BloobergValue ~ Age + central_connection * after_first_inspection + ROA + ESG_Rate + Leverage + RegisterCapital_log + ESG_Rate, data=data9,
               index=c("EndYear"),model="within")


plm1way2 <- plm(BloobergValue ~ central_connection * after_first_inspection + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
                index=c("Symbol"),model="within")

plm2way <- plm(BloobergValue ~ Age + central_connection * after_first_inspection + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
               index=c("Symbol", "EndYear"),model="within",effect="twoways")

library(stargazer)
stargazer(plm1way, plm1way2, plm2way,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)

# p2 model 3

library(plm)
plm1way <- plm(BloobergValue ~ Age + local_connection+ ROA + ESG_Rate + Leverage + RegisterCapital_log + ESG_Rate, data=data9,
               index=c("EndYear"),model="within")


plm1way2 <- plm(BloobergValue ~ local_connection  + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
                index=c("Symbol"),model="within")

plm2way <- plm(BloobergValue ~ Age + local_connection  + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
               index=c("Symbol", "EndYear"),model="within",effect="twoways")

library(stargazer)
stargazer(plm1way, plm1way2, plm2way,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)


# p2 model 4

library(plm)
plm1way <- plm(BloobergValue ~ Age + local_connection * after_first_inspection + ROA + ESG_Rate + Leverage + RegisterCapital_log + ESG_Rate, data=data9,
               index=c("EndYear"),model="within")


plm1way2 <- plm(BloobergValue ~ local_connection * after_first_inspection + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
                index=c("Symbol"),model="within")

plm2way <- plm(BloobergValue ~ Age + local_connection * after_first_inspection + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
               index=c("Symbol", "EndYear"),model="within",effect="twoways")

library(stargazer)
stargazer(plm1way, plm1way2, plm2way,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)

# p2 model 5

library(plm)
plm1way <- plm(BloobergValue ~ Age + local_connection * after_first_inspection + central_connection * after_first_inspection + ROA + ESG_Rate + Leverage + RegisterCapital_log + ESG_Rate, data=data9,
               index=c("EndYear"),model="within")


plm1way2 <- plm(BloobergValue ~ local_connection * after_first_inspection + central_connection * after_first_inspection + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
                index=c("Symbol"),model="within")

plm2way <- plm(BloobergValue ~ Age + local_connection * after_first_inspection + central_connection * after_first_inspection + ESG_Rate  + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=data9,
               index=c("Symbol", "EndYear"),model="within",effect="twoways")

library(stargazer)
stargazer(plm1way, plm1way2, plm2way,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
