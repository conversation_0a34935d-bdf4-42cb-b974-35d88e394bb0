<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.39">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>Robustness Check</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
/* CSS for syntax highlighting */
pre > code.sourceCode { white-space: pre; position: relative; }
pre > code.sourceCode > span { line-height: 1.25; }
pre > code.sourceCode > span:empty { height: 1.2em; }
.sourceCode { overflow: visible; }
code.sourceCode > span { color: inherit; text-decoration: inherit; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
pre > code.sourceCode { white-space: pre-wrap; }
pre > code.sourceCode > span { display: inline-block; text-indent: -5em; padding-left: 5em; }
}
pre.numberSource code
  { counter-reset: source-line 0; }
pre.numberSource code > span
  { position: relative; left: -4em; counter-increment: source-line; }
pre.numberSource code > span > a:first-child::before
  { content: counter(source-line);
    position: relative; left: -1em; text-align: right; vertical-align: baseline;
    border: none; display: inline-block;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
  }
pre.numberSource { margin-left: 3em;  padding-left: 4px; }
div.sourceCode
  {   }
@media screen {
pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
}
</style>


<script src="Robustness test_files/libs/clipboard/clipboard.min.js"></script>
<script src="Robustness test_files/libs/quarto-html/quarto.js"></script>
<script src="Robustness test_files/libs/quarto-html/popper.min.js"></script>
<script src="Robustness test_files/libs/quarto-html/tippy.umd.min.js"></script>
<script src="Robustness test_files/libs/quarto-html/anchor.min.js"></script>
<link href="Robustness test_files/libs/quarto-html/tippy.css" rel="stylesheet">
<link href="Robustness test_files/libs/quarto-html/quarto-syntax-highlighting-e26003cea8cd680ca0c55a263523d882.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="Robustness test_files/libs/bootstrap/bootstrap.min.js"></script>
<link href="Robustness test_files/libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="Robustness test_files/libs/bootstrap/bootstrap-8a79a254b8e706d3c925cde0a310d4f0.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">


</head>

<body class="fullcontent">

<div id="quarto-content" class="page-columns page-rows-contents page-layout-article">

<main class="content" id="quarto-document-content">

<header id="title-block-header" class="quarto-title-block default">
<div class="quarto-title">
<h1 class="title">Robustness Check</h1>
</div>



<div class="quarto-title-meta">

    
  
    
  </div>
  


</header>


<section id="package-installation-and-setup" class="level1">
<h1>Package Installation and Setup</h1>
<div class="cell">
<div class="sourceCode cell-code" id="cb1"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Load the Organization &amp; Environment style function</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="fu">source</span>(<span class="st">"modelsummary_OE_style.R"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'kableExtra'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:flextable':

    as_image, footnote</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'dplyr'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following object is masked from 'package:kableExtra':

    group_rows</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:plm':

    between, lag, lead</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:stats':

    filter, lag</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:base':

    intersect, setdiff, setequal, union</code></pre>
</div>
</div>
</section>
<section id="data-loading" class="level1">
<h1>Data loading</h1>
<div class="cell">
<div class="sourceCode cell-code" id="cb9"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Load the data file</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a><span class="fu">load</span>(<span class="st">"dta1_20240903.RData"</span>)</span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a><span class="co"># You can add a quick check to confirm the data loaded correctly</span></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a><span class="co"># 重新计算Age变量，使其随年份变化</span></span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lubridate)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'lubridate'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:base':

    date, intersect, setdiff, union</code></pre>
</div>
<div class="sourceCode cell-code" id="cb12"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(dplyr)</span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a>dta1 <span class="ot">&lt;-</span> dta1 <span class="sc">%&gt;%</span></span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(</span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a>    <span class="at">EndYear =</span> <span class="fu">ymd</span>(EndYear),</span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a>    <span class="at">Year =</span> <span class="fu">year</span>(EndYear),</span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a>    <span class="at">Age =</span> Year <span class="sc">-</span> EstablishYear</span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a>  )</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</div>
<div class="cell">
<div class="sourceCode cell-code" id="cb13"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a>dta1<span class="sc">$</span>Age</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>    [1] 24 25 26 27 28 29 30 31 32 33 34 23 24 25 26 27 28 29 30 31 32 33 25 26
   [25] 27 28 29 30 31 32 33 34 35 21 22 23 24 25 26 27 28 29 30 31 22 23 24 25
   [49] 26 27 28 29 30 31 32 23 24 25 26 27 28 29 30 31 32 33 22 23 24 25 26 27
   [73] 28 29 30 31 32 21 22 23 24 25 26 27 28 29 30 31 23 24 25 26 27 28 29 30
   [97] 31 32 33 28 29 30 31 32 33 34 35 36 37 38 27 28 29 30 31 32 33 34 35 36
  [121] 37 19 20 21 22 23 24 25 26 27 28 29 31 32 33 34 35 36 37 38 39 40 41 19
  [145] 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21
  [169] 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 27 28 29 30 31
  [193] 32 33 34 35 36 37 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24
  [217] 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26
  [241] 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28
  [265] 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19
  [289] 20 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23 24 25 26 27 17 18 19 20
  [313] 21 22 23 24 25 26 27 17 18 19 20 21 22 23 24 25 26 27 17 18 19 20 21 22
  [337] 23 24 25 26 27 19 20 21 22 23 24 25 26 27 28 29 17 18 19 20 21 22 23 24
  [361] 25 26 27 17 18 19 20 21 22 23 24 25 26 27 17 18 19 20 21 22 23 24 25 26
  [385] 27 22 23 24 25 26 27 28 29 30 31 32 17 18 19 20 21 22 23 24 25 26 27 16
  [409] 17 18 19 20 21 22 23 24 25 26 28 29 30 31 32 33 34 35 36 37 38 17 18 19
  [433] 20 21 22 23 24 25 26 27 18 19 20 21 22 23 24 25 26 27 28 15 16 17 18 19
  [457] 20 21 22 23 24 25 14 15 16 17 18 19 20 21 22 23 24 17 18 19 20 21 22 23
  [481] 24 25 26 27 17 18 19 20 21 22 23 24 25 26 27 17 18 19 20 21 22 23 24 25
  [505] 26 27 14 15 16 17 18 19 20 21 22 23 24 27 28 29 30 31 32 33 34 35 36 37
  [529] 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 14 15
  [553] 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 19 20 21 22
  [577] 23 24 25 26 27 28 29 14 15 16 17 18 19 20 21 22 23 24 13 14 15 16 17 18
  [601] 19 20 21 22 23 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19
  [625] 20 21 22 12 13 14 15 16 17 18 19 20 21 22  9 10 11 12 13 14 15 16 17 18
  [649] 19 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22 14
  [673] 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 17 18 19
  [697] 20 21 22 23 24 25 26 27 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17
  [721] 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21 22 NA NA NA 18 19 20 21
  [745] 22 23 24 25 13 14 15 16 17 18 19 20 21 22 23 NA NA 13 14 15 16 17 18 19
  [769] 20 21  9 10 11 12 13 14 15 16 17 18 19 18 19 20 21 22 23 24 25 26 27 28
  [793] 17 18 19 20 21 22 23 24 25 26 27 16 17 18 19 20 21 22 23 24 25 26 18 19
  [817] 20 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23 24 25 26 27 17 18 19 20
  [841] 21 22 23 24 25 26 27 15 16 17 18 19 20 21 22 23 24 25 18 19 20 21 22 23
  [865] 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25
  [889] 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27
  [913] 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 15
  [937] 16 17 18 19 20 21 22 23 24 25 18 19 20 21 22 23 24 25 26 27 28 19 20 21
  [961] 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22
  [985] 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23
 [1009] 24 25 26 27 16 17 18 19 20 21 22 23 24 25 26 18 19 20 21 22 23 24 25 26
 [1033] 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28
 [1057] 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 20 21
 [1081] 22 23 24 25 26 27 28 29 30 20 21 22 23 24 25 26 27 28 29 30 23 24 25 26
 [1105] 27 28 29 30 31 32 33 23 24 25 26 27 28 29 30 31 32 33 22 23 24 25 26 27
 [1129] 28 29 30 31 32 21 22 23 24 25 26 27 28 29 30 31 22 23 24 25 26 27 28 29
 [1153] 30 31 32 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28
 [1177] 29 25 26 27 28 29 30 31 32 33 34 35 22 23 24 25 26 27 28 29 30 31 32 19
 [1201] 20 21 22 23 24 25 26 27 28 29 23 24 25 26 27 28 29 30 31 32 33 19 20 21
 [1225] 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 33 34 35 36 37
 [1249] 38 39 40 41 42 43 19 20 21 22 23 24 25 26 27 28 29 20 21 22 23 24 25 26
 [1273] 27 28 29 30 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26
 [1297] 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28
 [1321] 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 19 20
 [1345] 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33
 [1369] 34 35 36 37 38 39 40 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23
 [1393] 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25
 [1417] 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27
 [1441] 28 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18
 [1465] 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20
 [1489] 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22
 [1513] 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24
 [1537] 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23 24 25
 [1561] 26 27 17 18 19 20 21 22 23 24 25 26 27 23 24 25 26 27 28 29 30 31 32 33
 [1585] 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 19 20
 [1609] 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22
 [1633] 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24
 [1657] 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 17 18 19 20 21 22 23 24
 [1681] 25 26 27 17 18 19 20 21 22 23 24 25 26 27 19 20 21 22 23 24 25 26 27 28
 [1705] 29 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 17
 [1729] 18 19 20 21 22 23 24 25 26 27 19 20 21 22 23 24 25 26 27 28 29 23 24 25
 [1753] 26 27 28 29 30 31 32 33 21 22 23 24 25 26 27 28 29 30 31 23 24 25 26 27
 [1777] 28 29 30 31 32 33 21 22 23 24 25 26 27 28 29 30 31 15 16 17 18 19 20 21
 [1801] 22 23 24 25 18 19 20 21 22 23 24 25 26 27 28 24 25 26 27 28 29 30 31 32
 [1825] 33 34 18 19 20 21 22 23 24 25 26 27 28 24 25 26 27 28 29 30 31 32 33 34
 [1849] 15 16 17 18 19 20 21 22 23 24 25 15 16 17 18 19 20 21 22 23 24 25 18 19
 [1873] 20 21 22 23 24 25 26 27 28 15 16 17 18 19 20 21 22 23 24 25 15 16 17 18
 [1897] 19 20 21 22 23 24 25 17 18 19 20 21 22 23 24 25 26 27 18 19 20 21 22 23
 [1921] 24 25 26 27 28 17 18 19 20 21 22 23 24 25 26 27 15 16 17 18 19 20 21 22
 [1945] 23 24 25 15 16 17 18 19 20 21 22 23 24 25 18 19 20 21 22 23 24 25 26 27
 [1969] 28 15 16 17 18 19 20 21 22 23 24 25 18 19 20 21 22 23 24 25 26 27 28 17
 [1993] 18 19 20 21 22 23 24 25 26 27 18 19 20 21 22 23 24 25 26 27 28 18 19 20
 [2017] 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 15 16 17 18 19
 [2041] 20 21 22 23 24 25 15 16 17 18 19 20 21 22 23 24 25 18 19 20 21 22 23 24
 [2065] 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26
 [2089] 27 28 15 16 17 18 19 20 21 22 23 24 25 17 18 19 20 21 22 23 24 25 26 27
 [2113] 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19
 [2137] 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21
 [2161] 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 21 22 23 24 25 26
 [2185] 27 28 29 30 31 15 16 17 18 19 20 21 22 23 24 25 17 18 19 20 21 22 23 24
 [2209] 25 26 27 23 24 25 26 27 28 29 30 31 32 33 18 19 20 21 22 23 24 25 26 27
 [2233] 28 24 25 26 27 28 29 30 31 32 33 34 15 16 17 18 19 20 21 22 23 24 25 22
 [2257] 23 24 25 26 27 28 29 30 31 32 19 20 21 22 23 24 25 26 27 28 29 22 23 24
 [2281] 25 26 27 28 29 30 31 32 24 25 26 27 28 29 30 31 32 33 34 18 19 20 21 22
 [2305] 23 24 25 26 27 28 23 24 25 26 27 28 29 30 31 32 33 18 19 20 21 22 23 24
 [2329] 25 26 27 28 17 18 19 20 21 22 23 24 25 26 27 23 24 25 26 27 28 29 30 31
 [2353] 32 33 16 17 18 19 20 21 22 23 24 25 26 22 23 24 25 26 27 28 29 30 31 32
 [2377] 23 24 25 26 27 28 29 30 31 32 33 19 20 21 22 23 24 25 26 27 28 29 22 23
 [2401] 24 25 26 27 28 29 30 31 32 20 21 22 23 24 25 26 27 28 29 30 19 20 21 22
 [2425] 23 24 25 26 27 28 29 15 16 17 18 19 20 21 22 23 24 25 23 24 25 26 27 28
 [2449] 29 30 31 32 33 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25
 [2473] 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27
 [2497] 28 17 18 19 20 21 22 23 24 25 26 27 14 15 16 17 18 19 20 21 22 23 24 19
 [2521] 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 22 23 24
 [2545] 25 26 27 28 29 30 31 32 14 15 16 17 18 19 20 21 22 23 24 19 20 21 22 23
 [2569] 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25
 [2593] 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26
 [2617] 27 28 23 24 25 26 27 28 29 30 31 32 33 19 20 21 22 23 24 25 26 27 28 29
 [2641] 14 15 16 17 18 19 20 21 22 23 24 21 22 23 24 25 26 27 28 29 30 31 18 19
 [2665] 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21
 [2689] 22 23 24 25 26 27 28 17 18 19 20 21 22 23 24 25 26 27 14 15 16 17 18 19
 [2713] 20 21 22 23 24 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21
 [2737] 22 23 24 27 28 29 30 31 32 33 34 35 36 37 14 15 16 17 18 19 20 21 22 23
 [2761] 24 18 19 20 21 22 23 24 25 26 27 28 22 23 24 25 26 27 28 29 30 31 32 18
 [2785] 19 20 21 22 23 24 25 26 27 28 22 23 24 25 26 27 28 29 30 31 32 17 18 19
 [2809] 20 21 22 23 24 25 26 27 17 18 19 20 21 22 23 24 25 26 27 18 19 20 21 22
 [2833] 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24
 [2857] 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21 22
 [2881] 23 24 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24
 [2905] 17 18 19 20 21 22 23 24 25 26 27 19 20 21 22 23 24 25 26 27 28 29 14 15
 [2929] 16 17 18 19 20 21 22 23 24 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21
 [2953] 22 23 24 25 26 27 28 15 16 17 18 19 20 21 22 23 24 25 14 15 16 17 18 19
 [2977] 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 23 24 25 26 27 28 29 30
 [3001] 31 32 33 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21 22 23
 [3025] 24 17 18 19 20 21 22 23 24 25 26 27 15 16 17 18 19 20 21 22 23 24 25 18
 [3049] 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21 22 23 24 14 15 16
 [3073] 17 18 19 20 21 22 23 24 22 23 24 25 26 27 28 29 30 31 32 14 15 16 17 18
 [3097] 19 20 21 22 23 24 17 18 19 20 21 22 23 24 25 26 27 18 19 20 21 22 23 24
 [3121] 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21 22
 [3145] 23 24 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21 22 23 24
 [3169] 14 15 16 17 18 19 20 21 22 23 24 16 17 18 19 20 21 22 23 24 25 26 19 20
 [3193] 21 22 23 24 25 26 27 28 29 14 15 16 17 18 19 20 21 22 23 24 21 22 23 24
 [3217] 25 26 27 28 29 30 31 14 15 16 17 18 19 20 21 22 23 24 18 19 20 21 22 23
 [3241] 24 25 26 27 28 14 15 16 17 18 19 20 21 22 23 24 23 24 25 26 27 28 29 30
 [3265] 31 32 33 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23
 [3289] 24 19 20 21 22 23 24 25 26 27 28 29 14 15 16 17 18 19 20 21 22 23 24 18
 [3313] 19 20 21 22 23 24 25 26 27 28 23 24 25 26 27 28 29 30 31 32 33 13 14 15
 [3337] 16 17 18 19 20 21 22 23 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18
 [3361] 19 20 21 22 23 24 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20
 [3385] 21 22 23 24 23 24 25 26 27 28 29 30 31 32 33 13 14 15 16 17 18 19 20 21
 [3409] 22 23 13 14 15 16 17 18 19 20 21 22 23 21 22 23 24 25 26 27 28 29 30 31
 [3433] 23 24 25 26 27 28 29 30 31 32 33 23 24 25 26 27 28 29 30 31 32 33 13 14
 [3457] 15 16 17 18 19 20 21 22 23 16 17 18 19 20 21 22 23 24 25 26 13 14 15 16
 [3481] 17 18 19 20 21 22 23 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19
 [3505] 20 21 22 23 24 21 22 23 24 25 26 27 28 29 30 31 18 19 20 21 22 23 24 25
 [3529] 26 27 28 18 19 20 21 22 23 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22
 [3553] 23 14 15 16 17 18 19 20 21 22 23 24 13 14 15 16 17 18 19 20 21 22 23 18
 [3577] 19 20 21 22 23 24 25 26 27 28 23 24 25 26 27 28 29 30 31 32 33 14 15 16
 [3601] 17 18 19 20 21 22 23 24 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17
 [3625] 18 19 20 21 22 23 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20
 [3649] 21 22 23 24 13 14 15 16 17 18 19 20 21 22 23 22 23 24 25 26 27 28 29 30
 [3673] 31 32 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24
 [3697] 13 14 15 16 17 18 19 20 21 22 23 17 18 19 20 21 22 23 24 25 26 27 13 14
 [3721] 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16
 [3745] 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18
 [3769] 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20
 [3793] 21 22 23 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23
 [3817] 24 14 15 16 17 18 19 20 21 22 23 24 18 19 20 21 22 23 24 25 26 27 28 13
 [3841] 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 13 14 15
 [3865] 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 18 19 20 21 22
 [3889] 23 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19
 [3913] 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 14 15 16 17 18 19 20 21 22
 [3937] 23 24 13 14 15 16 17 18 19 20 21 22 23 14 15 16 17 18 19 20 21 22 23 24
 [3961] 14 15 16 17 18 19 20 21 22 23 24 18 19 20 21 22 23 24 25 26 27 28 14 15
 [3985] 16 17 18 19 20 21 22 23 24 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16
 [4009] 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 14 15 16 17 18 19
 [4033] 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 13 14 15 16 17 18 19 20
 [4057] 21 22 23 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21
 [4081] 22 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 12
 [4105] 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 12 13 14
 [4129] 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16
 [4153] 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18
 [4177] 19 20 21 22 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20
 [4201] 21 22 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23
 [4225] 12 13 14 15 16 17 18 19 20 21 22 19 20 21 22 23 24 25 26 27 28 29 18 19
 [4249] 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15
 [4273] 16 17 18 19 20 21 22 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19
 [4297] 20 21 22 23 24 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25
 [4321] 26 27 28 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21
 [4345] 22 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 13
 [4369] 14 15 16 17 18 19 20 21 22 23 18 19 20 21 22 23 24 25 26 27 28 12 13 14
 [4393] 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17
 [4417] 18 19 20 21 22 23 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18
 [4441] 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 18 19 20 21 22 23 24 25 26
 [4465] 27 28 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22
 [4489] 17 18 19 20 21 22 23 24 25 26 27 13 14 15 16 17 18 19 20 21 22 23 12 13
 [4513] 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16
 [4537] 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17
 [4561] 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 16 17 18 19 20 21 22 23
 [4585] 24 25 26 18 19 20 21 22 23 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22
 [4609] 23 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21 22 18
 [4633] 19 20 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23 24 25 26 27 23 24 25
 [4657] 26 27 28 29 30 31 32 33 12 13 14 15 16 17 18 19 20 21 22 18 19 20 21 22
 [4681] 23 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19
 [4705] 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21
 [4729] 22 23 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21 22
 [4753] 13 14 15 16 17 18 19 20 21 22 23 19 20 21 22 23 24 25 26 27 28 29 12 13
 [4777] 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16
 [4801] 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18
 [4825] 19 20 21 22 23 17 18 19 20 21 22 23 24 25 26 27 12 13 14 15 16 17 18 19
 [4849] 20 21 22 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21
 [4873] 22 NA NA NA NA NA NA NA NA NA NA 19 NA NA NA NA NA NA NA NA NA NA 10 NA
 [4897] NA NA NA NA NA NA NA NA NA 22 NA NA NA NA NA NA NA NA NA NA 27 NA NA NA
 [4921] NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA NA NA 20 NA NA NA NA NA
 [4945] NA NA NA NA NA 18 NA NA NA NA NA NA NA NA NA NA  5 NA NA NA NA NA NA NA
 [4969] NA NA NA 29 NA NA NA NA NA NA NA NA NA NA 19 NA NA NA NA NA NA NA NA NA
 [4993] NA 14 NA NA NA NA NA NA NA NA NA NA 18 NA NA NA NA NA NA NA NA NA NA  9
 [5017] NA NA NA NA NA NA NA NA NA NA 27 NA NA NA NA NA NA NA NA NA NA 21 NA NA
 [5041] NA NA NA NA NA NA NA NA 24 NA NA NA NA NA NA NA NA NA NA 29 NA NA NA NA
 [5065] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
 [5089] NA NA NA NA 24 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
 [5113] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
 [5137] NA NA NA NA NA NA NA NA NA NA NA 29 NA NA NA NA NA NA NA NA NA NA NA NA
 [5161] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
 [5185] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
 [5209] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 31 NA NA NA NA NA NA NA
 [5233] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
 [5257] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 18
 [5281] NA NA NA NA NA NA NA NA NA NA 28 NA NA NA NA NA NA NA NA NA NA 25 NA NA
 [5305] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
 [5329] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
 [5353] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 27 NA NA NA NA NA NA NA NA
 [5377] NA NA NA NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA NA NA
 [5401] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
 [5425] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
 [5449] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
 [5473] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 22 23 24 25 26 27 28
 [5497] 29 30 31 32 NA NA NA NA NA NA NA 25 26 27 28 14 15 16 17 18 19 20 21 22
 [5521] 23 24 NA NA NA NA NA NA NA NA 25 26 27 NA NA NA NA NA NA 24 25 26 27 28
 [5545] NA NA NA NA 36 37 38 39 40 41 42 12 13 14 15 16 17 18 19 20 21 22 10 11
 [5569] 12 13 14 15 16 17 18 19 20 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13
 [5593] 14 15 16 17 18 19 20 15 16 17 18 19 20 21 22 23 24 25 11 12 13 14 15 16
 [5617] 17 18 19 20 21 19 20 21 22 23 24 25 26 27 28 29 10 11 12 13 14 15 16 17
 [5641] 18 19 20 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18 19
 [5665] 20 10 11 12 13 14 15 16 17 18 19 20 13 14 15 16 17 18 19 20 21 22 23 11
 [5689] 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18 19 20 19 20 21
 [5713] 22 23 24 25 26 27 28 29 11 12 13 14 15 16 17 18 19 20 21 13 14 15 16 17
 [5737] 18 19 20 21 22 23 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14 15 16
 [5761] 17 18 19 20 17 18 19 20 21 22 23 24 25 26 27 13 14 15 16 17 18 19 20 21
 [5785] 22 23 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18 19 20
 [5809] 10 11 12 13 14 15 16 17 18 19 20 13 14 15 16 17 18 19 20 21 22 23 14 15
 [5833] 16 17 18 19 20 21 22 23 24 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13
 [5857] 14 15 16 17 18 19 20 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15
 [5881] 16 17 18 19 20 13 14 15 16 17 18 19 20 21 22 23 10 11 12 13 14 15 16 17
 [5905] 18 19 20 13 14 15 16 17 18 19 20 21 22 23 19 20 21 22 23 24 25 26 27 28
 [5929] 29 13 14 15 16 17 18 19 20 21 22 23  9 10 11 12 13 14 15 16 17 18 19 11
 [5953] 12 13 14 15 16 17 18 19 20 21 18 19 20 21 22 23 24 25 26 27 28 10 11 12
 [5977] 13 14 15 16 17 18 19 20 11 12 13 14 15 16 17 18 19 20 21 11 12 13 14 15
 [6001] 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18 19 20 20 21 22 23 24 25 26
 [6025] 27 28 29 30 18 19 20 21 22 23 24 25 26 27 28 10 11 12 13 14 15 16 17 18
 [6049] 19 20 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16 17 18 19 20
 [6073] 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16 17 18 19 20 10 11
 [6097] 12 13 14 15 16 17 18 19 20 17 18 19 20 21 22 23 24 25 26 27  9 10 11 12
 [6121] 13 14 15 16 17 18 19  9 10 11 12 13 14 15 16 17 18 19  9 10 11 12 13 14
 [6145] 15 16 17 18 19 12 13 14 15 16 17 18 19 20 21 22  9 10 11 12 13 14 15 16
 [6169] 17 18 19 19 20 21 22 23 24 25 26 27 28 29 11 12 13 14 15 16 17 18 19 20
 [6193] 21 10 11 12 13 14 15 16 17 18 19 20 13 14 15 16 17 18 19 20 21 22 23 17
 [6217] 18 19 20 21 22 23 24 25 26 27 10 11 12 13 14 15 16 17 18 19 20 12 13 14
 [6241] 15 16 17 18 19 20 21 22  9 10 11 12 13 14 15 16 17 18 19 10 11 12 13 14
 [6265] 15 16 17 18 19 20 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16
 [6289] 17 18 19 20 10 11 12 13 14 15 16 17 18 19 20 11 12 13 14 15 16 17 18 19
 [6313] 20 21 11 12 13 14 15 16 17 18 19 20 21 16 17 18 19 20 21 22 23 24 25 26
 [6337] 12 13 14 15 16 17 18 19 20 21 22  7  8  9 10 11 12 13 14 15 16 17 11 12
 [6361] 13 14 15 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19 20 21 21 22 23 24
 [6385] 25 26 27 28 29 30 31 10 11 12 13 14 15 16 17 18 19 20 18 19 20 21 22 23
 [6409] 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22  9 10 11 12 13 14 15 16
 [6433] 17 18 19  8  9 10 11 12 13 14 15 16 17 18 10 11 12 13 14 15 16 17 18 19
 [6457] 20 10 11 12 13 14 15 16 17 18 19 20 17 18 19 20 21 22 23 24 25 26 27 19
 [6481] 20 21 22 23 24 25 26 27 28 29 14 15 16 17 18 19 20 21 22 23 24 16 17 18
 [6505] 19 20 21 22 23 24 25 26 13 14 15 16 17 18 19 20 21 22 23 10 11 12 13 14
 [6529] 15 16 17 18 19 20 11 12 13 14 15 16 17 18 19 20 21 14 15 16 17 18 19 20
 [6553] 21 22 23 24  8  9 10 11 12 13 14 15 16 17 18 10 11 12 13 14 15 16 17 18
 [6577] 19 20 10 11 12 13 14 15 16 17 18 19 20 19 20 21 22 23 24 25 26 27 28 29
 [6601]  7  8  9 10 11 12 13 14 15 16 17 11 12 13 14 15 16 17 18 19 20 21 11 12
 [6625] 13 14 15 16 17 18 19 20 21  9 10 11 12 13 14 15 16 17 18 19 10 11 12 13
 [6649] 14 15 16 17 18 19 20 15 16 17 18 19 20 21 22 23 24 25  8  9 10 11 12 13
 [6673] 14 15 16 17 18 11 12 13 14 15 16 17 18 19 20 21  8  9 10 11 12 13 14 15
 [6697] 16 17 18 16 17 18 19 20 21 22 23 24 25 26 14 15 16 17 18 19 20 21 22 23
 [6721] 24 10 11 12 13 14 15 16 17 18 19 20  9 10 11 12 13 14 15 16 17 18 19 10
 [6745] 11 12 13 14 15 16 17 18 19 20 22 23 24 25 26 27 28 29 30 31 32 11 12 13
 [6769] 14 15 16 17 18 19 20 21  7  8  9 10 11 12 13 14 15 16 17  9 10 11 12 13
 [6793] 14 15 16 17 18 19  9 10 11 12 13 14 15 16 17 18 19 10 11 12 13 14 15 16
 [6817] 17 18 19 20  9 10 11 12 13 14 15 16 17 18 19 15 16 17 18 19 20 21 22 23
 [6841] 24 25 11 12 13 14 15 16 17 18 19 20 21  9 10 11 12 13 14 15 16 17 18 19
 [6865] 13 14 15 16 17 18 19 20 21 22 23 10 11 12 13 14 15 16 17 18 19 20 11 12
 [6889] 13 14 15 16 17 18 19 20 21 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15
 [6913] 16 17 18 19 20 21 22 10 11 12 13 14 15 16 17 18 19 20 12 13 14 15 16 17
 [6937] 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 10 11 12 13 14 15 16 17
 [6961] 18 19 20 16 17 18 19 20 21 22 23 24 25 26  7  8  9 10 11 12 13 14 15 16
 [6985] 17 23 24 25 26 27 28 29 30 31 32 33 10 11 12 13 14 15 16 17 18 19 20  6
 [7009]  7  8  9 10 11 12 13 14 15 16 13 14 15 16 17 18 19 20 21 22 23  6  7  8
 [7033]  9 10 11 12 13 14 15 16 15 16 17 18 19 20 21 22 23 24 25 10 11 12 13 14
 [7057] 15 16 17 18 19 20 26 27 28 29 30 31 32 33 34 35 36 14 15 16 17 18 19 20
 [7081] 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 10 11 12 13 14 15 16 17 18
 [7105] 19 20  8  9 10 11 12 13 14 15 16 17 18 10 11 12 13 14 15 16 17 18 19 20
 [7129] 11 12 13 14 15 16 17 18 19 20 21  9 10 11 12 13 14 15 16 17 18 19 11 12
 [7153] 13 14 15 16 17 18 19 20 21 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16
 [7177] 17 18 19 20 21 22 23 10 11 12 13 14 15 16 17 18 19 20 11 12 13 14 15 16
 [7201] 17 18 19 20 21 17 18 19 20 21 22 23 24 25 26 27 15 16 17 18 19 20 21 22
 [7225] 23 24 25 13 14 15 16 17 18 19 20 21 22 23 22 23 24 25 26 27 28 29 30 31
 [7249] 32  7  8  9 10 11 12 13 14 15 16 17  8  9 10 11 12 13 14 15 16 17 18  9
 [7273] 10 11 12 13 14 15 16 17 18 19 16 17 18 19 20 21 22 23 24 25 26  5  6  7
 [7297]  8  9 10 11 12 13 14 15 17 18 19 20 21 22 23 24 25 26 27 11 12 13 14 15
 [7321] 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19 20 21  8  9 10 11 12 13 14
 [7345] 15 16 17 18  6  7  8  9 10 11 12 13 14 15 16 10 11 12 13 14 15 16 17 18
 [7369] 19 20  9 10 11 12 13 14 15 16 17 18 19 10 11 12 13 14 15 16 17 18 19 20
 [7393]  8  9 10 11 12 13 14 15 16 17 18 16 17 18 19 20 21 22 23 24 25 26 10 11
 [7417] 12 13 14 15 16 17 18 19 20 20 21 22 23 24 25 26 27 28 29 30 10 11 12 13
 [7441] 14 15 16 17 18 19 20  5  6  7  8  9 10 11 12 13 14 15  9 10 11 12 13 14
 [7465] 15 16 17 18 19  7  8  9 10 11 12 13 14 15 16 17 19 20 21 22 23 24 25 26
 [7489] 27 28 29 18 19 20 21 22 23 24 25 26 27 28  7  8  9 10 11 12 13 14 15 16
 [7513] 17 11 12 13 14 15 16 17 18 19 20 21  8  9 10 11 12 13 14 15 16 17 18 17
 [7537] 18 19 20 21 22 23 24 25 26 27  9 10 11 12 13 14 15 16 17 18 19  5  6  7
 [7561]  8  9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 11 12 13 14 15
 [7585] 16 17 18 19 20 21 15 16 17 18 19 20 21 22 23 24 25 13 14 15 16 17 18 19
 [7609] 20 21 22 23 18 19 20 21 22 23 24 25 26 27 28 22 23 24 25 26 27 28 29 30
 [7633] 31 32 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16 17 18 19 20
 [7657] 18 19 20 21 22 23 24 25 26 27 28  6  7  8  9 10 11 12 13 14 15 16 12 13
 [7681] 14 15 16 17 18 19 20 21 22 10 11 12 13 14 15 16 17 18 19 20 17 18 19 20
 [7705] 21 22 23 24 25 26 27 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15
 [7729] 16 17 18 19 20 18 19 20 21 22 23 24 25 26 27 28 10 11 12 13 14 15 16 17
 [7753] 18 19 20 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16 17 18 19
 [7777] 20 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 13
 [7801] 14 15 16 17 18 19 20 21 22 23  9 10 11 12 13 14 15 16 17 18 19 26 27 28
 [7825] 29 30 31 32 33 34 35 36 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14
 [7849] 15 16 17 18 19 20  6  7  8  9 10 11 12 13 14 15 16 10 11 12 13 14 15 16
 [7873] 17 18 19 20  8  9 10 11 12 13 14 15 16 17 18  4  5  6  7  8  9 10 11 12
 [7897] 13 14 10 11 12 13 14 15 16 17 18 19 20 15 16 17 18 19 20 21 22 23 24 25
 [7921] 10 11 12 13 14 15 16 17 18 19 20  4  5  6  7  8  9 10 11 12 13 14  9 10
 [7945] 11 12 13 14 15 16 17 18 19  9 10 11 12 13 14 15 16 17 18 19  6  7  8  9
 [7969] 10 11 12 13 14 15 16 13 14 15 16 17 18 19 20 21 22 23  4  5  6  7  8  9
 [7993] 10 11 12 13 14 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19
 [8017] 20 21 22 11 12 13 14 15 16 17 18 19 20 21  7  8  9 10 11 12 13 14 15 16
 [8041] 17  4  5  6  7  8  9 10 11 12 13 14 14 15 16 17 18 19 20 21 22 23 24  4
 [8065]  5  6  7  8  9 10 11 12 13 14  9 10 11 12 13 14 15 16 17 18 19 17 18 19
 [8089] 20 21 22 23 24 25 26 27 16 17 18 19 20 21 22 23 24 25 26  9 10 11 12 13
 [8113] 14 15 16 17 18 19 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16
 [8137] 17 18 19 20  9 10 11 12 13 14 15 16 17 18 19  4  5  6  7  8  9 10 11 12
 [8161] 13 14  5  6  7  8  9 10 11 12 13 14 15  4  5  6  7  8  9 10 11 12 13 14
 [8185]  9 10 11 12 13 14 15 16 17 18 19 11 12 13 14 15 16 17 18 19 20 21  7  8
 [8209]  9 10 11 12 13 14 15 16 17  5  6  7  8  9 10 11 12 13 14 15 10 11 12 13
 [8233] 14 15 16 17 18 19 20  7  8  9 10 11 12 13 14 15 16 17 23 24 25 26 27 28
 [8257] 29 30 31 32 33 11 12 13 14 15 16 17 18 19 20 21 18 19 20 21 22 23 24 25
 [8281] 26 27 28  4  5  6  7  8  9 10 11 12 13 14 16 17 18 19 20 21 22 23 24 25
 [8305] 26  4  5  6  7  8  9 10 11 12 13 14  6  7  8  9 10 11 12 13 14 15 16 10
 [8329] 11 12 13 14 15 16 17 18 19 20 12 13 14 15 16 17 18 19 20 21 22 11 12 13
 [8353] 14 15 16 17 18 19 20 21  4  5  6  7  8  9 10 11 12 13 14  6  7  8  9 10
 [8377] 11 12 13 14 15 16  7  8  9 10 11 12 13 14 15 16 17  6  7  8  9 10 11 12
 [8401] 13 14 15 16 13 14 15 16 17 18 19 20 21 22 23 11 12 13 14 15 16 17 18 19
 [8425] 20 21  9 10 11 12 13 14 15 16 17 18 19 13 14 15 16 17 18 19 20 21 22 23
 [8449] 14 15 16 17 18 19 20 21 22 23 24  5  6  7  8  9 10 11 12 13 14 15  7  8
 [8473]  9 10 11 12 13 14 15 16 17 10 11 12 13 14 15 16 17 18 19 20 15 16 17 18
 [8497] 19 20 21 22 23 24 25  7  8  9 10 11 12 13 14 15 16 17 18 19 20 21 22 23
 [8521] 24 25 26 27 28 10 11 12 13 14 15 16 17 18 19 20  9 10 11 12 13 14 15 16
 [8545] 17 18 19  7  8  9 10 11 12 13 14 15 16 17 13 14 15 16 17 18 19 20 21 22
 [8569] 23  4  5  6  7  8  9 10 11 12 13 14 11 12 13 14 15 16 17 18 19 20 21 18
 [8593] 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21 22 23 24  4  5  6
 [8617]  7  8  9 10 11 12 13 14 12 13 14 15 16 17 18 19 20 21 22  4  5  6  7  8
 [8641]  9 10 11 12 13 14  9 10 11 12 13 14 15 16 17 18 19  9 10 11 12 13 14 15
 [8665] 16 17 18 19  4  5  6  7  8  9 10 11 12 13 14  4  5  6  7  8  9 10 11 12
 [8689] 13 14  4  5  6  7  8  9 10 11 12 13 14  6  7  8  9 10 11 12 13 14 15 16
 [8713] 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16 17 18 19 20 13 14
 [8737] 15 16 17 18 19 20 21 22 23  5  6  7  8  9 10 11 12 13 14 15 17 18 19 20
 [8761] 21 22 23 24 25 26 27 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14 15
 [8785] 16 17 18 19 20  4  5  6  7  8  9 10 11 12 13 14  9 10 11 12 13 14 15 16
 [8809] 17 18 19 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21
 [8833] 22 10 11 12 13 14 15 16 17 18 19 20  9 10 11 12 13 14 15 16 17 18 19  4
 [8857]  5  6  7  8  9 10 11 12 13 14 19 20 21 22 23 24 25 26 27 28 29  4  5  6
 [8881]  7  8  9 10 11 12 13 14 14 15 16 17 18 19 20 21 22 23 24  8  9 10 11 12
 [8905] 13 14 15 16 17 18 10 11 12 13 14 15 16 17 18 19 20  4  5  6  7  8  9 10
 [8929] 11 12 13 14 12 13 14 15 16 17 18 19 20 21 22 10 11 12 13 14 15 16 17 18
 [8953] 19 20  6  7  8  9 10 11 12 13 14 15 16 15 16 17 18 19 20 21 22 23 24 25
 [8977]  9 10 11 12 13 14 15 16 17 18 19  9 10 11 12 13 14 15 16 17 18 19  4  5
 [9001]  6  7  8  9 10 11 12 13 14  9 10 11 12 13 14 15 16 17 18 19 12 13 14 15
 [9025] 16 17 18 19 20 21 22  4  5  6  7  8  9 10 11 12 13 14  4  5  6  7  8  9
 [9049] 10 11 12 13 14 17 18 19 20 21 22 23 24 25 26 27 14 15 16 17 18 19 20 21
 [9073] 22 23 24 10 11 12 13 14 15 16 17 18 19 20  8  9 10 11 12 13 14 15 16 17
 [9097] 18 12 13 14 15 16 17 18 19 20 21 22 11 12 13 14 15 16 17 18 19 20 21 18
 [9121] 19 20 21 22 23 24 25 26 27 28  5  6  7  8  9 10 11 12 13 14 15 12 13 14
 [9145] 15 16 17 18 19 20 21 22  4  5  6  7  8  9 10 11 12 13 14 11 12 13 14 15
 [9169] 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18 19 20 11 12 13 14 15 16 17
 [9193] 18 19 20 21  5  6  7  8  9 10 11 12 13 14 15  9 10 11 12 13 14 15 16 17
 [9217] 18 19  7  8  9 10 11 12 13 14 15 16 17 13 14 15 16 17 18 19 20 21 22 23
 [9241] 12 13 14 15 16 17 18 19 20 21 22  5  6  7  8  9 10 11 12 13 14 15  4  5
 [9265]  6  7  8  9 10 11 12 13 14  4  5  6  7  8  9 10 11 12 13 14 22 23 24 25
 [9289] 26 27 28 29 30 31 32  4  5  6  7  8  9 10 11 12 13 14 18 19 20 21 22 23
 [9313] 24 25 26 27 28  4  5  6  7  8  9 10 11 12 13 14  4  5  6  7  8  9 10 11
 [9337] 12 13 14  4  5  6  7  8  9 10 11 12 13 14  8  9 10 11 12 13 14 15 16 17
 [9361] 18 12 13 14 15 16 17 18 19 20 21 22 17 18 19 20 21 22 23 24 25 26 27  9
 [9385] 10 11 12 13 14 15 16 17 18 19 11 12 13 14 15 16 17 18 19 20 21  5  6  7
 [9409]  8  9 10 11 12 13 14 15  9 10 11 12 13 14 15 16 17 18 19  6  7  8  9 10
 [9433] 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 10 11 12 13 14 15 16
 [9457] 17 18 19 20  5  6  7  8  9 10 11 12 13 14 15  4  5  6  7  8  9 10 11 12
 [9481] 13 14  4  5  6  7  8  9 10 11 12 13 14  9 10 11 12 13 14 15 16 17 18 19
 [9505]  7  8  9 10 11 12 13 14 15 16 17 10 11 12 13 14 15 16 17 18 19 20 10 11
 [9529] 12 13 14 15 16 17 18 19 20  4  5  6  7  8  9 10 11 12 13 14  4  5  6  7
 [9553]  8  9 10 11 12 13 14 16 17 18 19 20 21 22 23 24 25 26  4  5  6  7  8  9
 [9577] 10 11 12 13 14  9 10 11 12 13 14 15 16 17 18 19  9 10 11 12 13 14 15 16
 [9601] 17 18 19 11 12 13 14 15 16 17 18 19 20 21  4  5  6  7  8  9 10 11 12 13
 [9625] 14 18 19 20 21 22 23 24 25 26 27 28 10 11 12 13 14 15 16 17 18 19 20  4
 [9649]  5  6  7  8  9 10 11 12 13 14  4  5  6  7  8  9 10 11 12 13 14 13 14 15
 [9673] 16 17 18 19 20 21 22 23  4  5  6  7  8  9 10 11 12 13 14 17 18 19 20 21
 [9697] 22 23 24 25 26 27 13 14 15 16 17 18 19 20 21 22 23  8  9 10 11 12 13 14
 [9721] 15 16 17 18  5  6  7  8  9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24
 [9745] 25 26 10 11 12 13 14 15 16 17 18 19 20  4  5  6  7  8  9 10 11 12 13 14
 [9769] 10 11 12 13 14 15 16 17 18 19 20  9 10 11 12 13 14 15 16 17 18 19  9 10
 [9793] 11 12 13 14 15 16 17 18 19 15 16 17 18 19 20 21 22 23 24 25  6  7  8  9
 [9817] 10 11 12 13 14 15 16  7  8  9 10 11 12 13 14 15 16 17 13 14 15 16 17 18
 [9841] 19 20 21 22 23 30 31 32 33 34 35 36 37 38 39 40 10 11 12 13 14 15 16 17
 [9865] 18 19 20  4  5  6  7  8  9 10 11 12 13 14 18 19 20 21 22 23 24 25 26 27
 [9889] 28 12 13 14 15 16 17 18 19 20 21 22  3  4  5  6  7  8  9 10 11 12 13  4
 [9913]  5  6  7  8  9 10 11 12 13 14  7  8  9 10 11 12 13 14 15 16 17  4  5  6
 [9937]  7  8  9 10 11 12 13 14 14 15 16 17 18 19 20 21 22 23 24  4  5  6  7  8
 [9961]  9 10 11 12 13 14  4  5  6  7  8  9 10 11 12 13 14 13 14 15 16 17 18 19
 [9985] 20 21 22 23  9 10 11 12 13 14 15 16 17 18 19  7  8  9 10 11 12 13 14 15
[10009] 16 17 10 11 12 13 14 15 16 17 18 19 20  4  5  6  7  8  9 10 11 12 13 14
[10033]  7  8  9 10 11 12 13 14 15 16 17  4  5  6  7  8  9 10 11 12 13 14  4  5
[10057]  6  7  8  9 10 11 12 13 14  4  5  6  7  8  9 10 11 12 13 14 11 12 13 14
[10081] 15 16 17 18 19 20 21  9 10 11 12 13 14 15 16 17 18 19  7  8  9 10 11 12
[10105] 13 14 15 16 17 12 13 14 15 16 17 18 19 20 21 22 17 18 19 20 21 22 23 24
[10129] 25 26 27  3  4  5  6  7  8  9 10 11 12 13  8  9 10 11 12 13 14 15 16 17
[10153] 18 13 14 15 16 17 18 19 20 21 22 23  4  5  6  7  8  9 10 11 12 13 14  9
[10177] 10 11 12 13 14 15 16 17 18 19  3  4  5  6  7  8  9 10 11 12 13 16 17 18
[10201] 19 20 21 22 23 24 25 26  4  5  6  7  8  9 10 11 12 13 14  8  9 10 11 12
[10225] 13 14 15 16 17 18 12 13 14 15 16 17 18 19 20 21 22  6  7  8  9 10 11 12
[10249] 13 14 15 16 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18
[10273] 19 20  3  4  5  6  7  8  9 10 11 12 13  7  8  9 10 11 12 13 14 15 16 17
[10297] 11 12 13 14 15 16 17 18 19 20 21  3  4  5  6  7  8  9 10 11 12 13  9 10
[10321] 11 12 13 14 15 16 17 18 19 10 11 12 13 14 15 16 17 18 19 20  3  4  5  6
[10345]  7  8  9 10 11 12 13  4  5  6  7  8  9 10 11 12 13 14 21 22 23 24 25 26
[10369] 27 28 29 30 31 30 31 32 33 34 35 36 37 38 39 40 14 15 16 17 18 19 20 21
[10393] 22 23 24  5  6  7  8  9 10 11 12 13 14 15  4  5  6  7  8  9 10 11 12 13
[10417] 14  3  4  5  6  7  8  9 10 11 12 13  9 10 11 12 13 14 15 16 17 18 19  4
[10441]  5  6  7  8  9 10 11 12 13 14 12 13 14 15 16 17 18 19 20 21 22  4  5  6
[10465]  7  8  9 10 11 12 13 14  3  4  5  6  7  8  9 10 11 12 13  4  5  6  7  8
[10489]  9 10 11 12 13 14  9 10 11 12 13 14 15 16 17 18 19  8  9 10 11 12 13 14
[10513] 15 16 17 18  8  9 10 11 12 13 14 15 16 17 18  4  5  6  7  8  9 10 11 12
[10537] 13 14  4  5  6  7  8  9 10 11 12 13 14  8  9 10 11 12 13 14 15 16 17 18
[10561] 10 11 12 13 14 15 16 17 18 19 20  4  5  6  7  8  9 10 11 12 13 14 13 14
[10585] 15 16 17 18 19 20 21 22 23  3  4  5  6  7  8  9 10 11 12 13  6  7  8  9
[10609] 10 11 12 13 14 15 16  4  5  6  7  8  9 10 11 12 13 14  7  8  9 10 11 12
[10633] 13 14 15 16 17 15 16 17 18 19 20 21 22 23 24 25  3  4  5  6  7  8  9 10
[10657] 11 12 13  3  4  5  6  7  8  9 10 11 12 13 10 11 12 13 14 15 16 17 18 19
[10681] 20 19 20 21 22 23 24 25 26 27 28 29  3  4  5  6  7  8  9 10 11 12 13  8
[10705]  9 10 11 12 13 14 15 16 17 18  9 10 11 12 13 14 15 16 17 18 19  3  4  5
[10729]  6  7  8  9 10 11 12 13 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14
[10753] 15 16 17 18 19 20 15 16 17 18 19 20 21 22 23 24 25  4  5  6  7  8  9 10
[10777] 11 12 13 14 10 11 12 13 14 15 16 17 18 19 20 12 13 14 15 16 17 18 19 20
[10801] 21 22  3  4  5  6  7  8  9 10 11 12 13  4  5  6  7  8  9 10 11 12 13 14
[10825]  2  3  4  5  6  7  8  9 10 11 12  9 10 11 12 13 14 15 16 17 18 19 12 13
[10849] 14 15 16 17 18 19 20 21 22 10 11 12 13 14 15 16 17 18 19 20  4  5  6  7
[10873]  8  9 10 11 12 13 14  4  5  6  7  8  9 10 11 12 13 14 23 24 25 26 27 28
[10897] 29 30 31 32 33 10 11 12 13 14 15 16 17 18 19 20 14 15 16 17 18 19 20 21
[10921] 22 23 24  5  6  7  8  9 10 11 12 13 14 15 13 14 15 16 17 18 19 20 21 22
[10945] 23 14 15 16 17 18 19 20 21 22 23 24  8  9 10 11 12 13 14 15 16 17 18 23
[10969] 24 25 26 27 28 29 30 31 32 33 11 12 13 14 15 16 17 18 19 20 21 15 16 17
[10993] 18 19 20 21 22 23 24 25 12 13 14 15 16 17 18 19 20 21 22 18 19 20 21 22
[11017] 23 24 25 26 27 28 35 36 37 38 39 40 41 42 43 44 45 10 11 12 13 14 15 16
[11041] 17 18 19 20 17 18 19 20 21 22 23 24 25 26 27 11 12 13 14 15 16 17 18 19
[11065] 20 21 11 12 13 14 15 16 17 18 19 20 21 18 19 20 21 22 23 24 25 26 27 28
[11089] 11 12 13 14 15 16 17 18 19 20 21 12 13 14 15 16 17 18 19 20 21 22 10 11
[11113] 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16 17 18 19 20 12 13 14 15
[11137] 16 17 18 19 20 21 22 10 11 12 13 14 15 16 17 18 19 20 12 13 14 15 16 17
[11161] 18 19 20 21 22 16 17 18 19 20 21 22 23 24 25 26  3  4  5  6  7  8  9 10
[11185] 11 12 13  8  9 10 11 12 13 14 15 16 17 18 23 24 25 26 27 28 29 30 31 32
[11209] 33  6  7  8  9 10 11 12 13 14 15 16 14 15 16 17 18 19 20 21 22 23 24  7
[11233]  8  9 10 11 12 13 14 15 16 17 10 11 12 13 14 15 16 17 18 19 20  9 10 11
[11257] 12 13 14 15 16 17 18 19 10 11 12 13 14 15 16 17 18 19 20  7  8  9 10 11
[11281] 12 13 14 15 16 17 14 15 16 17 18 19 20 21 22 23 24 16 17 18 19 20 21 22
[11305] 23 24 25 26 10 11 12 13 14 15 16 17 18 19 20  9 10 11 12 13 14 15 16 17
[11329] 18 19 10 11 12 13 14 15 16 17 18 19 20  8  9 10 11 12 13 14 15 16 17 18
[11353] 17 18 19 20 21 22 23 24 25 26 27 15 16 17 18 19 20 21 22 23 24 25  8  9
[11377] 10 11 12 13 14 15 16 17 18 10 11 12 13 14 15 16 17 18 19 20 12 13 14 15
[11401] 16 17 18 19 20 21 22 20 21 22 23 24 25 26 27 28 29 30  8  9 10 11 12 13
[11425] 14 15 16 17 18 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19
[11449] 20 21 22 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22
[11473] 23 16 17 18 19 20 21 22 23 24 25 26  7  8  9 10 11 12 13 14 15 16 17 10
[11497] 11 12 13 14 15 16 17 18 19 20 14 15 16 17 18 19 20 21 22 23 24 11 12 13
[11521] 14 15 16 17 18 19 20 21  9 10 11 12 13 14 15 16 17 18 19 18 19 20 21 22
[11545] 23 24 25 26 27 28 10 11 12 13 14 15 16 17 18 19 20  9 10 11 12 13 14 15
[11569] 16 17 18 19 10 11 12 13 14 15 16 17 18 19 20  7  8  9 10 11 12 13 14 15
[11593] 16 17 14 15 16 17 18 19 20 21 22 23 24 19 20 21 22 23 24 25 26 27 28 29
[11617] 14 15 16 17 18 19 20 21 22 23 24  6  7  8  9 10 11 12 13 14 15 16 12 13
[11641] 14 15 16 17 18 19 20 21 22  9 10 11 12 13 14 15 16 17 18 19  8  9 10 11
[11665] 12 13 14 15 16 17 18 10 11 12 13 14 15 16 17 18 19 20  9 10 11 12 13 14
[11689] 15 16 17 18 19 15 16 17 18 19 20 21 22 23 24 25 23 24 25 26 27 28 29 30
[11713] 31 32 33  9 10 11 12 13 14 15 16 17 18 19  4  5  6  7  8  9 10 11 12 13
[11737] 14 11 12 13 14 15 16 17 18 19 20 21 13 14 15 16 17 18 19 20 21 22 23 11
[11761] 12 13 14 15 16 17 18 19 20 21 14 15 16 17 18 19 20 21 22 23 24 18 19 20
[11785] 21 22 23 24 25 26 27 28 10 11 12 13 14 15 16 17 18 19 20 14 15 16 17 18
[11809] 19 20 21 22 23 24 23 24 25 26 27 28 29 30 31 32 33 18 19 20 21 22 23 24
[11833] 25 26 27 28 13 14 15 16 17 18 19 20 21 22 23  7  8  9 10 11 12 13 14 15
[11857] 16 17 12 13 14 15 16 17 18 19 20 21 22 17 18 19 20 21 22 23 24 25 26 27
[11881]  7  8  9 10 11 12 13 14 15 16 17 16 17 18 19 20 21 22 23 24 25 26  9 10
[11905] 11 12 13 14 15 16 17 18 19 16 17 18 19 20 21 22 23 24 25 26  9 10 11 12
[11929] 13 14 15 16 17 18 19  5  6  7  8  9 10 11 12 13 14 15  5  6  7  8  9 10
[11953] 11 12 13 14 15 11 12 13 14 15 16 17 18 19 20 21  3  4  5  6  7  8  9 10
[11977] 11 12 13 13 14 15 16 17 18 19 20 21 22 23  3  4  5  6  7  8  9 10 11 12
[12001] 13 10 11 12 13 14 15 16 17 18 19 20 15 16 17 18 19 20 21 22 23 24 25  8
[12025]  9 10 11 12 13 14 15 16 17 18 12 13 14 15 16 17 18 19 20 21 22  8  9 10
[12049] 11 12 13 14 15 16 17 18  4  5  6  7  8  9 10 11 12 13 14  5  6  7  8  9
[12073] 10 11 12 13 14 15 15 16 17 18 19 20 21 22 23 24 25  5  6  7  8  9 10 11
[12097] 12 13 14 15 11 12 13 14 15 16 17 18 19 20 21 15 16 17 18 19 20 21 22 23
[12121] 24 25 15 16 17 18 19 20 21 22 23 24 25 18 19 20 21 22 23 24 25 26 27 28
[12145] 22 23 24 25 26 27 28 29 30 31 32 19 20 21 22 23 24 25 26 27 28 29  8  9
[12169] 10 11 12 13 14 15 16 17 18 13 14 15 16 17 18 19 20 21 22 23  5  6  7  8
[12193]  9 10 11 12 13 14 15 12 13 14 15 16 17 18 19 20 21 22 10 11 12 13 14 15
[12217] 16 17 18 19 20 14 15 16 17 18 19 20 21 22 23 24 13 14 15 16 17 18 19 20
[12241] 21 22 23 23 24 25 26 27 28 29 30 31 32 33  5  6  7  8  9 10 11 12 13 14
[12265] 15  7  8  9 10 11 12 13 14 15 16 17  7  8  9 10 11 12 13 14 15 16 17  4
[12289]  5  6  7  8  9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 18 19 20
[12313] 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22 11 12 13 14 15
[12337] 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19 20 21 17 18 19 20 21 22 23
[12361] 24 25 26 27 11 12 13 14 15 16 17 18 19 20 21  8  9 10 11 12 13 14 15 16
[12385] 17 18 18 19 20 21 22 23 24 25 26 27 28 10 11 12 13 14 15 16 17 18 19 20
[12409] 16 17 18 19 20 21 22 23 24 25 26 11 12 13 14 15 16 17 18 19 20 21  7  8
[12433]  9 10 11 12 13 14 15 16 17  6  7  8  9 10 11 12 13 14 15 16 13 14 15 16
[12457] 17 18 19 20 21 22 23  6  7  8  9 10 11 12 13 14 15 16 16 17 18 19 20 21
[12481] 22 23 24 25 26 15 16 17 18 19 20 21 22 23 24 25 12 13 14 15 16 17 18 19
[12505] 20 21 22  9 10 11 12 13 14 15 16 17 18 19  6  7  8  9 10 11 12 13 14 15
[12529] 16  9 10 11 12 13 14 15 16 17 18 19 10 11 12 13 14 15 16 17 18 19 20 NA
[12553] 10 11 12 13 14 15 16 17 18 19 NA  9 10 11 12 13 14 15 16 17 18 NA 11 12
[12577] 13 14 15 16 17 18 19 20 NA 13 14 15 16 17 18 19 20 21 22 NA  6  7  8  9
[12601] 10 11 12 13 14 15 NA 15 16 17 18 19 20 21 22 23 24 NA 10 11 12 13 14 15
[12625] 16 17 18 19 NA 17 18 19 20 21 22 23 24 25 26 NA 22 23 24 25 26 27 28 29
[12649] 30 31 NA 11 12 13 14 15 16 17 18 19 20 NA 20 21 22 23 24 25 26 27 28 29
[12673] NA 18 19 20 21 22 23 24 25 26 27 NA 10 11 12 13 14 15 16 17 18 19 NA 24
[12697] 25 26 27 28 29 30 31 32 33 NA 17 18 19 20 21 22 23 24 25 26 NA 12 13 14
[12721] 15 16 17 18 19 20 21 NA 13 14 15 16 17 18 19 20 21 22 NA 11 12 13 14 15
[12745] 16 17 18 19 20 NA 20 21 22 23 24 25 26 27 28 29 NA 14 15 16 17 18 19 20
[12769] 21 22 23 NA 20 21 22 23 24 25 26 27 28 29 NA 11 12 13 14 15 16 17 18 19
[12793] 20 NA 45 46 47 48 49 50 51 52 53 54 NA 16 17 18 19 20 21 22 23 24 25 NA
[12817] 19 20 21 22 23 24 25 26 27 28 NA  9 10 11 12 13 14 15 16 17 18 NA  9 10
[12841] 11 12 13 14 15 16 17 18 NA  8  9 10 11 12 13 14 15 16 17 NA 17 18 19 20
[12865] 21 22 23 24 25 26 NA 11 12 13 14 15 16 17 18 19 20 NA 22 23 24 25 26 27
[12889] 28 29 30 31 NA 11 12 13 14 15 16 17 18 19 20 NA 12 13 14 15 16 17 18 19
[12913] 20 21 NA  9 10 11 12 13 14 15 16 17 18 NA 11 12 13 14 15 16 17 18 19 20
[12937] NA 12 13 14 15 16 17 18 19 20 21 NA 13 14 15 16 17 18 19 20 21 22 NA 13
[12961] 14 15 16 17 18 19 20 21 22 NA 12 13 14 15 16 17 18 19 20 21 NA 12 13 14
[12985] 15 16 17 18 19 20 21 NA 15 16 17 18 19 20 21 22 23 24 NA 10 11 12 13 14
[13009] 15 16 17 18 19 NA  6  7  8  9 10 11 12 13 14 15 NA 15 16 17 18 19 20 21
[13033] 22 23 24 NA 16 17 18 19 20 21 22 23 24 25 NA 19 20 21 22 23 24 25 26 27
[13057] 28 NA NA 18 19 20 21 22 23 24 25 26 NA NA 14 15 16 17 18 19 20 21 22 NA
[13081] NA 21 22 23 24 25 26 27 28 29 NA NA 19 20 21 22 23 24 25 26 27 NA NA 13
[13105] 14 15 16 17 18 19 20 21 NA NA 13 14 15 16 17 18 19 20 21 NA NA 17 18 19
[13129] 20 21 22 23 24 25 NA NA 13 14 15 16 17 18 19 20 21 NA NA 24 25 26 27 28
[13153] 29 30 31 32 NA NA  9 10 11 12 13 14 15 16 17 NA NA NA 16 17 18 19 20 21
[13177] 22 23 NA NA  6  7  8  9 10 11 12 13 14 NA NA 11 12 13 14 15 16 17 18 19
[13201] NA NA  6  7  8  9 10 11 12 13 14 NA NA  9 10 11 12 13 14 15 16 17 NA NA
[13225]  6  7  8  9 10 11 12 13 14 NA NA NA 19 20 21 22 23 24 25 26 NA NA 15 16
[13249] 17 18 19 20 21 22 23 NA NA NA 11 12 13 14 15 16 17 18 NA NA NA 14 15 16
[13273] 17 18 19 20 21 NA NA NA 12 13 14 15 16 17 18 19 NA NA NA 22 23 24 25 26
[13297] 27 28 29 NA NA NA 16 17 18 19 20 21 22 23 NA NA NA 10 11 12 13 14 15 16
[13321] 17 NA NA NA 12 13 14 15 16 17 18 19 NA NA NA 20 21 22 23 24 25 26 27 NA
[13345] NA NA 18 19 20 21 22 23 24 25 NA NA NA 17 18 19 20 21 22 23 24 NA NA NA
[13369] 20 21 22 23 24 25 26 27 NA NA NA  9 10 11 12 13 14 15 16 NA NA NA 15 16
[13393] 17 18 19 20 21 22 NA NA NA  9 10 11 12 13 14 15 16 NA NA NA 13 14 15 16
[13417] 17 18 19 20 NA NA NA NA 35 36 37 38 39 40 41 NA NA NA 12 13 14 15 16 17
[13441] 18 19 NA NA NA 10 11 12 13 14 15 16 17 NA NA NA 17 18 19 20 21 22 23 24
[13465] NA NA NA NA 14 15 16 17 18 19 20 NA NA NA 12 13 14 15 16 17 18 19 NA NA
[13489] NA NA 12 13 14 15 16 17 18 NA NA NA NA 15 16 17 18 19 20 21 NA NA NA 18
[13513] 19 20 21 22 23 24 25 NA NA NA NA 11 12 13 14 15 16 17 NA NA NA NA 23 24
[13537] 25 26 27 28 29 NA NA NA NA 15 16 17 18 19 20 21 NA NA NA NA  8  9 10 11
[13561] 12 13 14 NA NA NA NA 15 16 17 18 19 20 21 NA NA NA NA 19 20 21 22 23 24
[13585] 25 NA NA NA NA 16 17 18 19 20 21 22 NA NA NA NA 19 20 21 22 23 24 25 NA
[13609] NA NA NA 18 19 20 21 22 23 24 NA NA NA NA  9 10 11 12 13 14 15 NA NA NA
[13633] NA 19 20 21 22 23 24 25 NA NA NA NA  8  9 10 11 12 13 14 NA NA NA NA 19
[13657] 20 21 22 23 24 25 NA NA NA NA 18 19 20 21 22 23 24 NA NA NA NA 24 25 26
[13681] 27 28 29 30 NA NA NA NA 15 16 17 18 19 20 21 NA NA NA NA 10 11 12 13 14
[13705] 15 16 NA NA NA NA 15 16 17 18 19 20 21 NA NA NA NA 10 11 12 13 14 15 16
[13729] NA NA NA NA 19 20 21 22 23 24 25 NA NA NA NA NA NA 19 20 21 22 23 NA NA
[13753] NA NA 19 20 21 22 23 24 25 NA NA NA NA  9 10 11 12 13 14 15 NA NA NA NA
[13777]  7  8  9 10 11 12 13 NA NA NA NA 17 18 19 20 21 22 23 NA NA NA NA 18 19
[13801] 20 21 22 23 24 NA NA NA NA 14 15 16 17 18 19 20 NA NA NA NA 20 21 22 23
[13825] 24 25 26 NA NA NA NA 11 12 13 14 15 16 17 NA NA NA NA 21 22 23 24 25 26
[13849] 27 NA NA NA NA 19 20 21 22 23 24 25 NA NA NA NA 15 16 17 18 19 20 21 NA
[13873] NA NA NA 17 18 19 20 21 22 23 NA NA NA NA  7  8  9 10 11 12 13 NA NA NA
[13897] NA 21 22 23 24 25 26 27 NA NA NA NA 16 17 18 19 20 21 22 NA NA NA NA 12
[13921] 13 14 15 16 17 18 NA NA NA NA 19 20 21 22 23 24 25 NA NA NA NA NA 18 19
[13945] 20 21 22 23 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 26 27 28 29
[13969] 30 31 NA NA NA NA NA 18 19 20 21 22 23 NA NA NA NA NA 22 23 24 25 26 27
[13993] NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA NA  8  9 10 11 12 13 NA NA
[14017] NA NA NA  7  8  9 10 11 12 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA
[14041] NA 13 14 15 16 17 18 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 21
[14065] 22 23 24 25 26 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA NA 14 15 16
[14089] 17 18 19 NA NA NA NA NA 11 12 13 14 15 16 NA NA NA NA NA 24 25 26 27 28
[14113] 29 NA NA NA NA NA 22 23 24 25 26 27 NA NA NA NA NA 10 11 12 13 14 15 NA
[14137] NA NA NA NA 10 11 12 13 14 15 NA NA NA NA NA 21 22 23 24 25 26 NA NA NA
[14161] NA NA  7  8  9 10 11 12 NA NA NA NA NA 20 21 22 23 24 25 NA NA NA NA NA
[14185] 16 17 18 19 20 21 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 22 23
[14209] 24 25 26 27 NA NA NA NA NA 18 19 20 21 22 23 NA NA NA NA NA 22 23 24 25
[14233] 26 27 NA NA NA NA NA  7  8  9 10 11 12 NA NA NA NA NA 11 12 13 14 15 16
[14257] NA NA NA NA NA 11 12 13 14 15 16 NA NA NA NA NA  9 10 11 12 13 14 NA NA
[14281] NA NA NA  9 10 11 12 13 14 NA NA NA NA NA  7  8  9 10 11 12 NA NA NA NA
[14305] NA 11 12 13 14 15 16 NA NA NA NA NA 17 18 19 20 21 22 NA NA NA NA NA 14
[14329] 15 16 17 18 19 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 10 11 12
[14353] 13 14 15 NA NA NA NA NA 12 13 14 15 16 17 NA NA NA NA NA 10 11 12 13 14
[14377] 15 NA NA NA NA NA 11 12 13 14 15 16 NA NA NA NA NA 14 15 16 17 18 19 NA
[14401] NA NA NA NA 15 16 17 18 19 20 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA
[14425] NA NA 11 12 13 14 15 16 NA NA NA NA NA 19 20 21 22 23 24 NA NA NA NA NA
[14449] 13 14 15 16 17 18 NA NA NA NA NA 12 13 14 15 16 17 NA NA NA NA NA NA 11
[14473] 12 13 14 15 NA NA NA NA NA 11 12 13 14 15 16 NA NA NA NA NA 15 16 17 18
[14497] 19 20 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA 21 22 23 24 25
[14521] NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA NA NA 18 19 20 21 22 NA NA
[14545] NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA 10 11 12 13 14 NA NA NA NA
[14569] NA NA 33 34 35 36 37 NA NA NA NA NA 20 21 22 23 24 25 NA NA NA NA NA NA
[14593] 15 16 17 18 19 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA 20 21
[14617] 22 23 24 NA NA NA NA NA NA  7  8  9 10 11 NA NA NA NA NA NA 25 26 27 28
[14641] 29 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA 20 21 22 23 24 NA
[14665] NA NA NA NA NA 14 15 16 17 18 NA NA NA NA NA NA 10 11 12 13 14 NA NA NA
[14689] NA NA NA 10 11 12 13 14 NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA NA
[14713] NA 13 14 15 16 17 NA NA NA NA NA NA 18 19 20 21 22 NA NA NA NA NA NA 22
[14737] 23 24 25 26 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA 22 23 24
[14761] 25 26 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA 10 11 12 13 14
[14785] NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA 19 20 21 22 23 NA NA
[14809] NA NA NA NA 20 21 22 23 24 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA
[14833] NA NA 10 11 12 13 14 NA NA NA NA NA NA 21 22 23 24 25 NA NA NA NA NA NA
[14857] 30 31 32 33 34 NA NA NA NA NA NA 24 25 26 27 28 NA NA NA NA NA NA 21 22
[14881] 23 24 25 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA 19 20 21 22
[14905] 23 NA NA NA NA NA NA 20 21 22 23 24 NA NA NA NA NA NA 16 17 18 19 20 NA
[14929] NA NA NA NA NA 18 19 20 21 22 NA NA NA NA NA NA 15 16 17 18 19 NA NA NA
[14953] NA NA NA  7  8  9 10 11 NA NA NA NA NA NA 15 16 17 18 19 NA NA NA NA NA
[14977] NA 15 16 17 18 19 NA NA NA NA NA NA 11 12 13 14 15 NA NA NA NA NA NA 22
[15001] 23 24 25 26 NA NA NA NA NA NA 15 16 17 18 19 NA NA NA NA NA NA 13 14 15
[15025] 16 17 NA NA NA NA NA NA 21 22 23 24 25 NA NA NA NA NA NA 13 14 15 16 17
[15049] NA NA NA NA NA NA  9 10 11 12 13 NA NA NA NA NA NA 13 14 15 16 17 NA NA
[15073] NA NA NA NA 13 14 15 16 17 NA NA NA NA NA NA 24 25 26 27 28 NA NA NA NA
[15097] NA NA 21 22 23 24 25 NA NA NA NA NA NA 18 19 20 21 22 NA NA NA NA NA NA
[15121] 11 12 13 14 15 NA NA NA NA NA NA 17 18 19 20 21 NA NA NA NA NA NA 24 25
[15145] 26 27 28 NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA NA NA  9 10 11 12
[15169] 13 NA NA NA NA NA NA 17 18 19 20 21 NA NA NA NA NA NA 33 34 35 36 37 NA
[15193] NA NA NA NA NA 23 24 25 26 27 NA NA NA NA NA NA 19 20 21 22 23 NA NA NA
[15217] NA NA NA 23 24 25 26 27 NA NA NA NA NA NA 31 32 33 34 35 NA NA NA NA NA
[15241] NA 22 23 24 25 26 NA NA NA NA NA NA 18 19 20 21 22 NA NA NA NA NA NA 18
[15265] 19 20 21 22 NA NA NA NA NA NA  6  7  8  9 10 NA NA NA NA NA NA 17 18 19
[15289] 20 21 NA NA NA NA NA NA  9 10 11 12 13 NA NA NA NA NA NA NA 12 13 14 15
[15313] NA NA NA NA NA NA NA 15 16 17 18 NA NA NA NA NA NA NA  6  7  8  9 NA NA
[15337] NA NA NA NA NA 15 16 17 18 NA NA NA NA NA NA NA 10 11 12 13 NA NA NA NA
[15361] NA NA NA 21 22 23 24 NA NA NA NA NA NA NA 14 15 16 17 NA NA NA NA NA NA
[15385] NA 22 23 24 25 NA NA NA NA NA NA NA 17 18 19 20 NA NA NA NA NA NA NA 19
[15409] 20 21 22 NA NA NA NA NA NA NA 22 23 24 25 NA NA NA NA NA NA NA 17 18 19
[15433] 20 NA NA NA NA NA NA NA 19 20 21 22 NA NA NA NA NA NA NA 19 20 21 22 NA
[15457] NA NA NA NA NA NA 20 21 22 23 NA NA NA NA NA NA NA 21 22 23 24 NA NA NA
[15481] NA NA NA NA 12 13 14 15 NA NA NA NA NA NA NA  7  8  9 10 NA NA NA NA NA
[15505] NA NA 22 23 24 25 NA NA NA NA NA NA NA 25 26 27 28 NA NA NA NA NA NA NA
[15529] 16 17 18 19 NA NA NA NA NA NA NA NA 11 12 13 NA NA NA NA NA NA NA NA  7
[15553]  8  9 NA NA NA NA NA NA NA NA 10 11 12 NA NA NA NA NA NA NA NA  9 10 11
[15577] NA NA NA NA NA NA NA NA 18 19 20 NA NA NA NA NA NA NA NA 18 19 20 NA NA
[15601] NA NA NA NA NA  6  7  8  9 NA NA NA NA NA NA NA NA 13 14 15 NA NA NA NA
[15625] NA NA NA NA 18 19 20 NA NA NA NA NA NA NA NA 26 27 28 NA NA NA NA NA NA
[15649] NA NA  7  8  9 NA NA NA NA NA NA NA NA 19 20 21 NA NA NA NA NA NA NA NA
[15673] 15 16 17 NA NA NA NA NA NA NA NA 15 16 17 NA NA NA NA NA NA NA NA 17 18
[15697] 19 NA NA NA NA NA NA NA NA 21 22 23 NA NA NA NA NA NA NA NA  8  9 10 NA
[15721] NA NA NA NA NA NA NA 17 18 19 NA NA NA NA NA NA NA NA 16 17 18 NA NA NA
[15745] NA NA NA NA NA 21 22 23 NA NA NA NA NA NA NA NA 18 19 20 NA NA NA NA NA
[15769] NA NA NA 14 15 16 NA NA NA NA NA NA NA NA  7  8  9 NA NA NA NA NA NA NA
[15793] NA NA 15 16 NA NA NA NA NA NA NA NA NA 26 27 NA NA NA NA NA NA NA NA NA
[15817] 13 14 NA NA NA NA NA NA NA NA NA 29 30 NA NA NA NA NA NA NA NA NA 15 16
[15841] NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA  8  9 NA NA
[15865] NA NA NA NA NA NA NA 13 14 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA
[15889] NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA
[15913] NA NA NA 16 17 NA NA NA NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA NA
[15937] NA 23 24 NA NA NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA 14
[15961] 15 NA NA NA NA NA NA NA NA NA  8  9 NA NA NA NA NA NA NA NA NA 15 16 NA
[15985] NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA 18 19 NA NA NA
[16009] NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA
[16033] NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA 12 13 NA NA NA NA NA NA NA
[16057] NA NA 26 27 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA
[16081] 26 27 NA NA NA NA NA NA NA NA NA 23 24 NA NA NA NA NA NA NA NA NA 10 11
[16105] NA NA NA NA NA NA NA NA NA 12 13 NA NA NA NA NA NA NA NA NA  7  8 NA NA
[16129] NA NA NA NA NA NA NA 18 19 NA NA NA NA NA NA NA NA NA  9 10 NA NA NA NA
[16153] NA NA NA NA NA  7  8 NA NA NA NA NA NA NA NA NA  9 10 NA NA NA NA NA NA
[16177] NA NA NA 27 28 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA
[16201] NA 14 15 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA 19
[16225] 20 NA NA NA NA NA NA NA NA NA  5  6 NA NA NA NA NA NA NA NA NA 18 19 NA
[16249] NA NA NA NA NA NA NA NA 19 20 NA NA NA NA NA NA NA NA NA 11 12 NA NA NA
[16273] NA NA NA NA NA NA 26 27 NA NA NA NA NA NA NA NA NA 20 21 NA NA NA NA NA
[16297] NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA
[16321] NA NA 21 22 NA NA NA NA NA NA NA NA NA 21 22 NA NA NA NA NA NA NA NA NA
[16345] 20 21 NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA  8  9
[16369] NA NA NA NA NA NA NA NA NA 21 22 NA NA NA NA NA NA NA NA NA 10 11 NA NA
[16393] NA NA NA NA NA NA NA 22 23 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA
[16417] NA NA NA NA NA NA  8 NA NA NA NA NA NA NA NA NA 28 29 NA NA NA NA NA NA
[16441] NA NA NA 18 19 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA
[16465] NA 14 15 NA NA NA NA NA NA NA NA NA NA 23 NA NA NA NA NA NA NA NA  5  6
[16489]  7  7  8  9 10 11 12 13 14 15 16 17 10 11 12 13 14 15 16 17 18 19 20  3
[16513]  4  5  6  7  8  9 10 11 12 13 12 13 14 15 16 17 18 19 20 21 22 12 13 14
[16537] 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22  3  4  5  6  7
[16561]  8  9 10 11 12 13 10 11 12 13 14 15 16 17 18 19 20 11 12 13 14 15 16 17
[16585] 18 19 20 21 12 13 14 15 16 17 18 19 20 21 22  9 10 11 12 13 14 15 16 17
[16609] 18 19  8  9 10 11 12 13 14 15 16 17 18 14 15 16 17 18 19 20 21 22 23 24
[16633] 10 11 12 13 14 15 16 17 18 19 20  4  5  6  7  8  9 10 11 12 13 14 19 20
[16657] 21 22 23 24 25 26 27 28 29 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13
[16681] 14 15 16 17 18 19 20 13 14 15 16 17 18 19 20 21 22 23 19 20 21 22 23 24
[16705] 25 26 27 28 29  6  7  8  9 10 11 12 13 14 15 16  3  4  5  6  7  8  9 10
[16729] 11 12 13 11 12 13 14 15 16 17 18 19 20 21  8  9 10 11 12 13 14 15 16 17
[16753] 18 11 12 13 14 15 16 17 18 19 20 21  7  8  9 10 11 12 13 14 15 16 17 10
[16777] 11 12 13 14 15 16 17 18 19 20 15 16 17 18 19 20 21 22 23 24 25 11 12 13
[16801] 14 15 16 17 18 19 20 21  3  4  5  6  7  8  9 10 11 12 13 10 11 12 13 14
[16825] 15 16 17 18 19 20  7  8  9 10 11 12 13 14 15 16 17  7  8  9 10 11 12 13
[16849] 14 15 16 17 14 15 16 17 18 19 20 21 22 23 24  3  4  5  6  7  8  9 10 11
[16873] 12 13 11 12 13 14 15 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19 20 21
[16897] 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21 22 11 12
[16921] 13 14 15 16 17 18 19 20 21 14 15 16 17 18 19 20 21 22 23 24  3  4  5  6
[16945]  7  8  9 10 11 12 13  7  8  9 10 11 12 13 14 15 16 17  4  5  6  7  8  9
[16969] 10 11 12 13 14  8  9 10 11 12 13 14 15 16 17 18 10 11 12 13 14 15 16 17
[16993] 18 19 20 10 11 12 13 14 15 16 17 18 19 20  7  8  9 10 11 12 13 14 15 16
[17017] 17  3  4  5  6  7  8  9 10 11 12 13  3  4  5  6  7  8  9 10 11 12 13  3
[17041]  4  5  6  7  8  9 10 11 12 13 13 14 15 16 17 18 19 20 21 22 23 10 11 12
[17065] 13 14 15 16 17 18 19 20 13 14 15 16 17 18 19 20 21 22 23  9 10 11 12 13
[17089] 14 15 16 17 18 19  6  7  8  9 10 11 12 13 14 15 16 15 16 17 18 19 20 21
[17113] 22 23 24 25  9 10 11 12 13 14 15 16 17 18 19 10 11 12 13 14 15 16 17 18
[17137] 19 20 10 11 12 13 14 15 16 17 18 19 20  7  8  9 10 11 12 13 14 15 16 17
[17161]  3  4  5  6  7  8  9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24  8  9
[17185] 10 11 12 13 14 15 16 17 18 10 11 12 13 14 15 16 17 18 19 20  8  9 10 11
[17209] 12 13 14 15 16 17 18 14 15 16 17 18 19 20 21 22 23 24 10 11 12 13 14 15
[17233] 16 17 18 19 20  8  9 10 11 12 13 14 15 16 17 18 10 11 12 13 14 15 16 17
[17257] 18 19 20 19 20 21 22 23 24 25 26 27 28 29 11 12 13 14 15 16 17 18 19 20
[17281] 21  8  9 10 11 12 13 14 15 16 17 18 11 12 13 14 15 16 17 18 19 20 21  3
[17305]  4  5  6  7  8  9 10 11 12 13 10 11 12 13 14 15 16 17 18 19 20 11 12 13
[17329] 14 15 16 17 18 19 20 21  8  9 10 11 12 13 14 15 16 17 18 11 12 13 14 15
[17353] 16 17 18 19 20 21  4  5  6  7  8  9 10 11 12 13 14  4  5  6  7  8  9 10
[17377] 11 12 13 14  9 10 11 12 13 14 15 16 17 18 19  5  6  7  8  9 10 11 12 13
[17401] 14 15 15 16 17 18 19 20 21 22 23 24 25  3  4  5  6  7  8  9 10 11 12 13
[17425] 14 15 16 17 18 19 20 21 22 23 24 17 18 19 20 21 22 23 24 25 26 27 10 11
[17449] 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16 17 18 19 20 11 12 13 14
[17473] 15 16 17 18 19 20 21 12 13 14 15 16 17 18 19 20 21 22  4  5  6  7  8  9
[17497] 10 11 12 13 14 13 14 15 16 17 18 19 20 21 22 23  5  6  7  8  9 10 11 12
[17521] 13 14 15  8  9 10 11 12 13 14 15 16 17 18  5  6  7  8  9 10 11 12 13 14
[17545] 15  9 10 11 12 13 14 15 16 17 18 19 13 14 15 16 17 18 19 20 21 22 23  8
[17569]  9 10 11 12 13 14 15 16 17 18  8  9 10 11 12 13 14 15 16 17 18 11 12 13
[17593] 14 15 16 17 18 19 20 21  8  9 10 11 12 13 14 15 16 17 18 10 11 12 13 14
[17617] 15 16 17 18 19 20  6  7  8  9 10 11 12 13 14 15 16  2  3  4  5  6  7  8
[17641]  9 10 11 12  6  7  8  9 10 11 12 13 14 15 16  9 10 11 12 13 14 15 16 17
[17665] 18 19 10 11 12 13 14 15 16 17 18 19 20  6  7  8  9 10 11 12 13 14 15 16
[17689] 24 25 26 27 28 29 30 31 32 33 34  2  3  4  5  6  7  8  9 10 11 12 10 11
[17713] 12 13 14 15 16 17 18 19 20 12 13 14 15 16 17 18 19 20 21 22 11 12 13 14
[17737] 15 16 17 18 19 20 21 16 17 18 19 20 21 22 23 24 25 26  8  9 10 11 12 13
[17761] 14 15 16 17 18  8  9 10 11 12 13 14 15 16 17 18  6  7  8  9 10 11 12 13
[17785] 14 15 16 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18 19
[17809] 20  7  8  9 10 11 12 13 14 15 16 17 10 11 12 13 14 15 16 17 18 19 20  3
[17833]  4  5  6  7  8  9 10 11 12 13  3  4  5  6  7  8  9 10 11 12 13 10 11 12
[17857] 13 14 15 16 17 18 19 20  6  7  8  9 10 11 12 13 14 15 16 10 11 12 13 14
[17881] 15 16 17 18 19 20  9 10 11 12 13 14 15 16 17 18 19  5  6  7  8  9 10 11
[17905] 12 13 14 15 15 16 17 18 19 20 21 22 23 24 25  2  3  4  5  6  7  8  9 10
[17929] 11 12 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18 19 20
[17953] 13 14 15 16 17 18 19 20 21 22 23 10 11 12 13 14 15 16 17 18 19 20 13 14
[17977] 15 16 17 18 19 20 21 22 23 11 12 13 14 15 16 17 18 19 20 21 20 21 22 23
[18001] 24 25 26 27 28 29 30  6  7  8  9 10 11 12 13 14 15 16 25 26 27 28 29 30
[18025] 31 32 33 34 35  8  9 10 11 12 13 14 15 16 17 18 11 12 13 14 15 16 17 18
[18049] 19 20 21 12 13 14 15 16 17 18 19 20 21 22 10 11 12 13 14 15 16 17 18 19
[18073] 20  8  9 10 11 12 13 14 15 16 17 18  9 10 11 12 13 14 15 16 17 18 19  8
[18097]  9 10 11 12 13 14 15 16 17 18  7  8  9 10 11 12 13 14 15 16 17  2  3  4
[18121]  5  6  7  8  9 10 11 12 16 17 18 19 20 21 22 23 24 25 26  2  3  4  5  6
[18145]  7  8  9 10 11 12 10 11 12 13 14 15 16 17 18 19 20 11 12 13 14 15 16 17
[18169] 18 19 20 21  7  8  9 10 11 12 13 14 15 16 17  8  9 10 11 12 13 14 15 16
[18193] 17 18 16 17 18 19 20 21 22 23 24 25 26  5  6  7  8  9 10 11 12 13 14 15
[18217] 14 15 16 17 18 19 20 21 22 23 24 10 11 12 13 14 15 16 17 18 19 20 16 17
[18241] 18 19 20 21 22 23 24 25 26 13 14 15 16 17 18 19 20 21 22 23  9 10 11 12
[18265] 13 14 15 16 17 18 19 18 19 20 21 22 23 24 25 26 27 28 10 11 12 13 14 15
[18289] 16 17 18 19 20 14 15 16 17 18 19 20 21 22 23 24 12 13 14 15 16 17 18 19
[18313] 20 21 22  9 10 11 12 13 14 15 16 17 18 19  8  9 10 11 12 13 14 15 16 17
[18337] 18  5  6  7  8  9 10 11 12 13 14 15 14 15 16 17 18 19 20 21 22 23 24  9
[18361] 10 11 12 13 14 15 16 17 18 19 11 12 13 14 15 16 17 18 19 20 21  5  6  7
[18385]  8  9 10 11 12 13 14 15 18 19 20 21 22 23 24 25 26 27 28 10 11 12 13 14
[18409] 15 16 17 18 19 20  9 10 11 12 13 14 15 16 17 18 19  7  8  9 10 11 12 13
[18433] 14 15 16 17 12 13 14 15 16 17 18 19 20 21 22 11 12 13 14 15 16 17 18 19
[18457] 20 21  8  9 10 11 12 13 14 15 16 17 18  8  9 10 11 12 13 14 15 16 17 18
[18481]  8  9 10 11 12 13 14 15 16 17 18  6  7  8  9 10 11 12 13 14 15 16  7  8
[18505]  9 10 11 12 13 14 15 16 17 16 17 18 19 20 21 22 23 24 25 26  9 10 11 12
[18529] 13 14 15 16 17 18 19 10 11 12 13 14 15 16 17 18 19 20  8  9 10 11 12 13
[18553] 14 15 16 17 18  8  9 10 11 12 13 14 15 16 17 18 12 13 14 15 16 17 18 19
[18577] 20 21 22  6  7  8  9 10 11 12 13 14 15 16  9 10 11 12 13 14 15 16 17 18
[18601] 19  9 10 11 12 13 14 15 16 17 18 19 12 13 14 15 16 17 18 19 20 21 22 16
[18625] 17 18 19 20 21 22 23 24 25 26 14 15 16 17 18 19 20 21 22 23 24 13 14 15
[18649] 16 17 18 19 20 21 22 23 11 12 13 14 15 16 17 18 19 20 21  7  8  9 10 11
[18673] 12 13 14 15 16 17 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16
[18697] 17 18 19 20 16 17 18 19 20 21 22 23 24 25 26  8  9 10 11 12 13 14 15 16
[18721] 17 18 14 15 16 17 18 19 20 21 22 23 24 11 12 13 14 15 16 17 18 19 20 21
[18745] 17 18 19 20 21 22 23 24 25 26 27  7  8  9 10 11 12 13 14 15 16 17  6  7
[18769]  8  9 10 11 12 13 14 15 16 14 15 16 17 18 19 20 21 22 23 24  9 10 11 12
[18793] 13 14 15 16 17 18 19  6  7  8  9 10 11 12 13 14 15 16 11 12 13 14 15 16
[18817] 17 18 19 20 21 18 19 20 21 22 23 24 25 26 27 28 11 12 13 14 15 16 17 18
[18841] 19 20 21  6  7  8  9 10 11 12 13 14 15 16  8  9 10 11 12 13 14 15 16 17
[18865] 18 18 19 20 21 22 23 24 25 26 27 28  9 10 11 12 13 14 15 16 17 18 19  7
[18889]  8  9 10 11 12 13 14 15 16 17  7  8  9 10 11 12 13 14 15 16 17  7  8  9
[18913] 10 11 12 13 14 15 16 17  8  9 10 11 12 13 14 15 16 17 18 18 19 20 21 22
[18937] 23 24 25 26 27 28  7  8  9 10 11 12 13 14 15 16 17  7  8  9 10 11 12 13
[18961] 14 15 16 17 12 13 14 15 16 17 18 19 20 21 22 14 15 16 17 18 19 20 21 22
[18985] 23 24 18 19 20 21 22 23 24 25 26 27 28 11 12 13 14 15 16 17 18 19 20 21
[19009]  9 10 11 12 13 14 15 16 17 18 19 10 11 12 13 14 15 16 17 18 19 20 10 11
[19033] 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16 17 18 19 20 18 19 20 21
[19057] 22 23 24 25 26 27 28 16 17 18 19 20 21 22 23 24 25 26 11 12 13 14 15 16
[19081] 17 18 19 20 21  9 10 11 12 13 14 15 16 17 18 19 12 13 14 15 16 17 18 19
[19105] 20 21 22 11 12 13 14 15 16 17 18 19 20 21  9 10 11 12 13 14 15 16 17 18
[19129] 19  7  8  9 10 11 12 13 14 15 16 17  6  7  8  9 10 11 12 13 14 15 16 11
[19153] 12 13 14 15 16 17 18 19 20 21  8  9 10 11 12 13 14 15 16 17 18  9 10 11
[19177] 12 13 14 15 16 17 18 19 19 20 21 22 23 24 25 26 27 28 29 11 12 13 14 15
[19201] 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19 20 21  5  6  7  8  9 10 11
[19225] 12 13 14 15 12 13 14 15 16 17 18 19 20 21 22 16 17 18 19 20 21 22 23 24
[19249] 25 26 11 12 13 14 15 16 17 18 19 20 21  9 10 11 12 13 14 15 16 17 18 19
[19273] 19 20 21 22 23 24 25 26 27 28 29  8  9 10 11 12 13 14 15 16 17 18  8  9
[19297] 10 11 12 13 14 15 16 17 18  8  9 10 11 12 13 14 15 16 17 18 11 12 13 14
[19321] 15 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15
[19345] 16 17 18 19 20 15 16 17 18 19 20 21 22 23 24 25 14 15 16 17 18 19 20 21
[19369] 22 23 24  8  9 10 11 12 13 14 15 16 17 18 12 13 14 15 16 17 18 19 20 21
[19393] 22 11 12 13 14 15 16 17 18 19 20 21  8  9 10 11 12 13 14 15 16 17 18 13
[19417] 14 15 16 17 18 19 20 21 22 23  9 10 11 12 13 14 15 16 17 18 19 24 25 26
[19441] 27 28 29 30 31 32 33 34  8  9 10 11 12 13 14 15 16 17 18 14 15 16 17 18
[19465] 19 20 21 22 23 24  9 10 11 12 13 14 15 16 17 18 19  6  7  8  9 10 11 12
[19489] 13 14 15 16  8  9 10 11 12 13 14 15 16 17 18  9 10 11 12 13 14 15 16 17
[19513] 18 19 13 14 15 16 17 18 19 20 21 22 23 14 15 16 17 18 19 20 21 22 23 24
[19537]  6  7  8  9 10 11 12 13 14 15 16  9 10 11 12 13 14 15 16 17 18 19 NA 13
[19561] 14 15 16 17 18 19 20 21 22 NA  8  9 10 11 12 13 14 15 16 17 NA 19 20 21
[19585] 22 23 24 25 26 27 28 NA  6  7  8  9 10 11 12 13 14 15 NA 17 18 19 20 21
[19609] 22 23 24 25 26 NA 13 14 15 16 17 18 19 20 21 22 NA 10 11 12 13 14 15 16
[19633] 17 18 19 10 11 12 13 14 15 16 17 18 19 20 NA 10 11 12 13 14 15 16 17 18
[19657] 19 NA  7  8  9 10 11 12 13 14 15 16 13 14 15 16 17 18 19 20 21 22 23 NA
[19681]  7  8  9 10 11 12 13 14 15 16 NA  5  6  7  8  9 10 11 12 13 14 NA  8  9
[19705] 10 11 12 13 14 15 16 17 NA  9 10 11 12 13 14 15 16 17 18 NA  9 10 11 12
[19729] 13 14 15 16 17 18 NA  7  8  9 10 11 12 13 14 15 16 NA  6  7  8  9 10 11
[19753] 12 13 14 15 NA 11 12 13 14 15 16 17 18 19 20 NA 12 13 14 15 16 17 18 19
[19777] 20 21 NA  9 10 11 12 13 14 15 16 17 18 NA 20 21 22 23 24 25 26 27 28 29
[19801] NA  8  9 10 11 12 13 14 15 16 17 NA  6  7  8  9 10 11 12 13 14 15 NA 19
[19825] 20 21 22 23 24 25 26 27 28 10 11 12 13 14 15 16 17 18 19 20 NA 11 12 13
[19849] 14 15 16 17 18 19 20 NA 14 15 16 17 18 19 20 21 22 23 NA 10 11 12 13 14
[19873] 15 16 17 18 19 NA  8  9 10 11 12 13 14 15 16 17 NA  7  8  9 10 11 12 13
[19897] 14 15 16 NA 15 16 17 18 19 20 21 22 23 24 NA  7  8  9 10 11 12 13 14 15
[19921] 16 NA 18 19 20 21 22 23 24 25 26 27 NA 19 20 21 22 23 24 25 26 27 28 NA
[19945] 11 12 13 14 15 16 17 18 19 20 NA 15 16 17 18 19 20 21 22 23 24 NA 11 12
[19969] 13 14 15 16 17 18 19 20 NA  5  6  7  8  9 10 11 12 13 14 NA  9 10 11 12
[19993] 13 14 15 16 17 18 NA  9 10 11 12 13 14 15 16 17 18 NA 16 17 18 19 20 21
[20017] 22 23 24 25 NA  8  9 10 11 12 13 14 15 16 17 NA 14 15 16 17 18 19 20 21
[20041] 22 23 NA 12 13 14 15 16 17 18 19 20 21 NA  6  7  8  9 10 11 12 13 14 15
[20065] NA 12 13 14 15 16 17 18 19 20 21 NA 10 11 12 13 14 15 16 17 18 19 NA 10
[20089] 11 12 13 14 15 16 17 18 19 NA  9 10 11 12 13 14 15 16 17 18 NA 13 14 15
[20113] 16 17 18 19 20 21 22 NA 17 18 19 20 21 22 23 24 25 26 NA 12 13 14 15 16
[20137] 17 18 19 20 21 NA  8  9 10 11 12 13 14 15 16 17 NA 10 11 12 13 14 15 16
[20161] 17 18 19 NA  8  9 10 11 12 13 14 15 16 17 NA 12 13 14 15 16 17 18 19 20
[20185] 21 NA 22 23 24 25 26 27 28 29 30 31 NA 16 17 18 19 20 21 22 23 24 25 NA
[20209] 12 13 14 15 16 17 18 19 20 21 NA 19 20 21 22 23 24 25 26 27 28 NA 11 12
[20233] 13 14 15 16 17 18 19 20 NA 12 13 14 15 16 17 18 19 20 21 NA NA 11 12 13
[20257] 14 15 16 17 18 19 NA NA 11 12 13 14 15 16 17 18 19 NA NA  8  9 10 11 12
[20281] 13 14 15 16 NA NA  7  8  9 10 11 12 13 14 15 NA NA  8  9 10 11 12 13 14
[20305] 15 16 NA NA NA 14 15 16 17 18 19 20 21 NA NA 13 14 15 16 17 18 19 20 21
[20329] NA NA 17 18 19 20 21 22 23 24 25 NA NA  8  9 10 11 12 13 14 15 16 NA NA
[20353] 13 14 15 16 17 18 19 20 21 NA NA 15 16 17 18 19 20 21 22 23 NA NA 15 16
[20377] 17 18 19 20 21 22 23 NA NA  7  8  9 10 11 12 13 14 15 NA NA NA  8  9 10
[20401] 11 12 13 14 15 NA NA 25 26 27 28 29 30 31 32 33 NA NA 12 13 14 15 16 17
[20425] 18 19 20 NA NA 12 13 14 15 16 17 18 19 20 NA NA 12 13 14 15 16 17 18 19
[20449] 20 NA NA 16 17 18 19 20 21 22 23 24 NA NA 12 13 14 15 16 17 18 19 20 NA
[20473] NA 22 23 24 25 26 27 28 29 30 NA NA  9 10 11 12 13 14 15 16 17 NA NA 14
[20497] 15 16 17 18 19 20 21 22 NA NA NA 15 16 17 18 19 20 21 22 NA NA NA 13 14
[20521] 15 16 17 18 19 20 NA NA NA 16 17 18 19 20 21 22 23 NA NA NA  7  8  9 10
[20545] 11 12 13 14 NA NA NA 17 18 19 20 21 22 23 24 NA NA NA 13 14 15 16 17 18
[20569] 19 20 NA NA NA 17 18 19 20 21 22 23 24 NA NA NA 13 14 15 16 17 18 19 20
[20593] NA NA NA 13 14 15 16 17 18 19 20 NA NA NA  6  7  8  9 10 11 12 13 NA NA
[20617] NA NA 10 11 12 13 14 15 16 NA NA NA 15 16 17 18 19 20 21 22 NA NA NA 20
[20641] 21 22 23 24 25 26 27 NA NA NA 10 11 12 13 14 15 16 17 NA NA NA 12 13 14
[20665] 15 16 17 18 19 NA NA NA  8  9 10 11 12 13 14 15 NA NA NA 10 11 12 13 14
[20689] 15 16 17 NA NA NA 14 15 16 17 18 19 20 21 NA NA NA 20 21 22 23 24 25 26
[20713] 27 NA NA NA 12 13 14 15 16 17 18 19 NA NA NA NA 13 14 15 16 17 18 19 NA
[20737] NA NA 12 13 14 15 16 17 18 19 NA NA NA 13 14 15 16 17 18 19 20 NA NA NA
[20761] 14 15 16 17 18 19 20 21 NA NA NA 22 23 24 25 26 27 28 29 NA NA NA  7  8
[20785]  9 10 11 12 13 14 NA NA NA 17 18 19 20 21 22 23 24 NA NA NA  9 10 11 12
[20809] 13 14 15 16 NA NA NA  6  7  8  9 10 11 12 13 NA NA NA  9 10 11 12 13 14
[20833] 15 16 NA NA NA NA 11 12 13 14 15 16 17 NA NA NA 12 13 14 15 16 17 18 19
[20857] NA NA NA  7  8  9 10 11 12 13 14 NA NA NA 18 19 20 21 22 23 24 25 NA NA
[20881] NA  6  7  8  9 10 11 12 13 NA NA NA  9 10 11 12 13 14 15 16 NA NA NA 13
[20905] 14 15 16 17 18 19 20 NA NA NA 14 15 16 17 18 19 20 21 NA NA NA NA 16 17
[20929] 18 19 20 21 22 NA NA NA NA 12 13 14 15 16 17 18 NA NA NA NA 10 11 12 13
[20953] 14 15 16 NA NA NA 12 13 14 15 16 17 18 19 NA NA NA NA  9 10 11 12 13 14
[20977] 15 NA NA NA NA 10 11 12 13 14 15 16 NA NA NA NA 17 18 19 20 21 22 23 NA
[21001] NA NA 17 18 19 20 21 22 23 24 NA NA NA NA 12 13 14 15 16 17 18 NA NA NA
[21025] NA 18 19 20 21 22 23 24 NA NA NA NA  9 10 11 12 13 14 15 NA NA NA NA 11
[21049] 12 13 14 15 16 17 NA NA NA  8  9 10 11 12 13 14 15 NA NA NA NA 14 15 16
[21073] 17 18 19 20 NA NA NA NA 20 21 22 23 24 25 26 NA NA NA NA 14 15 16 17 18
[21097] 19 20 NA NA NA NA 12 13 14 15 16 17 18 NA NA NA NA  9 10 11 12 13 14 15
[21121] NA NA NA NA  7  8  9 10 11 12 13 NA NA NA NA  8  9 10 11 12 13 14 NA NA
[21145] NA NA  9 10 11 12 13 14 15 NA NA NA NA 13 14 15 16 17 18 19 NA NA NA NA
[21169] 11 12 13 14 15 16 17 NA NA NA NA 10 11 12 13 14 15 16 NA NA NA NA 14 15
[21193] 16 17 18 19 20 NA NA NA NA 14 15 16 17 18 19 20 NA NA NA NA 11 12 13 14
[21217] 15 16 17 NA NA NA NA 13 14 15 16 17 18 19 NA NA NA NA 18 19 20 21 22 23
[21241] 24 NA NA NA NA 14 15 16 17 18 19 20 NA NA NA NA 18 19 20 21 22 23 24 NA
[21265] NA NA NA NA NA NA 18 19 20 21 NA NA NA NA  8  9 10 11 12 13 14 NA NA NA
[21289] NA  7  8  9 10 11 12 13 NA NA NA NA  9 10 11 12 13 14 15 NA NA NA NA  8
[21313]  9 10 11 12 13 14 NA NA NA NA  8  9 10 11 12 13 14 NA NA NA NA 13 14 15
[21337] 16 17 18 19 NA NA NA NA 12 13 14 15 16 17 18 NA NA NA NA 14 15 16 17 18
[21361] 19 20 NA NA NA NA 21 22 23 24 25 26 27 NA NA NA NA 21 22 23 24 25 26 27
[21385] NA NA NA NA 17 18 19 20 21 22 23 NA NA NA NA 19 20 21 22 23 24 25 NA NA
[21409] NA NA  7  8  9 10 11 12 13 NA NA NA NA 12 13 14 15 16 17 18 NA NA NA NA
[21433] 18 19 20 21 22 23 24 NA NA NA NA 22 23 24 25 26 27 28 NA NA NA NA 10 11
[21457] 12 13 14 15 16 NA NA NA NA 12 13 14 15 16 17 18 NA NA NA NA 11 12 13 14
[21481] 15 16 17 NA NA NA NA NA 10 11 12 13 14 15 NA NA NA NA 17 18 19 20 21 22
[21505] 23 NA NA NA NA  9 10 11 12 13 14 15 NA NA NA NA 18 19 20 21 22 23 24 NA
[21529] NA NA NA 11 12 13 14 15 16 17 NA NA NA NA 11 12 13 14 15 16 17 NA NA NA
[21553] NA 21 22 23 24 25 26 27 NA NA NA NA 13 14 15 16 17 18 19 NA NA NA NA 23
[21577] 24 25 26 27 28 29 NA NA NA NA 12 13 14 15 16 17 18 NA NA NA NA  9 10 11
[21601] 12 13 14 15 NA NA NA NA 16 17 18 19 20 21 22 NA NA NA NA 20 21 22 23 24
[21625] 25 26 NA NA NA NA 14 15 16 17 18 19 20 NA NA NA NA 18 19 20 21 22 23 24
[21649] NA NA NA NA  9 10 11 12 13 14 15 NA NA NA NA  6  7  8  9 10 11 12 NA NA
[21673] NA NA 17 18 19 20 21 22 23 NA NA NA NA 12 13 14 15 16 17 18 NA NA NA NA
[21697] 15 16 17 18 19 20 21 NA NA NA NA  6  7  8  9 10 11 12 NA NA NA NA 14 15
[21721] 16 17 18 19 20 NA NA NA NA  7  8  9 10 11 12 13 NA NA NA NA 13 14 15 16
[21745] 17 18 19 NA NA NA NA 22 23 24 25 26 27 28 NA NA NA NA 14 15 16 17 18 19
[21769] 20 NA NA NA NA 27 28 29 30 31 32 33 NA NA NA NA 10 11 12 13 14 15 16 NA
[21793] NA NA NA NA  8  9 10 11 12 13 NA NA NA NA NA 10 11 12 13 14 15 NA NA NA
[21817] NA NA NA 16 17 18 19 20 NA NA NA NA NA 11 12 13 14 15 16 NA NA NA NA 14
[21841] 15 16 17 18 19 20 NA NA NA NA NA 23 24 25 26 27 28 NA NA NA NA NA  9 10
[21865] 11 12 13 14 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 10 11 12 13
[21889] 14 15 NA NA NA NA NA 19 20 21 22 23 24 NA NA NA NA NA 17 18 19 20 21 22
[21913] NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA NA 15 16 17 18 19 NA NA
[21937] NA NA NA 12 13 14 15 16 17 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA
[21961] NA 22 23 24 25 26 27 NA NA NA NA NA 10 11 12 13 14 15 NA NA NA NA NA 18
[21985] 19 20 21 22 23 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 10 11 12
[22009] 13 14 15 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA NA 11 12 13 14 15
[22033] 16 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA NA 13 14 15 16 17 18 NA
[22057] NA NA NA NA  9 10 11 12 13 14 NA NA NA NA NA 11 12 13 14 15 16 NA NA NA
[22081] NA NA 27 28 29 30 31 32 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA
[22105] 10 11 12 13 14 15 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA  8  9
[22129] 10 11 12 13 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA 13 14 15 16
[22153] 17 18 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 10 11 12 13 14 15
[22177] NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA 15 16 17 18 19 20 NA NA
[22201] NA NA NA  8  9 10 11 12 13 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA
[22225] NA 18 19 20 21 22 23 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA NA 14
[22249] 15 16 17 18 19 NA NA NA NA NA 21 22 23 24 25 26 NA NA NA NA NA 14 15 16
[22273] 17 18 19 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 18 19 20 21 22
[22297] 23 NA NA NA NA NA  6  7  8  9 10 11 NA NA NA NA NA 20 21 22 23 24 25 NA
[22321] NA NA NA NA 22 23 24 25 26 27 NA NA NA NA NA 12 13 14 15 16 17 NA NA NA
[22345] NA NA NA 18 19 20 21 22 NA NA NA NA NA  9 10 11 12 13 14 NA NA NA NA NA
[22369] 16 17 18 19 20 21 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 13 14
[22393] 15 16 17 18 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA 15 16 17 18
[22417] 19 20 NA NA NA NA NA 11 12 13 14 15 16 NA NA NA NA NA 14 15 16 17 18 19
[22441] NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA NA NA NA 23 24 25 NA NA
[22465] NA NA NA 15 16 17 18 19 20 NA NA NA NA NA  9 10 11 12 13 14 NA NA NA NA
[22489] NA 10 11 12 13 14 15 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 10
[22513] 11 12 13 14 15 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 14 15 16
[22537] 17 18 19 NA NA NA NA NA 10 11 12 13 14 15 NA NA NA NA NA 16 17 18 19 20
[22561] 21 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 20 21 22 23 24 25 NA
[22585] NA NA NA NA  7  8  9 10 11 12 NA NA NA NA NA 10 11 12 13 14 15 NA NA NA
[22609] NA NA 15 16 17 18 19 20 NA NA NA NA NA 19 20 21 22 23 24 NA NA NA NA NA
[22633] 16 17 18 19 20 21 NA NA NA NA NA  9 10 11 12 13 14 NA NA NA NA NA  6  7
[22657]  8  9 10 11 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 15 16 17 18
[22681] 19 20 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 13 14 15 16 17 18
[22705] NA NA NA NA NA 17 18 19 20 21 22 NA NA NA NA NA 13 14 15 16 17 18 NA NA
[22729] NA NA NA  7  8  9 10 11 12 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA
[22753] NA  7  8  9 10 11 12 NA NA NA NA NA 19 20 21 22 23 24 NA NA NA NA NA NA
[22777] NA NA 19 20 21 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 13 14 15
[22801] 16 17 18 NA NA NA NA NA 31 32 33 34 35 36 NA NA NA NA NA 10 11 12 13 14
[22825] 15 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 23 24 25 26 27 28 NA
[22849] NA NA NA NA 24 25 26 27 28 29 NA NA NA NA NA 23 24 25 26 27 28 NA NA NA
[22873] NA NA 20 21 22 23 24 25 NA NA NA NA NA NA  9 10 11 12 13 NA NA NA NA NA
[22897] 21 22 23 24 25 26 NA NA NA NA NA 12 13 14 15 16 17 NA NA NA NA NA  9 10
[22921] 11 12 13 14 NA NA NA NA NA 21 22 23 24 25 26 NA NA NA NA NA 12 13 14 15
[22945] 16 17 NA NA NA NA NA 21 22 23 24 25 26 NA NA NA NA NA 14 15 16 17 18 19
[22969] NA NA NA NA NA  9 10 11 12 13 14 NA NA NA NA NA NA 13 14 15 16 17 NA NA
[22993] NA NA NA NA NA NA NA NA 12 NA NA NA NA NA 11 12 13 14 15 16 NA NA NA NA
[23017] NA NA 13 14 15 16 17 NA NA NA NA NA NA 13 14 15 16 17 NA NA NA NA NA NA
[23041] 20 21 22 23 24 NA NA NA NA NA NA 15 16 17 18 19 NA NA NA NA NA NA 17 18
[23065] 19 20 21 NA NA NA NA NA NA 23 24 25 26 27 NA NA NA NA NA NA 20 21 22 23
[23089] 24 NA NA NA NA NA NA 22 23 24 25 26 NA NA NA NA NA NA 14 15 16 17 18 NA
[23113] NA NA NA NA NA  7  8  9 10 11 NA NA NA NA NA NA 21 22 23 24 25 NA NA NA
[23137] NA NA NA 14 15 16 17 18 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA
[23161] NA 19 20 21 22 23 NA NA NA NA NA NA 25 26 27 28 29 NA NA NA NA NA 19 20
[23185] 21 22 23 24 NA NA NA NA NA NA 23 24 25 26 27 NA NA NA NA NA 14 15 16 17
[23209] 18 19 NA NA NA NA NA NA NA 14 15 16 17 NA NA NA NA NA NA 19 20 21 22 23
[23233] NA NA NA NA NA NA 13 14 15 16 17 NA NA NA NA NA NA 15 16 17 18 19 NA NA
[23257] NA NA NA NA 18 19 20 21 22 NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA
[23281] NA NA 22 23 24 25 26 NA NA NA NA NA NA 10 11 12 13 14 NA NA NA NA NA NA
[23305] 14 15 16 17 18 NA NA NA NA NA NA 24 25 26 27 28 NA NA NA NA NA NA NA 19
[23329] 20 21 22 NA NA NA NA NA NA 17 18 19 20 21 NA NA NA NA NA NA 12 13 14 15
[23353] 16 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA 16 17 18 19 20 NA
[23377] NA NA NA NA NA 10 11 12 13 14 NA NA NA NA NA NA 13 14 15 16 17 NA NA NA
[23401] NA NA NA 15 16 17 18 19 NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA NA
[23425] NA 13 14 15 16 17 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA 13
[23449] 14 15 16 17 NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA NA NA 17 18 19
[23473] 20 21 NA NA NA NA NA NA 15 16 17 18 19 NA NA NA NA NA NA 11 12 13 14 15
[23497] NA NA NA NA NA NA 10 11 12 13 14 NA NA NA NA NA NA 12 13 14 15 16 NA NA
[23521] NA NA NA NA 18 19 20 21 22 NA NA NA NA NA NA 20 21 22 23 24 NA NA NA NA
[23545] NA NA 19 20 21 22 23 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA
[23569] 12 13 14 15 16 NA NA NA NA NA NA 13 14 15 16 17 NA NA NA NA NA NA 13 14
[23593] 15 16 17 NA NA NA NA NA NA  6  7  8  9 10 NA NA NA NA NA NA 16 17 18 19
[23617] 20 NA NA NA NA NA NA  9 10 11 12 13 NA NA NA NA NA NA 15 16 17 18 19 NA
[23641] NA NA NA NA NA NA 12 13 14 15 NA NA NA NA NA NA 10 11 12 13 14 NA NA NA
[23665] NA NA NA  7  8  9 10 11 NA NA NA NA NA NA  8  9 10 11 12 NA NA NA NA NA
[23689] NA 16 17 18 19 20 NA NA NA NA NA NA 11 12 13 14 15 NA NA NA NA NA NA 13
[23713] 14 15 16 17 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA 14 15 16
[23737] 17 18 NA NA NA NA NA NA 25 26 27 28 29 NA NA NA NA NA NA 20 21 22 23 24
[23761] NA NA NA NA NA NA  9 10 11 12 13 NA NA NA NA NA NA 13 14 15 16 17 NA NA
[23785] NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA  6  7  8  9 10 NA NA NA NA
[23809] NA NA 11 12 13 14 15 NA NA NA NA NA NA 17 18 19 20 21 NA NA NA NA NA NA
[23833] 12 13 14 15 16 NA NA NA NA NA NA  6  7  8  9 10 NA NA NA NA NA NA 10 11
[23857] 12 13 14 NA NA NA NA NA NA NA 24 25 26 27 NA NA NA NA NA NA 15 16 17 18
[23881] 19 NA NA NA NA NA NA 13 14 15 16 17 NA NA NA NA NA NA 32 33 34 35 36 NA
[23905] NA NA NA NA NA 20 21 22 23 24 NA NA NA NA NA NA 25 26 27 28 29 NA NA NA
[23929] NA NA NA  8  9 10 11 12 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA
[23953] NA 14 15 16 17 18 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA 16
[23977] 17 18 19 20 NA NA NA NA NA NA 15 16 17 18 19 NA NA NA NA NA NA  9 10 11
[24001] 12 13 NA NA NA NA NA NA  7  8  9 10 11 NA NA NA NA NA NA 13 14 15 16 17
[24025] NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA 22 23 24 25 26 NA NA
[24049] NA NA NA NA 23 24 25 26 27 NA NA NA NA NA NA 15 16 17 18 19 NA NA NA NA
[24073] NA NA  6  7  8  9 10 NA NA NA NA NA NA 15 16 17 18 19 NA NA NA NA NA NA
[24097] 18 19 20 21 22 NA NA NA NA NA NA 22 23 24 25 26 NA NA NA NA NA NA 16 17
[24121] 18 19 20 NA NA NA NA NA NA 13 14 15 16 17 NA NA NA NA NA NA 21 22 23 24
[24145] 25 NA NA NA NA NA NA  9 10 11 12 13 NA NA NA NA NA NA 15 16 17 18 19 NA
[24169] NA NA NA NA NA NA 11 12 13 14 NA NA NA NA NA NA 11 12 13 14 15 NA NA NA
[24193] NA NA NA 24 25 26 27 28 NA NA NA NA NA NA 17 18 19 20 21 NA NA NA NA NA
[24217] NA 15 16 17 18 19 NA NA NA NA NA NA 19 20 21 22 23 NA NA NA NA NA NA  9
[24241] 10 11 12 13 NA NA NA NA NA NA 10 11 12 13 14 NA NA NA NA NA NA 18 19 20
[24265] 21 22 NA NA NA NA NA NA 22 23 24 25 26 NA NA NA NA NA NA 10 11 12 13 14
[24289] NA NA NA NA NA NA 21 22 23 24 25 NA NA NA NA NA NA 13 14 15 16 17 NA NA
[24313] NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA NA  6  7  8  9 NA NA NA NA
[24337] NA NA NA 22 23 24 25 NA NA NA NA NA NA NA  6  7  8  9 NA NA NA NA NA NA
[24361] NA 16 17 18 19 NA NA NA NA NA NA NA 13 14 15 16 NA NA NA NA NA NA NA 20
[24385] 21 22 23 NA NA NA NA NA NA NA 11 12 13 14 NA NA NA NA NA NA NA 10 11 12
[24409] 13 NA NA NA NA NA NA NA 16 17 18 19 NA NA NA NA NA NA NA  7  8  9 10 NA
[24433] NA NA NA NA NA NA  8  9 10 11 NA NA NA NA NA NA NA 11 12 13 14 NA NA NA
[24457] NA NA NA NA 17 18 19 20 NA NA NA NA NA NA NA 13 14 15 16 NA NA NA NA NA
[24481] NA NA 11 12 13 14 NA NA NA NA NA NA NA  7  8  9 10 NA NA NA NA NA NA NA
[24505] 12 13 14 15 NA NA NA NA NA NA NA 14 15 16 17 NA NA NA NA NA NA NA 19 20
[24529] 21 22 NA NA NA NA NA NA NA 21 22 23 24 NA NA NA NA NA NA NA NA 13 14 15
[24553] NA NA NA NA NA NA NA NA 14 15 16 NA NA NA NA NA NA NA NA 13 14 15 NA NA
[24577] NA NA NA NA NA NA  9 10 11 NA NA NA NA NA NA NA NA  9 10 11 NA NA NA NA
[24601] NA NA NA NA 11 12 13 NA NA NA NA NA NA NA NA 12 13 14 NA NA NA NA NA NA
[24625] NA NA  9 10 11 NA NA NA NA NA NA NA NA 20 21 22 NA NA NA NA NA NA NA NA
[24649] 18 19 20 NA NA NA NA NA NA NA NA 14 15 16 NA NA NA NA NA NA NA NA NA NA
[24673] 17 NA NA NA NA NA NA NA NA 17 18 19 NA NA NA NA NA NA NA NA 11 12 13 NA
[24697] NA NA NA NA NA NA NA 11 12 13 NA NA NA NA NA NA NA NA 17 18 19 NA NA NA
[24721] NA NA NA NA NA 13 14 15 NA NA NA NA NA NA NA NA 16 17 18 NA NA NA NA NA
[24745] NA NA NA 17 18 19 NA NA NA NA NA NA NA NA  7  8  9 NA NA NA NA NA NA NA
[24769] NA  7  8  9 NA NA NA NA NA NA NA NA  8  9 10 NA NA NA NA NA NA NA NA 25
[24793] 26 27 NA NA NA NA NA NA NA NA 10 11 12 NA NA NA NA NA NA NA NA 26 27 28
[24817] NA NA NA NA NA NA NA NA  9 10 11 NA NA NA NA NA NA NA NA  8  9 10 NA NA
[24841] NA NA NA NA NA NA 26 27 28 NA NA NA NA NA NA NA NA  7  8  9 NA NA NA NA
[24865] NA NA NA NA  6  7  8 NA NA NA NA NA NA NA NA  9 10 11 NA NA NA NA NA NA
[24889] NA NA 16 17 18 NA NA NA NA NA NA NA NA 18 19 20 NA NA NA NA NA NA NA NA
[24913] 20 21 22 NA NA NA NA NA NA NA NA 12 13 14 NA NA NA NA NA NA NA NA 22 23
[24937] 24 NA NA NA NA NA NA NA NA 13 14 15 NA NA NA NA NA NA NA NA 12 13 14 NA
[24961] NA NA NA NA NA NA NA 18 19 20 NA NA NA NA NA NA NA NA  9 10 11 NA NA NA
[24985] NA NA NA NA NA 13 14 15 NA NA NA NA NA NA NA NA 15 16 17 NA NA NA NA NA
[25009] NA NA NA 17 18 19 NA NA NA NA NA NA NA NA 12 13 14 NA NA NA NA NA NA NA
[25033] NA 14 15 16 NA NA NA NA NA NA NA NA 10 11 12 NA NA NA NA NA NA NA NA 12
[25057] 13 14 NA NA NA NA NA NA NA NA 17 18 19 NA NA NA NA NA NA NA NA NA NA 17
[25081] NA NA NA NA NA NA NA NA  9 10 11 NA NA NA NA NA NA NA NA 10 11 12 NA NA
[25105] NA NA NA NA NA NA 19 20 21 NA NA NA NA NA NA NA NA 14 15 16 NA NA NA NA
[25129] NA NA NA NA 19 20 21 NA NA NA NA NA NA NA NA 23 24 25 NA NA NA NA NA NA
[25153] NA NA NA 14 15 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA
[25177] NA 18 19 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA 13
[25201] 14 NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA  8  9 NA
[25225] NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA 14 15 NA NA NA
[25249] NA NA NA NA NA NA 19 20 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA
[25273] NA NA NA NA 25 26 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA
[25297] NA NA NA 15 NA NA NA NA NA NA NA NA NA 22 23 NA NA NA NA NA NA NA NA NA
[25321] 14 15 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA 17 18
[25345] NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA 13 14 NA NA
[25369] NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA
[25393] NA NA NA NA NA 13 14 NA NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA NA
[25417] NA NA NA 18 19 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA
[25441] NA 20 21 NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA 22
[25465] 23 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA 19 20 NA
[25489] NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA 18 19 NA NA NA
[25513] NA NA NA NA NA NA NA 20 NA NA NA NA NA NA NA NA NA 29 30 NA NA NA NA NA
[25537] NA NA NA NA 20 21 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA
[25561] NA NA 17 18 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA NA NA NA NA NA
[25585] 18 19 NA NA NA NA NA NA NA NA NA  5  6 NA NA NA NA NA NA NA NA NA 19 20
[25609] NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA  8  9 NA NA
[25633] NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA  9 10 NA NA NA NA
[25657] NA NA NA NA NA 23 24 NA NA NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA
[25681] NA NA NA 24 25 NA NA NA NA NA NA NA NA NA 24 25 NA NA NA NA NA NA NA NA
[25705] NA 18 19 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA  9
[25729] 10 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA  7  8 NA
[25753] NA NA NA NA NA NA NA NA  9 10 NA NA NA NA NA NA NA NA NA 20 21 NA NA NA
[25777] NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA 21 22 NA NA NA NA NA
[25801] NA NA NA NA 19 20 NA NA NA NA NA NA NA NA NA 23 24 NA NA NA NA NA NA NA
[25825] NA NA 10 11 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA
[25849] 19 20 NA NA NA NA NA NA NA NA NA 18 19 NA NA NA NA NA NA NA NA NA 18 19
[25873] NA NA NA NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA NA NA 11 12 NA NA
[25897] NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA
[25921] NA NA NA NA NA 23 24 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA NA NA
[25945] NA NA NA 10 11 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA
[25969] NA 16 17 NA NA NA NA NA NA NA NA NA 21 22 NA NA NA NA NA NA NA NA NA 24
[25993] 25 NA NA NA NA NA NA NA NA NA  9 10 NA NA NA NA NA NA NA NA NA  9 10 NA
[26017] NA NA NA NA NA NA NA NA  9 10 NA NA NA NA NA NA NA NA NA 12 13 NA NA NA
[26041] NA NA NA NA NA NA 19 20 NA NA NA NA NA NA NA NA NA 18 19 NA NA NA NA NA
[26065] NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA
[26089] NA NA 18 19 NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA
[26113] 24 25 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA 19 20
[26137] NA NA NA NA NA NA NA NA NA 25 26 NA NA NA NA NA NA NA NA NA 17 18 NA NA
[26161] NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA 27 28 NA NA NA NA
[26185] NA NA NA NA NA 13 14 NA NA NA NA NA NA NA NA NA  6  7 NA NA NA NA NA NA
[26209] NA NA NA  8  9 NA NA NA NA NA NA NA NA NA 24 25 NA NA NA NA NA NA NA NA
[26233] NA 24 25 NA NA NA NA NA NA NA NA NA 34 35 NA NA NA NA NA NA NA NA NA 14
[26257] 15 NA NA NA NA NA NA NA NA NA  9 10 NA NA NA NA NA NA NA NA NA 21 22 NA
[26281] NA NA NA NA NA NA NA NA  7  8 NA NA NA NA NA NA NA NA NA 18 19 NA NA NA
[26305] NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA
[26329] NA NA NA NA 12 13 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA
[26353] NA NA 10 11 NA NA NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA
[26377] 13 14 NA NA NA NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA NA NA 16 17
[26401] NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA 25 26 NA NA
[26425] NA NA NA NA NA NA NA 13 14 NA NA NA NA NA NA NA NA NA 27 28 NA NA NA NA
[26449] NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA 25 26 NA NA NA NA NA NA
[26473] NA NA NA  9 10 NA NA NA NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA NA
[26497] NA 19 20 NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA  4
[26521]  5 NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA 26 27 NA
[26545] NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA  5  6 NA NA NA
[26569] NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA 13 14 NA NA NA NA NA
[26593] NA NA NA NA  8  9 NA NA NA NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA
[26617] NA NA 19 20 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA
[26641] 18 19 NA NA NA NA NA NA NA NA NA NA 15 NA NA NA NA NA NA NA NA NA 18 19
[26665] NA NA NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA  9 10 NA NA
[26689] NA NA NA NA NA NA NA 18 19 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA
[26713] NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA 21 22 NA NA NA NA NA NA
[26737] NA NA NA  9 10 NA NA NA NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA NA
[26761] NA 21 22 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA 10
[26785] 11 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA NA 17 NA
[26809] NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA NA 11 NA NA NA
[26833] NA NA NA NA NA NA NA 14 NA NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA
[26857] NA NA NA NA NA 10 NA NA NA NA NA NA NA NA NA NA  7 NA NA NA NA NA NA NA
[26881] NA NA NA 10 NA NA NA NA NA NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA
[26905] NA 17 NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA NA NA NA NA NA 20
[26929] NA NA NA NA NA NA NA NA NA NA 18 NA NA NA NA NA NA NA NA NA NA 28 NA NA
[26953] NA NA NA NA NA NA NA NA  7 NA NA NA NA NA NA NA NA NA NA 21 NA NA NA NA
[26977] NA NA NA NA NA NA 14 NA NA NA NA NA NA NA NA NA NA 20 NA NA NA NA NA NA
[27001] NA NA NA NA 20 NA NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA NA NA NA
[27025] NA 15 16 NA NA NA NA NA NA NA NA NA NA 10 NA NA NA NA NA NA NA NA NA NA
[27049] 13 NA NA NA NA NA NA NA NA NA NA 10 NA NA NA NA NA NA NA NA NA NA 15 NA
[27073] NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA NA NA NA NA NA 20 NA NA NA
[27097] NA NA NA NA NA NA NA 15 NA NA NA NA NA NA NA NA NA NA 20 NA NA NA NA NA
[27121] NA NA NA NA NA 25 NA NA NA NA NA NA NA NA NA NA 20 NA NA NA NA NA NA NA
[27145] NA NA NA 28 NA NA NA NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA
[27169] NA 28 NA NA NA NA NA NA NA NA NA NA 15 NA NA NA NA NA NA NA NA NA NA 16
[27193] NA NA NA NA NA NA NA NA NA NA 15 NA NA NA NA NA NA NA NA NA NA 22 NA NA
[27217] NA NA NA NA NA NA NA NA 21 NA NA NA NA NA NA NA NA NA NA 22 NA NA NA NA
[27241] NA NA NA NA NA NA 30 NA NA NA NA NA NA NA NA NA NA 10 NA NA NA NA NA NA
[27265] NA NA NA NA 18 NA NA NA NA NA NA NA NA NA NA 10 NA NA NA NA NA NA NA NA
[27289] NA NA 13 NA NA NA NA NA NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA NA
[27313] 16 NA NA NA NA NA NA NA NA NA NA  9 NA NA NA NA NA NA NA NA NA NA 30 NA
[27337] NA NA NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA
[27361] NA NA NA NA NA NA NA 14 NA NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA
[27385] NA NA NA NA NA 13 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA
[27409] NA NA NA 27 NA NA NA NA NA NA NA NA NA NA 20 NA NA NA NA NA NA NA NA NA
[27433] NA 13 NA NA NA NA NA NA NA NA NA NA 25 NA NA NA NA NA NA NA NA NA NA 21
[27457] NA NA NA NA NA NA NA NA NA NA 24 NA NA NA NA NA NA NA NA NA NA 14 NA NA
[27481] NA NA NA NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA NA 11 NA NA NA NA
[27505] NA NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA NA  5 NA NA NA NA NA NA
[27529] NA NA NA NA 20 NA NA NA NA NA NA NA NA NA NA 12 NA NA NA NA NA NA NA NA
[27553] NA NA 14 NA NA NA NA NA NA NA NA NA NA  8 NA NA NA NA NA NA NA NA NA NA
[27577]  9 NA NA NA NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA NA 13 NA
[27601] NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA NA NA NA NA NA 17 NA NA NA
[27625] NA NA NA NA NA NA NA 28 NA NA NA NA NA NA NA NA NA NA 18 NA NA NA NA NA
[27649] NA NA NA NA NA 14 NA NA NA NA NA NA NA NA NA NA 19 NA NA NA NA NA NA NA
[27673] NA NA NA 15 NA NA NA NA NA NA NA NA NA NA 20 NA NA NA NA NA NA NA NA NA
[27697] NA 10 NA NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA NA NA NA NA NA 10
[27721] NA NA NA NA NA NA NA NA NA NA 18 NA NA NA NA NA NA NA NA NA NA 16 NA NA
[27745] NA NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA NA 11 NA NA NA NA
[27769] NA NA NA NA NA NA 23 NA NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA NA
[27793] NA NA NA NA  5 NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA NA NA NA
[27817] NA NA 18 NA NA NA NA NA NA NA NA NA NA  9 NA NA NA NA NA NA NA NA NA NA
[27841] 17 NA NA NA NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA NA  7 NA
[27865] NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA NA NA NA NA NA 22 NA NA NA
[27889] NA NA NA NA NA NA NA 14 NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA
[27913] NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA NA  9 NA NA NA NA NA NA NA
[27937] NA NA NA 20 NA NA NA NA NA NA NA NA NA NA 18 NA NA NA NA NA NA NA NA NA
[27961] NA 13 NA NA NA NA NA NA NA NA NA NA 25 NA NA NA NA NA NA NA NA NA NA 29
[27985] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 10 NA NA
[28009] NA NA NA NA NA NA NA NA 23 NA NA NA NA NA NA NA NA NA NA 23 NA NA NA NA
[28033] NA NA NA NA NA NA 13 NA NA NA NA NA NA NA NA NA NA 18 NA NA NA NA NA NA
[28057] NA NA NA NA 19 NA NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA NA NA NA
[28081] NA NA 12 NA NA NA NA NA NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA NA
[28105] NA NA NA NA NA NA NA NA NA NA NA 22 NA NA NA NA NA NA NA NA NA NA 10 NA
[28129] NA NA NA NA NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA NA 40 NA NA NA
[28153] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA
[28177] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA NA NA
[28201] NA NA NA 27 NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA NA NA NA NA
[28225] NA 24 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[28249] NA NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA NA NA NA NA NA 24 NA NA
[28273] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 19 NA NA NA NA
[28297] NA NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA
[28321] NA NA NA NA 13 NA NA NA NA NA NA NA NA NA NA 22 NA NA NA NA NA NA NA NA
[28345] NA NA 23 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[28369] 17 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA  5 NA
[28393] NA NA NA NA NA NA NA NA NA 24 NA NA NA NA NA NA NA NA NA NA 19 NA NA NA
[28417] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA
[28441] NA NA NA NA NA 10 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[28465] NA NA NA 30 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[28489] NA NA NA NA NA NA NA NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA NA NA
[28513] NA NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA NA NA NA NA NA 26 NA NA
[28537] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[28561] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[28585] NA NA NA NA 17 NA NA NA NA NA NA NA NA NA NA  9 NA NA NA NA NA NA NA NA
[28609] NA NA 37 NA NA NA NA NA NA NA NA NA NA  9 NA NA NA NA NA NA NA NA NA NA
[28633] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[28657] NA NA NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA NA 23 NA NA NA
[28681] NA NA NA NA NA NA NA 10 NA NA NA NA NA NA NA NA NA NA 27 NA NA NA NA NA
[28705] NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA NA 12 NA NA NA NA NA NA NA
[28729] NA NA NA NA NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA NA
[28753] NA 16 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 19
[28777] NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA NA NA  9 NA NA
[28801] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[28825] NA NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[28849] NA NA NA NA 16 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[28873] NA NA 16 NA NA NA NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA NA
[28897] 19 NA NA NA NA NA NA NA NA NA NA 22 NA NA NA NA NA NA NA NA NA NA NA NA
[28921] NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA
[28945] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[28969] NA NA NA NA NA 26 NA NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA NA NA
[28993] NA NA NA 12 NA NA NA NA NA NA NA NA NA NA 10 NA NA NA NA NA NA NA NA NA
[29017] NA 13 NA NA NA NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA NA 23
[29041] NA NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA NA NA NA NA NA NA NA NA
[29065] NA NA NA NA NA NA NA NA 10 NA NA NA NA NA NA NA NA NA NA 22 NA NA NA NA
[29089] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 26 NA NA NA NA NA NA
[29113] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA NA NA NA
[29137] NA NA 11 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29161] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 13 NA
[29185] NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA NA NA 18 NA NA NA
[29209] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29233] NA NA NA NA NA 13 NA NA NA NA NA NA NA NA NA NA 21 NA NA NA NA NA NA NA
[29257] NA NA NA NA NA NA NA NA NA NA NA NA NA NA 21 NA NA NA NA NA NA NA NA NA
[29281] NA NA NA NA NA NA NA NA NA NA NA NA 22 NA NA NA NA NA NA NA NA NA NA NA
[29305] NA NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA NA NA NA NA NA NA NA NA
[29329] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29353] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29377] NA NA NA NA 18 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29401] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29425] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29449] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29473] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29497] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29521] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29545] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29569] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29593] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29617] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29641] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29665] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29689] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29713] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29737] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[29761] NA NA NA NA NA NA NA NA NA NA NA 12 13 14 15 16 17 NA NA NA NA NA  8  9
[29785] 10 11 12 13 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA NA 15 16 17 18
[29809] 19 20 NA NA NA NA NA 36 37 38 39 40 41 NA NA NA NA NA 10 11 12 13 14 15
[29833] NA NA NA NA NA 10 11 12 13 14 15 NA NA NA NA NA 15 16 17 18 19 20 NA NA
[29857] NA NA NA 17 18 19 20 21 22 NA NA NA NA NA 15 16 17 18 19 20 19 20 21 22
[29881] 23 24 25 26 27 28 29 11 12 13 14 15 16 17 18 19 20 21 12 13 14 15 16 17
[29905] 18 19 20 21 22 14 15 16 17 18 19 20 21 22 23 24 12 13 14 15 16 17 18 19
[29929] 20 21 22 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21
[29953] 22 17 18 19 20 21 22 23 24 25 26 27 15 16 17 18 19 20 21 22 23 24 25 19
[29977] 20 21 22 23 24 25 26 27 28 29 15 16 17 18 19 20 21 22 23 24 25  9 10 11
[30001] 12 13 14 15 16 17 18 19 23 24 25 26 27 NA NA NA NA NA NA 11 12 13 14 15
[30025] 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19 20 21 13 14 15 16 17 18 19
[30049] 20 21 22 23 11 12 13 14 15 16 17 18 19 20 21 NA NA 21 22 23 24 25 26 27
[30073] 28 29 NA NA NA NA NA NA 16 17 18 19 20 17 18 19 20 21 22 23 24 25 26 27
[30097] 17 18 19 20 21 22 23 24 25 26 27 11 12 13 14 15 16 17 18 19 20 21 16 17
[30121] 18 19 20 21 22 23 24 25 26 16 17 18 19 20 21 22 23 24 25 26 11 12 13 14
[30145] 15 16 17 18 19 20 21 NA NA NA NA NA NA NA NA NA NA 19 12 13 14 15 16 17
[30169] 18 19 20 21 22 11 12 13 14 15 16 17 18 19 20 21 24 25 26 27 28 29 30 31
[30193] 32 33 34 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21
[30217] 22 12 13 14 15 16 17 18 19 20 21 22 19 20 21 22 23 24 25 26 27 28 29 10
[30241] 11 12 13 14 15 16 17 18 19 20 17 18 19 20 21 22 23 24 25 26 27 18 19 20
[30265] 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21 22 23 24 15 16 17 18 19
[30289] 20 21 22 23 24 25 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20
[30313] 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22
[30337] 23 24 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24
[30361] 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 14 15
[30385] 16 17 18 19 20 21 22 23 24 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21
[30409] 22 23 24 25 26 27 28 23 24 25 26 27 28 29 30 31 32 33 17 18 19 20 21 22
[30433] 23 24 25 26 27 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21
[30457] 22 23 24 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23
[30481] 24 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 17
[30505] 18 19 20 21 22 23 24 25 26 27 18 19 20 21 22 23 24 25 26 27 28 15 16 17
[30529] 18 19 20 21 22 23 24 25 14 15 16 17 18 19 20 21 22 23 24 19 20 21 22 23
[30553] 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20
[30577] 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22
[30601] 23 24 18 19 20 21 22 23 24 25 26 27 28 15 16 17 18 19 20 21 22 23 24 25
[30625] 17 18 19 20 21 22 23 24 25 26 27 14 15 16 17 18 19 20 21 22 23 24 14 15
[30649] 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17
[30673] 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 23 24 25 26 27 28
[30697] 29 30 31 32 33 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21
[30721] 22 23 24 17 18 19 20 21 22 23 24 25 26 27 14 15 16 17 18 19 20 21 22 23
[30745] 24 18 19 20 21 22 23 24 25 26 27 28 16 17 18 19 20 21 22 23 24 25 26 23
[30769] 24 25 26 27 28 29 30 31 32 33 17 18 19 20 21 22 23 24 25 26 27 14 15 16
[30793] 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18
[30817] 19 20 21 22 23 24 17 18 19 20 21 22 23 24 25 26 27 16 17 18 19 20 21 22
[30841] 23 24 25 26 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21 22
[30865] 23 24 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24
[30889] 17 18 19 20 21 22 23 24 25 26 27 15 16 17 18 19 20 21 22 23 24 25 13 14
[30913] 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 18 19 20 21
[30937] 22 23 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18
[30961] 19 20 21 22 23 17 18 19 20 21 22 23 24 25 26 27 18 19 20 21 22 23 24 25
[30985] 26 27 28 16 17 18 19 20 21 22 23 24 25 26 18 19 20 21 22 23 24 25 26 27
[31009] 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 13
[31033] 14 15 16 17 18 19 20 21 22 23 19 20 21 22 23 24 25 26 27 28 29 15 16 17
[31057] 18 19 20 21 22 23 24 25 14 15 16 17 18 19 20 21 22 23 24 23 24 25 26 27
[31081] 28 29 30 31 32 33 17 18 19 20 21 22 23 24 25 26 27 18 19 20 21 22 23 24
[31105] 25 26 27 28 13 14 15 16 17 18 19 20 21 22 23 18 19 20 21 22 23 24 25 26
[31129] 27 28 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23
[31153] 18 19 20 21 22 23 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22 23 13 14
[31177] 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 22 23 24 25
[31201] 26 27 28 29 30 31 32 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18
[31225] 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20
[31249] 21 22 23 17 18 19 20 21 22 23 24 25 26 27 13 14 15 16 17 18 19 20 21 22
[31273] 23 13 14 15 16 17 18 19 20 21 22 23 15 16 17 18 19 20 21 22 23 24 25 12
[31297] 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 13 14 15
[31321] 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17
[31345] 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 18 19 20 21 22 23 24
[31369] 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22 18 19 20 21 22 23 24 25 26
[31393] 27 28 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23
[31417] 13 14 15 16 17 18 19 20 21 22 23 18 19 20 21 22 23 24 25 26 27 28 26 27
[31441] 28 29 30 31 32 33 34 35 36 11 12 13 14 15 16 17 18 19 20 21 12 13 14 15
[31465] 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18
[31489] 19 20 21 22 23 14 15 16 17 18 19 20 21 22 23 24 13 14 15 16 17 18 19 20
[31513] 21 22 23 18 19 20 21 22 23 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22
[31537] 23 13 14 15 16 17 18 19 20 21 22 23 15 16 17 18 19 20 21 22 23 24 25 13
[31561] 14 15 16 17 18 19 20 21 22 23 17 18 19 20 21 22 23 24 25 26 27 12 13 14
[31585] 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17
[31609] 18 19 20 21 22 23 17 18 19 20 21 22 23 24 25 26 27 18 19 20 21 22 23 24
[31633] 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20
[31657] 21 22 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23
[31681] 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22 12 13
[31705] 14 15 16 17 18 19 20 21 22 19 20 21 22 23 24 25 26 27 28 29 13 14 15 16
[31729] 17 18 19 20 21 22 23 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19
[31753] 20 21 22 23 24 15 16 17 18 19 20 21 22 23 24 25 13 14 15 16 17 18 19 20
[31777] 21 22 23 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21
[31801] 22 22 23 24 25 26 27 28 29 30 31 32 13 14 15 16 17 18 19 20 21 22 23 18
[31825] 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 12 13 14
[31849] 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16
[31873] 17 18 19 20 21 22 17 18 19 20 21 22 23 24 25 26 27 13 14 15 16 17 18 19
[31897] 20 21 22 23 17 18 19 20 21 22 23 24 25 26 27 17 18 19 20 21 22 23 24 25
[31921] 26 27 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28
[31945] 13 14 15 16 17 18 19 20 21 22 23 19 20 21 22 23 24 25 26 27 28 29 15 16
[31969] 17 18 19 20 21 22 23 24 25 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21
[31993] 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22 18 19 20 21 22 23
[32017] 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20
[32041] 21 22 23 13 14 15 16 17 18 19 20 21 22 23 17 18 19 20 21 22 23 24 25 26
[32065] 27 19 20 21 22 23 24 25 26 27 28 29 12 13 14 15 16 17 18 19 20 21 22 18
[32089] 19 20 21 22 23 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22 23 12 13 14
[32113] 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16
[32137] 17 18 19 20 21 22 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24
[32161] 25 26 27 28 14 15 16 17 18 19 20 21 22 23 24 12 13 14 15 16 17 18 19 20
[32185] 21 22 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23
[32209] 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21 22 13 14
[32233] 15 16 17 18 19 20 21 22 23 11 12 13 14 15 16 17 18 19 20 21 18 19 20 21
[32257] 22 23 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22 23 14 15 16 17 18 19
[32281] 20 21 22 23 24 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20
[32305] 21 22 23 12 13 14 15 16 17 18 19 20 21 22 20 21 22 23 24 25 26 27 28 29
[32329] 30 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 13
[32353] 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 12 13 14
[32377] 15 16 17 18 19 20 21 22 18 19 20 21 22 23 24 25 26 27 28 13 14 15 16 17
[32401] 18 19 20 21 22 23 16 17 18 19 20 21 22 23 24 25 26 13 14 15 16 17 18 19
[32425] 20 21 22 23 17 18 19 20 21 22 23 24 25 26 27 18 19 20 21 22 23 24 25 26
[32449] 27 28 16 17 18 19 20 21 22 23 24 25 26 12 13 14 15 16 17 18 19 20 21 22
[32473] 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21 22 12 13
[32497] 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15
[32521] 16 17 18 19 20 21 22 16 17 18 19 20 21 22 23 24 25 26 18 19 20 21 22 23
[32545] 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19
[32569] 20 21 22 18 19 20 21 22 23 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22
[32593] 23 13 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 12
[32617] 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 16 17 18
[32641] 19 20 21 22 23 24 25 26 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16
[32665] 17 18 19 20 21 22 11 12 13 14 15 16 17 18 19 20 21 19 20 21 22 23 24 25
[32689] 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26
[32713] 27 28 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29
[32737] 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 13 14
[32761] 15 16 17 18 19 20 21 22 23 19 20 21 22 23 24 25 26 27 28 29 12 13 14 15
[32785] 16 17 18 19 20 21 22 17 18 19 20 21 22 23 24 25 26 27 14 15 16 17 18 19
[32809] 20 21 22 23 24 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19
[32833] 20 21 22 13 14 15 16 17 18 19 20 21 22 23 16 17 18 19 20 21 22 23 24 25
[32857] 26 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21 22 18
[32881] 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22 15 16 17
[32905] 18 19 20 21 22 23 24 25 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16
[32929] 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18
[32953] 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 17 18 19 20 21 22 23 24 25
[32977] 26 27 13 14 15 16 17 18 19 20 21 22 23 17 18 19 20 21 22 23 24 25 26 27
[33001] 17 18 19 20 21 22 23 24 25 26 27 13 14 15 16 17 18 19 20 21 22 23 12 13
[33025] 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 15 16 17 18
[33049] 19 20 21 22 23 24 25 14 15 16 17 18 19 20 21 22 23 24 12 13 14 15 16 17
[33073] 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 17 18 19 20 21 22 23 24
[33097] 25 26 27 12 13 14 15 16 17 18 19 20 21 22 19 20 21 22 23 24 25 26 27 28
[33121] 29 18 19 20 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23 24 25 26 27 16
[33145] 17 18 19 20 21 22 23 24 25 26 12 13 14 15 16 17 18 19 20 21 22 13 14 15
[33169] 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21 22 18 19 20 21 22
[33193] 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 12 13 14 15 16 17 18
[33217] 19 20 21 22 14 15 16 17 18 19 20 21 22 23 24 19 20 21 22 23 24 25 26 27
[33241] 28 29 13 14 15 16 17 18 19 20 21 22 23 17 18 19 20 21 22 23 24 25 26 27
[33265] 15 16 17 18 19 20 21 22 23 24 25 12 13 14 15 16 17 18 19 20 21 22 18 19
[33289] 20 21 22 23 24 25 26 27 28 13 14 15 16 17 18 19 20 21 22 23 21 22 23 24
[33313] 25 26 27 28 29 30 31 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17
[33337] 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 28 29 30 31 32 33 34 35
[33361] 36 37 38 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22
[33385] 23 12 13 14 15 16 17 18 19 20 21 22 14 15 16 17 18 19 20 21 22 23 24 12
[33409] 13 14 15 16 17 18 19 20 21 22 16 17 18 19 20 21 22 23 24 25 26 13 14 15
[33433] 16 17 18 19 20 21 22 23 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14
[33457] 15 16 17 18 19 20 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18
[33481] 19 20 21 22 10 11 12 13 14 15 16 17 18 19 20 18 19 20 21 22 23 24 25 26
[33505] 27 28 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22
[33529] 12 13 14 15 16 17 18 19 20 21 22 11 12 13 14 15 16 17 18 19 20 21 14 15
[33553] 16 17 18 19 20 21 22 23 24 16 17 18 19 20 21 22 23 24 25 26 10 11 12 13
[33577] 14 15 16 17 18 19 20 11 12 13 14 15 16 17 18 19 20 21 11 12 13 14 15 16
[33601] 17 18 19 20 21 12 13 14 15 16 17 18 19 20 21 22 14 15 16 17 18 19 20 21
[33625] 22 23 24 18 19 20 21 22 23 24 25 26 27 28 11 12 13 14 15 16 17 18 19 20
[33649] 21 12 13 14 15 16 17 18 19 20 21 22 16 17 18 19 20 21 22 23 24 25 26 12
[33673] 13 14 15 16 17 18 19 20 21 22 11 12 13 14 15 16 17 18 19 20 21 13 14 15
[33697] 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17
[33721] 18 19 20 21 22 23 12 13 14 15 16 17 18 19 20 21 22 17 18 19 20 21 22 23
[33745] 24 25 26 27 12 13 14 15 16 17 18 19 20 21 22 17 18 19 20 21 22 23 24 25
[33769] 26 27 11 12 13 14 15 16 17 18 19 20 21 14 15 16 17 18 19 20 21 22 23 24
[33793] 10 11 12 13 14 15 16 17 18 19 20 13 14 15 16 17 18 19 20 21 22 23 11 12
[33817] 13 14 15 16 17 18 19 20 21 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21
[33841] 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18
[33865] 19 20 21 22 23 11 12 13 14 15 16 17 18 19 20 21 11 12 13 14 15 16 17 18
[33889] 19 20 21 11 12 13 14 15 16 17 18 19 20 21 12 13 14 15 16 17 18 19 20 21
[33913] 22 13 14 15 16 17 18 19 20 21 22 23 18 19 20 21 22 23 24 25 26 27 28 16
[33937] 17 18 19 20 21 22 23 24 25 26 16 17 18 19 20 21 22 23 24 25 26 11 12 13
[33961] 14 15 16 17 18 19 20 21 17 18 19 20 21 22 23 24 25 26 27 12 13 14 15 16
[33985] 17 18 19 20 21 22 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18
[34009] 19 20 21 22 11 12 13 14 15 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19
[34033] 20 21 16 17 18 19 20 21 22 23 24 25 26 20 21 22 23 24 25 26 27 28 29 30
[34057] 11 12 13 14 15 16 17 18 19 20 21 12 13 14 15 16 17 18 19 20 21 22 11 12
[34081] 13 14 15 16 17 18 19 20 21 12 13 14 15 16 17 18 19 20 21 22 15 16 17 18
[34105] 19 20 21 22 23 24 25 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17
[34129] 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18 19
[34153] 20 21 22 17 18 19 20 21 22 23 24 25 26 27 12 13 14 15 16 17 18 19 20 21
[34177] 22 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 12
[34201] 13 14 15 16 17 18 19 20 21 22 16 17 18 19 20 21 22 23 24 25 26 12 13 14
[34225] 15 16 17 18 19 20 21 22 16 17 18 19 20 21 22 23 24 25 26 11 12 13 14 15
[34249] 16 17 18 19 20 21 18 19 20 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18
[34273] 19 20 21 22 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22
[34297] 23 24 12 13 14 15 16 17 18 19 20 21 22 11 12 13 14 15 16 17 18 19 20 21
[34321] 10 11 12 13 14 15 16 17 18 19 20 12 13 14 15 16 17 18 19 20 21 22 12 13
[34345] 14 15 16 17 18 19 20 21 22 25 26 27 28 29 30 31 32 33 34 35 11 12 13 14
[34369] 15 16 17 18 19 20 21 15 16 17 18 19 20 21 22 23 24 25 12 13 14 15 16 17
[34393] 18 19 20 21 22 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21
[34417] 22 23 24 11 12 13 14 15 16 17 18 19 20 21 12 13 14 15 16 17 18 19 20 21
[34441] 22 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 11
[34465] 12 13 14 15 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19 20 21 18 19 20
[34489] 21 22 23 24 25 26 27 28 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16
[34513] 17 18 19 20 21 22 16 17 18 19 20 21 22 23 24 25 26 18 19 20 21 22 23 24
[34537] 25 26 27 28 11 12 13 14 15 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19
[34561] 20 21 15 16 17 18 19 20 21 22 23 24 25 14 15 16 17 18 19 20 21 22 23 24
[34585] 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 11 12
[34609] 13 14 15 16 17 18 19 20 21 18 19 20 21 22 23 24 25 26 27 28 15 16 17 18
[34633] 19 20 21 22 23 24 25 12 13 14 15 16 17 18 19 20 21 22 12 13 14 15 16 17
[34657] 18 19 20 21 22 12 13 14 15 16 17 18 19 20 21 22 18 19 20 21 22 23 24 25
[34681] 26 27 28 17 18 19 20 21 22 23 24 25 26 27 13 14 15 16 17 18 19 20 21 22
[34705] 23 11 12 13 14 15 16 17 18 19 20 21 14 15 16 17 18 19 20 21 22 23 24 12
[34729] 13 14 15 16 17 18 19 20 21 22 17 18 19 20 21 22 23 24 25 26 27 18 19 20
[34753] 21 22 23 24 25 26 27 28 11 12 13 14 15 16 17 18 19 20 21 15 16 17 18 19
[34777] 20 21 22 23 24 25 18 19 20 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20
[34801] 21 22 23 24 11 12 13 14 15 16 17 18 19 20 21 19 20 21 22 23 24 25 26 27
[34825] 28 29 11 12 13 14 15 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19 20 21
[34849] 12 13 14 15 16 17 18 19 20 21 22 13 14 15 16 17 18 19 20 21 22 23 11 12
[34873] 13 14 15 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19 20 21 11 12 13 14
[34897] 15 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19 20 21 14 15 16 17 18 19
[34921] 20 21 22 23 24 12 13 14 15 16 17 18 19 20 21 22 18 19 20 21 22 23 24 25
[34945] 26 27 28 12 13 14 15 16 17 18 19 20 21 22 14 15 16 17 18 19 20 21 22 23
[34969] 24 15 16 17 18 19 20 21 22 23 24 25 14 15 16 17 18 19 20 21 22 23 24 18
[34993] 19 20 21 22 23 24 25 26 27 28 11 12 13 14 15 16 17 18 19 20 21 18 19 20
[35017] 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 15 16 17 18 19
[35041] 20 21 22 23 24 25 13 14 15 16 17 18 19 20 21 22 23 12 13 14 15 16 17 18
[35065] 19 20 21 22 18 19 20 21 22 23 24 25 26 27 28 26 27 28 29 30 31 32 33 34
[35089] 35 36 24 25 26 27 28 29 30 31 32 33 34 23 24 25 26 27 28 29 30 31 32 33
[35113] 20 21 22 23 24 25 26 27 28 29 30 20 21 22 23 24 25 26 27 28 29 30 19 20
[35137] 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 23 24 25 26
[35161] 27 28 29 30 31 32 33 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24
[35185] 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26
[35209] 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28
[35233] 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19
[35257] 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21
[35281] 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23
[35305] 24 25 26 27 28 29 21 22 23 24 25 26 27 28 29 30 31 25 26 27 28 29 30 31
[35329] 32 33 34 35 23 24 25 26 27 28 29 30 31 32 33 19 20 21 22 23 24 25 26 27
[35353] 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29
[35377] 20 21 22 23 24 25 26 27 28 29 30 19 20 21 22 23 24 25 26 27 28 29 19 20
[35401] 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22
[35425] 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 20 21 22 23 24 25
[35449] 26 27 28 29 30 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26
[35473] 27 28 29 23 24 25 26 27 28 29 30 31 32 33 19 20 21 22 23 24 25 26 27 28
[35497] 29 20 21 22 23 24 25 26 27 28 29 30 19 20 21 22 23 24 25 26 27 28 29 19
[35521] 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 27 28 29
[35545] 30 31 32 33 34 35 36 37 25 26 27 28 29 30 31 32 33 34 35 24 25 26 27 28
[35569] 29 30 31 32 33 34 19 20 21 22 23 24 25 26 27 28 29 27 28 29 30 31 32 33
[35593] 34 35 36 37 25 26 27 28 29 30 31 32 33 34 35 19 20 21 22 23 24 25 26 27
[35617] 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29
[35641] 19 20 21 22 23 24 25 26 27 28 29 20 21 22 23 24 25 26 27 28 29 30 19 20
[35665] 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 21 22 23 24
[35689] 25 26 27 28 29 30 31 22 23 24 25 26 27 28 29 30 31 32 18 19 20 21 22 23
[35713] 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 23 24 25 26 27 28 29 30
[35737] 31 32 33 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27
[35761] 28 23 24 25 26 27 28 29 30 31 32 33 18 19 20 21 22 23 24 25 26 27 28 19
[35785] 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21
[35809] 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22
[35833] 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24
[35857] 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 22 23 24 25 26 27 28 29 30
[35881] 31 32 23 24 25 26 27 28 29 30 31 32 33 18 19 20 21 22 23 24 25 26 27 28
[35905] 21 22 23 24 25 26 27 28 29 30 31 18 19 20 21 22 23 24 25 26 27 28 22 23
[35929] 24 25 26 27 28 29 30 31 32 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21
[35953] 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23
[35977] 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26
[36001] 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28
[36025] 29 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 15
[36049] 16 17 18 19 20 21 22 23 24 25 19 20 21 22 23 24 25 26 27 28 29 18 19 20
[36073] 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23 24 25 26 27 15 16 17 18 19
[36097] 20 21 22 23 24 25 18 19 20 21 22 23 24 25 26 27 28 15 16 17 18 19 20 21
[36121] 22 23 24 25 19 20 21 22 23 24 25 26 27 28 29 20 21 22 23 24 25 26 27 28
[36145] 29 30 18 19 20 21 22 23 24 25 26 27 28 15 16 17 18 19 20 21 22 23 24 25
[36169] 15 16 17 18 19 20 21 22 23 24 25 17 18 19 20 21 22 23 24 25 26 27 18 19
[36193] 20 21 22 23 24 25 26 27 28 15 16 17 18 19 20 21 22 23 24 25 18 19 20 21
[36217] 22 23 24 25 26 27 28 15 16 17 18 19 20 21 22 23 24 25 18 19 20 21 22 23
[36241] 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26
[36265] 27 28 29 17 18 19 20 21 22 23 24 25 26 27 15 16 17 18 19 20 21 22 23 24
[36289] 25 19 20 21 22 23 24 25 26 27 28 29 15 16 17 18 19 20 21 22 23 24 25 18
[36313] 19 20 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23 24 25 26 27 18 19 20
[36337] 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22
[36361] 23 24 25 26 27 28 15 16 17 18 19 20 21 22 23 24 25 19 20 21 22 23 24 25
[36385] 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27
[36409] 28 29 18 19 20 21 22 23 24 25 26 27 28 21 22 23 24 25 26 27 28 29 30 31
[36433] 15 16 17 18 19 20 21 22 23 24 25 15 16 17 18 19 20 21 22 23 24 25 15 16
[36457] 17 18 19 20 21 22 23 24 25 15 16 17 18 19 20 21 22 23 24 25 19 20 21 22
[36481] 23 24 25 26 27 28 29 15 16 17 18 19 20 21 22 23 24 25 18 19 20 21 22 23
[36505] 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23 24
[36529] 25 26 27 15 16 17 18 19 20 21 22 23 24 25 18 19 20 21 22 23 24 25 26 27
[36553] 28 27 28 29 30 31 32 33 34 35 36 37 18 19 20 21 22 23 24 25 26 27 28 18
[36577] 19 20 21 22 23 24 25 26 27 28 16 17 18 19 20 21 22 23 24 25 26 18 19 20
[36601] 21 22 23 24 25 26 27 28 15 16 17 18 19 20 21 22 23 24 25 22 23 24 25 26
[36625] 27 28 29 30 31 32 18 19 20 21 22 23 24 25 26 27 28 30 31 32 33 34 35 36
[36649] 37 38 39 40 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27
[36673] 28 29 15 16 17 18 19 20 21 22 23 24 25 15 16 17 18 19 20 21 22 23 24 25
[36697] 21 22 23 24 25 26 27 28 29 30 31 19 20 21 22 23 24 25 26 27 28 29 15 16
[36721] 17 18 19 20 21 22 23 24 25 22 23 24 25 26 27 28 29 30 31 32 18 19 20 21
[36745] 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23 24
[36769] 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 15 16 17 18 19 20 21 22
[36793] 23 24 25 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27
[36817] 28 14 15 16 17 18 19 20 21 22 23 24 14 15 16 17 18 19 20 21 22 23 24 18
[36841] 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20
[36865] 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21 22 23 24 22 23 24 25 26
[36889] 27 28 29 30 31 32 17 18 19 20 21 22 23 24 25 26 27 19 20 21 22 23 24 25
[36913] 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23 24 25
[36937] 26 27 14 15 16 17 18 19 20 21 22 23 24 18 19 20 21 22 23 24 25 26 27 28
[36961] 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 19 20
[36985] 21 22 23 24 25 26 27 28 29 26 27 28 29 30 31 32 33 34 35 36 18 19 20 21
[37009] 22 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23
[37033] 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25
[37057] 26 27 28 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28
[37081] 29 19 20 21 22 23 24 25 26 27 28 29 17 18 19 20 21 22 23 24 25 26 27 24
[37105] 25 26 27 28 29 30 31 32 33 34 19 20 21 22 23 24 25 26 27 28 29 18 19 20
[37129] 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22
[37153] 23 24 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24
[37177] 25 26 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26
[37201] 27 28 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28
[37225] 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 17 18
[37249] 19 20 21 22 23 24 25 26 27 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22
[37273] 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24
[37297] 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23 24
[37321] 25 26 27 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27
[37345] 28 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 18
[37369] 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 17 18 19
[37393] 20 21 22 23 24 25 26 27 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22 23
[37417] 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 17 18 19 20 21 22 23
[37441] 24 25 26 27 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26
[37465] 27 28 23 24 25 26 27 28 29 30 31 32 33 25 26 27 28 29 30 31 32 33 34 35
[37489] 19 20 21 22 23 24 25 26 27 28 29 18 19 20 21 22 23 24 25 26 27 28 18 19
[37513] 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 19 20 21 22
[37537] 23 24 25 26 27 28 29 22 23 24 25 26 27 28 29 30 31 32 17 18 19 20 21 22
[37561] 23 24 25 26 27 17 18 19 20 21 22 23 24 25 26 27 19 20 21 22 23 24 25 26
[37585] 27 28 29 19 20 21 22 23 24 25 26 27 28 29 19 20 21 22 23 24 25 26 27 28
[37609] 29 18 19 20 21 22 23 24 25 26 27 28 16 17 18 19 20 21 22 23 24 25 26 18
[37633] 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22 23 24 25 26 27 28 16 17 18
[37657] 19 20 21 22 23 24 25 26 18 19 20 21 22 23 24 25 26 27 28 18 19 20 21 22
[37681] 23 24 25 26 27 28 17 18 19 20 21 22 23 24 25 26 27 24 25 26 27 28 29 30
[37705] 31 32 33 34 25 26 27 28 29 30 31 32 33 34 35 23 24 25 26 27 28 29 30 31
[37729] 32 33 25 26 27 28 29 30 31 32 33 34 35 23 24 25 26 27 28 29 30 31 32 33
[37753] 21 22 23 24 25 26 27 28 29 30 31 19 20 21 22 23 24 25 26 27 28 29 21 22
[37777] 23 24 25 26 27 28 29 30 31 22 23 24 25 26 27 28 29 30 31 32 18 19 20 21
[37801] 22 23 24 25 26 27 28 15 16 17 18 19 20 21 22 23 24 25 19 20 21 22 23 24
[37825] 25 26 27 28 29 21 22 23 24 25 26 27 28 29 30 31 18 19 20 21 22 23 24 25
[37849] 26 27 28 18 19 20 21 22 23 24 25 26 27 28 15 16 17 18 19 20 21 22 23 24
[37873] 25 15 16 17 18 19 20 21 22 23 24 25 22 23 24 25 26 27 28 29 30 31 32  9
[37897] 10 11 12 13 14 15 16 17 18 19 NA NA NA NA NA NA 29 30 31 32 33 NA NA NA
[37921] NA NA NA 14 15 16 17 18 NA NA NA NA NA NA NA NA NA NA 36 NA NA NA NA NA
[37945] NA NA NA NA NA 19 NA NA NA NA NA 11 12 13 14 15 16 NA NA NA NA NA 15 16
[37969] 17 18 19 20 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA 19 20 21 22 23 24
[37993] 25 26 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA NA  9 10 11 12 13 14
[38017] NA NA NA NA NA 20 21 22 23 24 25 NA NA NA NA NA NA NA NA NA NA 29 NA NA
[38041] NA NA NA NA NA 21 22 23 24 NA NA NA NA NA NA NA  7  8  9 10 NA NA NA NA
[38065] NA NA 14 15 16 17 18 NA NA NA NA NA NA NA NA NA NA 12 NA NA NA NA NA 16
[38089] 17 18 19 20 21 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 18 19 20
[38113] 21 22 23 NA NA NA NA NA NA NA NA NA NA 24 NA NA NA NA NA NA NA NA NA NA
[38137] 11 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA 18 19 20 21 22 23 24 NA
[38161] NA NA NA  7  8  9 10 11 12 13 12 13 14 15 16 17 18 19 20 21 22 11 12 13
[38185] 14 15 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18 19 20 11 12 13 14 15
[38209] 16 17 18 19 20 21 13 14 15 16 17 18 19 20 21 22 23 17 18 19 20 21 22 23
[38233] 24 25 26 27 11 12 13 14 15 16 17 18 19 20 21 NA NA NA NA NA NA NA NA 14
[38257] 15 16 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18 19 20
[38281] 11 12 13 14 15 16 17 18 19 20 21 11 12 13 14 15 16 17 18 19 20 21 10 11
[38305] 12 13 14 15 16 17 18 19 20 18 19 20 21 22 23 24 25 26 27 28 NA NA NA NA
[38329] NA  6  7  8  9 10 11 12 13 14 15 16 17 18 19 20 21 22 11 12 13 14 15 16
[38353] 17 18 19 20 21 19 20 21 22 23 24 25 26 27 28 29 16 17 18 19 20 21 22 23
[38377] 24 25 26 11 12 13 14 15 16 17 18 19 20 21 10 11 12 13 14 15 16 17 18 19
[38401] 20 12 13 14 15 16 17 18 19 20 21 22 18 19 20 21 22 23 24 25 26 27 28 13
[38425] 14 15 16 17 18 19 20 21 22 23 13 14 15 16 17 18 19 20 21 22 23 NA NA NA
[38449] NA NA NA NA NA 14 15 16 11 12 13 14 15 16 17 18 19 20 21 11 12 13 14 15
[38473] 16 17 18 19 20 21 17 18 19 20 21 22 23 24 25 26 27 14 15 16 17 18 19 20
[38497] 21 22 23 24 NA NA NA NA NA  8  9 10 11 12 13 10 11 12 13 14 15 16 17 18
[38521] 19 20 12 13 14 15 16 17 18 19 20 21 22 18 19 20 21 22 23 24 25 26 27 28
[38545]  8  9 10 11 12 13 14 15 16 17 18 10 11 12 13 14 15 16 17 18 19 20 16 17
[38569] 18 19 20 21 22 23 24 25 26 11 12 13 14 15 16 17 18 19 20 21 14 15 16 17
[38593] 18 19 20 21 22 23 24  7  8  9 10 11 12 13 14 15 16 17  9 10 11 12 13 14
[38617] 15 16 17 18 19 10 11 12 13 14 15 16 17 18 19 20 15 16 17 18 19 20 21 22
[38641] 23 24 25 16 17 18 19 20 21 22 23 24 25 26  8  9 10 11 12 13 14 15 16 17
[38665] 18 NA 12 13 14 15 16 17 18 19 20 21 NA NA NA 11 12 13 14 15 16 17 18 NA
[38689] NA NA  8  9 10 11 12 13 14 15  3  4  5  6  7  8  9 10 11 12 13 NA NA NA
[38713] NA NA NA  6  7  8  9 10 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA 10 11
[38737] 12 13 14 15 16 17 12 13 14 15 16 17 18 19 20 21 22 NA 15 16 17 18 19 20
[38761] 21 22 23 24  9 10 11 12 13 14 15 16 17 18 19 NA NA NA NA NA NA NA 13 14
[38785] 15 16 NA NA NA NA NA NA NA 15 16 17 18 NA NA NA 12 13 14 15 16 17 18 19
[38809] NA NA NA NA NA NA NA NA 11 12 13 NA NA NA NA NA NA 21 22 23 24 25  7  8
[38833]  9 10 11 12 13 14 15 16 17 NA NA NA NA NA NA NA NA NA NA NA  3  4  5  6
[38857]  7  8  9 10 11 12 13  7  8  9 10 11 12 13 14 15 16 17  6  7  8  9 10 11
[38881] 12 13 14 15 16  9 10 11 12 13 14 15 16 17 18 19  3  4  5  6  7  8  9 10
[38905] 11 12 13 14 15 16 17 18 19 20 21 22 23 24 NA NA NA NA NA NA 14 15 16 17
[38929] 18  7  8  9 10 11 12 13 14 15 16 17  9 10 11 12 13 14 15 16 17 18 19 16
[38953] 17 18 19 20 21 22 23 24 25 26  3  4  5  6  7  8  9 10 11 12 13  6  7  8
[38977]  9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 NA NA NA NA NA
[39001]  9 10 11 12 13 14 NA NA NA NA NA 15 16 17 18 19 20 18 19 20 21 22 23 24
[39025] 25 26 27 28 NA NA NA NA NA NA NA  3  4  5  6 15 16 17 18 19 20 21 22 23
[39049] 24 25 NA NA NA NA 19 20 21 22 23 24 25 NA NA NA NA NA NA NA NA NA NA 17
[39073] 10 11 12 13 14 15 16 17 18 19 20 NA NA NA NA NA NA NA 18 19 20 21 NA NA
[39097] NA NA NA 15 16 17 18 19 20 23 24 25 26 27 28 29 30 31 32 33 11 12 13 14
[39121] 15 16 17 18 19 20 21 15 16 17 18 19 20 21 22 23 24 25 14 15 16 17 18 19
[39145] 20 21 22 23 24  3  4  5  6  7  8  9 10 11 12 13  4  5  6  7  8  9 10 11
[39169] 12 13 14 NA NA NA NA NA NA NA NA NA 24 25  1  2  3  4  5  6  7  8  9 10
[39193] 11 NA NA NA NA  7  8  9 10 11 12 13  8  9 10 11 12 13 14 15 16 17 18 NA
[39217] NA NA NA NA NA 13 14 15 16 17 17 18 19 20 21 22 23 24 25 26 27 NA NA NA
[39241] NA 16 17 18 19 20 21 22 NA NA NA NA NA  9 10 11 12 13 14  8  9 10 11 12
[39265] 13 14 15 16 17 18  8  9 10 11 12 13 14 15 16 17 18 16 17 18 19 20 21 22
[39289] 23 24 25 26 NA NA  5  6  7  8  9 10 11 12 13 NA NA NA  6  7  8  9 10 11
[39313] 12 13 NA NA NA NA NA NA  7  8  9 10 11 NA NA NA NA NA 20 21 22 23 24 25
[39337]  8  9 10 11 12 13 14 15 16 17 18 12 13 14 15 16 17 18 19 20 21 22 NA NA
[39361] NA NA NA NA NA NA 17 18 19 NA 15 16 17 18 19 20 21 22 23 24  8  9 10 11
[39385] 12 13 14 15 16 17 18 NA NA NA NA NA NA NA NA NA NA 15  2  3  4  5  6  7
[39409]  8  9 10 11 12 NA NA NA NA NA NA NA  5  6  7  8 17 18 19 20 21 22 23 24
[39433] 25 26 27 23 24 25 26 27 28 29 30 31 32 33 NA NA NA NA NA NA NA 22 23 24
[39457] 25 NA NA NA NA NA NA  9 10 11 12 13 24 25 26 27 28 29 30 31 32 33 34 NA
[39481] NA NA NA NA NA NA 18 19 20 21 15 16 17 18 19 20 21 22 23 24 25 15 16 17
[39505] 18 19 20 21 22 23 24 25 NA  8  9 10 11 12 13 14 15 16 17 NA NA NA NA NA
[39529] NA NA 26 27 28 29 NA NA NA NA NA NA 19 20 21 22 23 NA NA NA NA  9 10 11
[39553] 12 13 14 15 12 13 14 15 16 17 18 19 20 21 22 NA NA NA NA NA 14 15 16 17
[39577] 18 19 11 12 13 14 15 16 17 18 19 20 21 NA 11 12 13 14 15 16 17 18 19 20
[39601]  4  5  6  7  8  9 10 11 12 13 14 27 28 29 30 31 32 33 34 35 36 37 NA NA
[39625] NA NA NA NA NA NA NA 19 20 NA NA NA NA NA NA NA NA NA 21 22 NA NA NA NA
[39649] NA 14 15 16 17 18 19 NA NA NA NA NA NA NA NA 25 26 27 28 29 30 31 32 33
[39673] 34 35 36 37 38  1  2  3  4  5  6  7  8  9 10 11 11 12 13 14 15 16 17 18
[39697] 19 20 21 NA NA NA NA NA NA NA NA NA NA 16 18 19 20 21 22 23 24 25 26 27
[39721] 28  7  8  9 10 11 12 13 14 15 16 17  4  5  6  7  8  9 10 11 12 13 14 NA
[39745] NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA 21 22 23 24 NA NA NA
[39769] 21 22 23 24 25 26 27 28 14 15 16 17 18 19 20 21 22 23 24 NA NA NA NA NA
[39793] 22 23 24 25 26 27 NA NA NA NA NA NA NA 16 17 18 19  9 10 11 12 13 14 15
[39817] 16 17 18 19 10 11 12 13 14 15 16 17 18 19 20 20 21 22 23 24 25 26 27 28
[39841] 29 30 NA NA NA NA NA NA NA 18 19 20 21 17 18 19 20 21 22 23 24 25 26 27
[39865] NA 29 30 31 32 33 34 35 36 37 38 NA NA NA NA NA NA NA NA NA 28 29 NA NA
[39889] NA NA NA  6  7  8  9 10 11 NA NA NA NA NA NA NA 12 13 14 15  4  5  6  7
[39913]  8  9 10 11 12 13 14  3  4  5  6  7  8  9 10 11 12 13 NA NA NA NA NA NA
[39937]  7  8  9 10 11  8  9 10 11 12 13 14 15 16 17 18 13 14 15 16 17 18 19 20
[39961] 21 22 23  6  7  8  9 10 11 12 13 14 15 16 NA NA NA NA NA NA NA NA 12 13
[39985] 14 NA NA NA NA NA NA NA NA NA NA 25 13 14 15 16 17 18 19 20 21 22 23  4
[40009]  5  6  7  8  9 10 11 12 13 14  2  3  4  5  6  7  8  9 10 11 12 14 15 16
[40033] 17 18 19 20 21 22 23 24  4  5  6  7  8  9 10 11 12 13 14 NA NA NA NA NA
[40057] NA NA NA NA  9 10  4  5  6  7  8  9 10 11 12 13 14 NA NA NA NA 11 12 13
[40081] 14 15 16 17 NA NA NA NA NA NA NA NA 17 18 19 NA NA NA NA NA NA NA NA 18
[40105] 19 20 10 11 12 13 14 15 16 17 18 19 20 18 19 20 21 22 23 24 25 26 27 28
[40129] NA NA NA NA NA NA NA NA NA 12 13  3  4  5  6  7  8  9 10 11 12 13  2  3
[40153]  4  5  6  7  8  9 10 11 12  7  8  9 10 11 12 13 14 15 16 17 NA NA NA NA
[40177] NA NA NA NA NA NA 19  4  5  6  7  8  9 10 11 12 13 14 14 15 16 17 18 19
[40201] 20 21 22 23 24 NA NA NA NA NA NA NA NA NA  9 10 15 16 17 18 19 20 21 22
[40225] 23 24 25  7  8  9 10 11 12 13 14 15 16 17 10 11 12 13 14 15 16 17 18 19
[40249] 20 11 12 13 14 15 16 17 18 19 20 21  5  6  7  8  9 10 11 12 13 14 15  9
[40273] 10 11 12 13 14 15 16 17 18 19  9 10 11 12 13 14 15 16 17 18 19 NA NA NA
[40297] NA NA 11 12 13 14 15 16 NA NA NA NA NA NA NA NA 11 12 13 19 20 21 22 23
[40321] 24 25 26 27 28 29 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA
[40345] NA NA 11 12 NA NA NA NA NA NA 10 11 12 13 14 NA NA NA NA NA NA 20 21 22
[40369] 23 24 12 13 14 15 16 17 18 19 20 21 22 NA NA NA NA NA  9 10 11 12 13 14
[40393] NA NA NA NA NA NA NA  7  8  9 10 NA NA NA NA NA NA NA 20 21 22 23  7  8
[40417]  9 10 11 12 13 14 15 16 17 NA NA NA NA NA NA NA NA NA NA  7 NA NA NA NA
[40441] NA NA NA 30 31 32 33  7  8  9 10 11 12 13 14 15 16 17 14 15 16 17 18 19
[40465] 20 21 22 23 24 NA NA NA NA NA NA 15 16 17 18 19  6  7  8  9 10 11 12 13
[40489] 14 15 16 NA NA NA NA NA  9 10 11 12 13 14 NA NA NA NA NA 14 15 16 17 18
[40513] 19 12 13 14 15 16 17 18 19 20 21 22  3  4  5  6  7  8  9 10 11 12 13 11
[40537] 12 13 14 15 16 17 18 19 20 21  5  6  7  8  9 10 11 12 13 14 15 11 12 13
[40561] 14 15 16 17 18 19 20 21 NA NA NA NA  6  7  8  9 10 11 12 17 18 19 20 21
[40585] 22 23 24 25 26 27  9 10 11 12 13 14 15 16 17 18 19 NA NA NA NA NA NA NA
[40609] NA 26 27 28 14 15 16 17 18 19 20 21 22 23 24  6  7  8  9 10 11 12 13 14
[40633] 15 16 NA NA NA NA NA NA NA NA NA NA  5 12 13 14 15 16 17 18 19 20 21 22
[40657] 10 11 12 13 14 15 16 17 18 19 20 10 11 12 13 14 15 16 17 18 19 20  7  8
[40681]  9 10 11 12 13 14 15 16 17 NA NA NA NA NA NA  6  7  8  9 10 NA NA NA NA
[40705] NA NA  9 10 11 12 13 NA NA NA NA NA NA NA NA NA 18 19  4  5  6  7  8  9
[40729] 10 11 12 13 14 NA NA NA NA NA NA NA NA NA 24 25 NA 11 12 13 14 15 16 17
[40753] 18 19 20 NA NA NA NA NA 22 23 24 25 26 27 NA NA NA NA 11 12 13 14 15 16
[40777] 17 NA NA NA  7  8  9 10 11 12 13 14 NA NA NA NA NA NA NA 25 26 27 28 NA
[40801] NA NA NA  7  8  9 10 11 12 13 28 29 30 31 32 33 34 35 36 37 38  3  4  5
[40825]  6  7  8  9 10 11 12 13 NA NA NA NA NA NA NA 28 29 30 31 17 18 19 20 21
[40849] 22 23 24 25 26 27  6  7  8  9 10 11 12 13 14 15 16 NA NA NA NA NA NA NA
[40873] NA NA 25 26 11 12 13 14 15 16 17 18 19 20 21 NA NA NA NA NA 19 20 21 22
[40897] 23 24 24 25 26 27 28 29 30 31 32 33 34  5  6  7  8  9 10 11 12 13 14 15
[40921] NA  7  8  9 10 11 12 13 14 15 16 NA 11 12 13 14 15 16 17 18 19 20 NA 17
[40945] 18 19 20 21 22 23 24 25 26 NA 15 16 17 18 19 20 21 22 23 24 NA NA  8  9
[40969] 10 11 12 13 14 15 16 NA NA NA 11 12 13 14 15 16 17 18 NA NA NA NA NA 13
[40993] 14 15 16 17 18 NA 16 17 18 19 20 21 22 23 24 25 NA NA NA 12 13 14 15 16
[41017] 17 18 19 NA NA NA 14 15 16 17 18 19 20 21 NA NA NA 17 18 19 20 21 22 23
[41041] 24 NA NA NA 11 12 13 14 15 16 17 18 NA NA NA NA NA NA NA 25 26 27 28 NA
[41065] NA NA NA 14 15 16 17 18 19 20 NA NA NA NA NA  8  9 10 11 12 13 NA NA NA
[41089] 19 20 21 22 23 24 25 26 NA NA NA  9 10 11 12 13 14 15 16 NA NA NA  8  9
[41113] 10 11 12 13 14 15 NA NA NA 19 20 21 22 23 24 25 26 NA NA NA NA 17 18 19
[41137] 20 21 22 23 NA NA NA NA 16 17 18 19 20 21 22 NA NA NA NA 15 16 17 18 19
[41161] 20 21 NA NA NA NA 15 16 17 18 19 20 21 NA NA NA NA 13 14 15 16 17 18 19
[41185] NA NA NA NA NA 20 21 22 23 24 25 NA NA NA NA NA 11 12 13 14 15 16 NA NA
[41209] NA NA NA 14 15 16 17 18 19 NA NA NA NA 17 18 19 20 21 22 23 NA NA NA NA
[41233] NA 17 18 19 20 21 22 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 19
[41257] 20 21 22 23 24 NA NA NA NA NA 20 21 22 23 24 25 NA NA NA NA NA 27 28 29
[41281] 30 31 32 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 21 22 23 24 25
[41305] 26 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA NA 14 15 16 17 18 19 NA
[41329] NA NA NA NA NA 17 18 19 20 21 NA NA NA NA NA NA 19 20 21 22 23 NA NA NA
[41353] NA NA NA 25 26 27 28 29 NA NA NA NA NA NA NA 19 20 21 22 NA NA NA NA NA
[41377] NA NA NA NA NA 24 NA NA NA NA NA NA 17 18 19 20 21 NA NA NA NA NA NA NA
[41401] NA NA NA 23 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 33
[41425] 34 35 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA  8  9 10 11 12
[41449] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 19 20 21 22 23 24 NA NA
[41473] NA NA NA NA NA 21 22 23 24 NA NA NA NA NA 32 33 34 35 36 37 NA NA NA NA
[41497] NA NA 10 11 12 13 14 NA NA NA NA 13 14 15 16 17 18 19 NA NA NA NA NA 13
[41521] 14 15 16 17 18 NA NA NA NA NA NA NA NA 15 16 17 NA NA NA NA NA 31 32 33
[41545] 34 35 36 NA NA NA NA NA NA NA NA NA NA  5 NA NA NA NA NA NA NA NA NA NA
[41569] 21 NA NA NA NA NA NA 19 20 21 22 23 NA 10 11 12 13 14 15 16 17 18 19 NA
[41593] NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA 18 19 20 21 22 NA NA NA
[41617] NA NA NA 14 15 16 17 18 NA NA NA NA NA NA 15 16 17 18 19 NA NA NA NA NA
[41641] NA 11 12 13 14 15 NA NA NA NA 15 16 17 18 19 20 21 NA NA NA NA NA NA 15
[41665] 16 17 18 19 NA NA NA NA NA NA NA NA NA 22 23 NA NA NA 12 13 14 15 16 17
[41689] 18 19 NA NA NA NA NA 18 19 20 21 22 23 NA NA NA NA NA 16 17 18 19 20 21
[41713] NA NA NA NA NA NA NA NA 23 24 25 NA NA NA NA NA NA NA NA NA 20 21 NA NA
[41737] NA NA NA NA  8  9 10 11 12 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[41761] NA 15 16 17 18 19 20 NA NA NA  9 10 11 12 13 14 15 16 NA NA NA 15 16 17
[41785] 18 19 20 21 22 NA NA NA NA NA  8  9 10 11 12 13 NA NA NA NA NA NA NA NA
[41809] NA NA 16 NA NA NA NA NA NA  9 10 11 12 13 NA NA NA NA NA NA NA 10 11 12
[41833] 13 NA NA NA NA NA NA 13 14 15 16 17 NA NA NA NA 16 17 18 19 20 21 22 NA
[41857] NA NA NA NA NA NA NA 26 27 28 NA NA NA NA NA NA 23 24 25 26 27 NA NA NA
[41881] 14 15 16 17 18 19 20 21 NA NA NA NA NA NA NA NA NA 12 13 NA NA NA NA NA
[41905] NA 13 14 15 16 17 NA NA NA NA NA NA NA NA 21 22 23 NA NA NA NA  8  9 10
[41929] 11 12 13 14 NA NA NA NA  8  9 10 11 12 13 14 NA NA NA NA 17 18 19 20 21
[41953] 22 23 NA NA NA NA NA NA NA 12 13 14 15 NA NA NA NA NA NA NA NA NA NA 21
[41977] NA  9 10 11 12 13 14 15 16 17 18 NA NA NA 16 17 18 19 20 21 22 23 NA NA
[42001] NA NA NA NA 22 23 24 25 26 NA 28 29 30 31 32 33 34 35 36 37 NA NA NA NA
[42025] NA NA 14 15 16 17 18 NA NA NA NA NA 21 22 23 24 25 26 NA NA NA NA NA NA
[42049] NA NA NA NA NA NA NA NA NA NA NA  7  8  9 10 11 NA NA NA NA NA NA 25 26
[42073] 27 28 29 NA NA NA NA NA NA 10 11 12 13 14 NA NA NA NA NA NA  8  9 10 11
[42097] 12 NA NA NA NA NA NA NA NA NA NA 22 NA NA NA NA NA NA NA NA NA NA NA NA
[42121] NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA 20 21 22 23 24 NA NA NA
[42145] NA 10 11 12 13 14 15 16 NA NA NA NA NA 19 20 21 22 23 24 NA NA NA NA NA
[42169] 14 15 16 17 18 19 NA NA NA NA NA NA 15 16 17 18 19 NA NA NA NA NA NA NA
[42193] NA NA NA NA NA NA NA NA NA 18 19 20 21 22 23 NA NA NA 14 15 16 17 18 19
[42217] 20 21 NA 14 15 16 17 18 19 20 21 22 23 NA NA NA 14 15 16 17 18 19 20 21
[42241] NA NA NA 13 14 15 16 17 18 19 20 NA NA NA NA NA NA NA NA NA NA NA NA NA
[42265] NA NA NA NA NA NA NA NA 22 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA
[42289] NA 11 12 13 14 15 16 NA NA NA NA NA NA 10 11 12 13 14 NA NA NA NA NA NA
[42313] 16 17 18 19 20 NA NA NA NA NA NA 18 19 20 21 22 NA NA NA NA NA NA 14 15
[42337] 16 17 18 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 16 17 18 19
[42361] 20 NA NA NA NA NA NA NA 16 17 18 19 NA NA NA NA NA 13 14 15 16 17 18 NA
[42385] NA NA NA NA NA NA 12 13 14 15 NA NA NA  8  9 10 11 12 13 14 15 NA NA NA
[42409] NA NA  7  8  9 10 11 12 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[42433] NA NA 11 12 13 14 NA NA NA NA NA NA NA NA 11 12 13 NA NA NA NA NA NA 15
[42457] 16 17 18 19 NA NA NA NA NA NA 20 21 22 23 24 NA NA NA NA 12 13 14 15 16
[42481] 17 18 NA NA NA 14 15 16 17 18 19 20 21 NA NA NA NA NA NA 23 24 25 26 27
[42505] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 10 11 12 13 14 15 NA NA
[42529] NA NA NA NA NA NA NA NA NA NA NA NA NA NA 10 11 12 13 14 15 NA NA NA NA
[42553] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[42577] NA NA NA 12 13 NA NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA NA NA 13
[42601] 14 15 16 NA NA NA NA NA NA NA NA NA NA 20 NA NA NA NA NA NA NA NA NA NA
[42625] 18 NA NA NA NA NA NA NA NA 16 17 18 NA NA NA NA NA  9 10 11 12 13 14 NA
[42649] NA NA NA NA NA NA NA NA NA 19 NA NA NA NA NA NA NA 19 20 21 22 NA NA NA
[42673] NA NA NA NA NA NA 21 22 NA NA NA 18 19 20 21 22 23 24 25 NA NA NA NA  8
[42697]  9 10 11 12 13 14 NA NA NA NA NA NA 17 18 19 20 21 NA NA NA NA NA NA 22
[42721] 23 24 25 26 NA NA NA NA 13 14 15 16 17 18 19 NA NA NA NA NA 23 24 25 26
[42745] 27 28 NA NA NA NA NA NA  7  8  9 10 11 NA NA NA NA NA NA NA NA NA NA 22
[42769] NA NA NA NA NA NA 19 20 21 22 23 NA NA NA NA NA NA 18 19 20 21 22 NA NA
[42793] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA  9 10 11 NA NA NA NA
[42817] NA NA NA NA NA NA NA NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA 22
[42841] 23 24 25 26 27 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[42865] 21 22 23 NA NA NA NA NA  8  9 10 11 12 13 NA NA NA NA NA NA NA 18 19 20
[42889] 21 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA NA NA NA NA 18 NA
[42913] NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA NA NA NA 18 19 20 NA NA NA
[42937] NA 13 14 15 16 17 18 19 NA NA NA NA NA NA 24 25 26 27 28 NA NA NA NA NA
[42961] NA NA NA NA NA NA NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA NA NA 14
[42985] 15 16 17 18 NA NA NA NA NA NA NA NA  9 10 11 NA NA NA NA NA NA 10 11 12
[43009] 13 14 NA NA NA NA NA NA 19 20 21 22 23 NA NA 18 19 20 21 22 23 24 25 26
[43033] NA NA NA NA NA NA 11 12 13 14 15 NA NA NA NA NA NA NA NA 14 15 16 NA NA
[43057] NA NA NA NA NA 21 22 23 24 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA
[43081] 14 15 16 17 18 19 20 NA NA NA NA  7  8  9 10 11 12 13 NA NA NA NA NA NA
[43105] NA 24 25 26 27 NA NA NA NA NA NA 21 22 23 24 25 NA NA NA NA NA NA 14 15
[43129] 16 17 18 NA NA NA 12 13 14 15 16 17 18 19 NA NA 13 14 15 16 17 18 19 20
[43153] 21 NA NA NA NA 11 12 13 14 15 16 17 NA NA NA NA 20 21 22 23 24 25 26 NA
[43177] NA NA NA NA 13 14 15 16 17 18 NA NA NA NA 11 12 13 14 15 16 17 NA NA NA
[43201] NA NA NA 21 22 23 24 25 NA NA NA NA NA NA NA NA 12 13 14 NA NA NA NA 13
[43225] 14 15 16 17 18 19 NA NA NA NA NA 22 23 24 25 26 27 NA NA NA NA NA NA 24
[43249] 25 26 27 28 NA NA NA NA NA NA 17 18 19 20 21 NA NA NA NA NA 18 19 20 21
[43273] 22 23 NA NA NA NA NA 12 13 14 15 16 17 NA NA NA NA NA NA NA NA NA NA  9
[43297] NA NA NA NA NA NA 11 12 13 14 15 NA NA NA NA NA NA NA NA  8  9 10 NA NA
[43321] NA 14 15 16 17 18 19 20 21 NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA
[43345] NA 14 15 16 17 18 19 NA NA NA NA NA NA 17 18 19 20 21 NA NA NA NA NA NA
[43369] NA 15 16 17 18 NA  9 10 11 12 13 14 15 16 17 18 NA NA NA NA NA NA 16 17
[43393] 18 19 20 NA NA NA NA NA 24 25 26 27 28 29 NA NA NA NA NA 13 14 15 16 17
[43417] 18 NA NA NA NA 10 11 12 13 14 15 16 NA NA NA NA NA 26 27 28 29 30 31 NA
[43441] NA NA NA NA 15 16 17 18 19 20 NA NA NA NA NA NA NA 20 21 22 23 NA NA NA
[43465] NA NA NA NA 18 19 20 21 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA 14
[43489] 15 16 17 18 19 20 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA 23
[43513] 24 25 26 27 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA NA 16 17 18
[43537] 19 20 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA NA  6  7  8  9 10
[43561] NA NA NA NA NA NA 17 18 19 20 21 NA 15 16 17 18 19 20 21 22 23 24 NA NA
[43585] NA NA NA NA 19 20 21 22 23 NA NA NA 33 34 35 36 37 38 39 40 NA NA NA 17
[43609] 18 19 20 21 22 23 24 NA NA NA NA 10 11 12 13 14 15 16 NA NA NA NA NA NA
[43633]  8  9 10 11 12 NA NA NA NA NA NA NA NA 18 19 20 NA NA NA NA NA NA 16 17
[43657] 18 19 20 NA NA NA NA NA NA 21 22 23 24 25 NA NA NA NA NA NA 20 21 22 23
[43681] 24 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA 15 16 17 18 19 NA
[43705] NA NA NA NA NA 18 19 20 21 22 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA
[43729] NA NA NA NA NA 25 26 27 NA NA NA NA NA NA NA NA NA 29 30 NA NA NA NA NA
[43753] 14 15 16 17 18 19 NA NA NA NA NA NA 13 14 15 16 17 NA NA NA NA 12 13 14
[43777] 15 16 17 18 NA  9 10 11 12 13 14 15 16 17 18 NA NA NA NA NA NA NA NA NA
[43801] 30 31 NA NA NA NA NA  8  9 10 11 12 13 NA NA NA NA NA  8  9 10 11 12 13
[43825] NA NA NA NA NA 18 19 20 21 22 23 NA NA NA NA NA NA NA NA NA 25 26 NA NA
[43849] NA NA NA 12 13 14 15 16 17 NA NA NA 16 17 18 19 20 21 22 23 NA NA NA NA
[43873] NA NA  7  8  9 10 11 NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA NA NA
[43897]  4  5  6  7  8 NA NA NA NA NA NA NA 20 21 22 23 NA NA NA NA NA NA 16 17
[43921] 18 19 20 NA NA NA NA NA NA NA NA 16 17 18 NA NA NA NA NA NA 14 15 16 17
[43945] 18 NA NA NA NA NA NA 11 12 13 14 15 NA NA NA NA NA NA 20 21 22 23 24 NA
[43969] NA NA NA NA NA 10 11 12 13 14 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA
[43993] NA NA NA 23 24 25 26 27 NA NA NA NA NA NA 13 14 15 16 17 NA NA NA NA 17
[44017] 18 19 20 21 22 23 NA NA NA NA NA NA NA NA NA NA 40 NA NA NA NA NA  8  9
[44041] 10 11 12 13 NA NA NA NA NA NA  6  7  8  9 10 NA NA NA NA NA NA  9 10 11
[44065] 12 13 NA NA NA 11 12 13 14 15 16 17 18 NA NA NA NA 21 22 23 24 25 26 27
[44089] NA NA NA NA 18 19 20 21 22 23 24 NA NA NA NA NA NA 12 13 14 15 16 NA NA
[44113] NA NA NA 21 22 23 24 25 26 NA NA NA NA NA NA NA NA NA NA 22 NA NA NA NA
[44137] NA NA NA NA 23 24 25 NA NA NA NA NA NA  9 10 11 12 13 NA NA NA NA NA NA
[44161] 17 18 19 20 21 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA 13 14
[44185] 15 16 17 NA NA NA NA NA NA NA NA 15 16 17 NA NA  9 10 11 12 13 14 15 16
[44209] 17 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA NA NA  8  9 10 11 12 NA
[44233] NA NA 21 22 23 24 25 26 27 28 NA NA NA NA NA 30 31 32 33 34 35 NA NA NA
[44257] NA NA NA NA NA NA 10 11 NA NA NA NA 13 14 15 16 17 18 19 NA NA NA NA 19
[44281] 20 21 22 23 24 25 NA NA NA NA 14 15 16 17 18 19 20 NA NA NA NA NA 13 14
[44305] 15 16 17 18 NA NA NA NA NA 12 13 14 15 16 17 NA NA NA NA NA NA 18 19 20
[44329] 21 22 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA NA 20 21 22 23 24
[44353] NA NA NA NA NA NA NA  8  9 10 11 NA NA NA NA NA 22 23 24 25 26 27 NA NA
[44377] NA NA NA NA 18 19 20 21 22 NA NA NA NA NA NA NA 16 17 18 19 NA NA NA 22
[44401] 23 24 25 26 27 28 29 NA NA NA NA 13 14 15 16 17 18 19 NA NA NA NA NA NA
[44425] NA 15 16 17 18 NA NA NA NA NA NA  9 10 11 12 13 NA NA NA NA NA NA NA 14
[44449] 15 16 17 NA NA NA NA 10 11 12 13 14 15 16 NA NA NA NA 15 16 17 18 19 20
[44473] 21 NA NA NA 13 14 15 16 17 18 19 20 NA NA NA  7  8  9 10 11 12 13 14 NA
[44497] NA NA NA NA NA 11 12 13 14 15 NA NA NA NA NA 21 22 23 24 25 26 NA NA NA
[44521] NA NA NA 11 12 13 14 15 NA NA NA 16 17 18 19 20 21 22 23 NA NA NA NA NA
[44545] NA 25 26 27 28 29 NA NA NA NA 11 12 13 14 15 16 17 NA NA NA 11 12 13 14
[44569] 15 16 17 18 NA NA NA NA NA NA NA NA 14 15 16 NA NA NA 14 15 16 17 18 19
[44593] 20 21 NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA NA NA NA NA 17 18 19
[44617] NA NA NA NA NA 19 20 21 22 23 24 NA NA NA NA 11 12 13 14 15 16 17 NA NA
[44641] NA NA NA NA 14 15 16 17 18 NA NA NA NA 13 14 15 16 17 18 19 NA NA NA NA
[44665] NA NA 14 15 16 17 18 NA NA NA NA NA  6  7  8  9 10 11 NA NA NA NA NA  9
[44689] 10 11 12 13 14 NA NA NA NA NA NA NA 38 39 40 41 NA NA NA NA NA NA 16 17
[44713] 18 19 20 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA 12 13 14 15 16 17 18
[44737] 19 NA NA NA NA NA 22 23 24 25 26 27 NA NA NA NA NA 13 14 15 16 17 18 NA
[44761] NA NA NA NA 17 18 19 20 21 22 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA
[44785] NA NA NA NA 10 11 12 13 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA
[44809] NA 13 14 15 16 17 NA NA NA NA NA NA NA 18 19 20 21 NA NA NA NA NA 17 18
[44833] 19 20 21 22 NA NA NA NA NA NA  5  6  7  8  9 NA NA NA NA NA 12 13 14 15
[44857] 16 17 NA NA NA NA NA NA 19 20 21 22 23 NA NA NA NA NA NA NA NA 17 18 19
[44881] NA NA NA NA NA 25 26 27 28 29 30 NA NA NA NA NA NA 11 12 13 14 15 NA NA
[44905] NA NA NA NA NA 19 20 21 22 NA NA NA NA NA 17 18 19 20 21 22 NA NA NA NA
[44929] NA 11 12 13 14 15 16 NA NA NA NA 12 13 14 15 16 17 18 NA NA NA NA NA NA
[44953] 11 12 13 14 15 NA NA NA NA NA 20 21 22 23 24 25 NA NA NA  7  8  9 10 11
[44977] 12 13 14 NA NA NA NA NA NA 13 14 15 16 17 NA NA NA NA NA NA 14 15 16 17
[45001] 18 NA NA NA NA NA NA NA NA 17 18 19 NA NA NA NA NA NA NA NA NA 13 14 NA
[45025] NA NA NA NA NA 11 12 13 14 15 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA
[45049]  7  8  9 10 11 12 13 14 NA NA NA NA NA NA NA NA 15 16 17 NA NA NA 15 16
[45073] 17 18 19 20 21 22 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 16 17
[45097] 18 19 20 21 NA NA NA NA NA NA NA 16 17 18 19 NA NA NA NA 20 21 22 23 24
[45121] 25 26 NA NA NA NA NA NA NA NA 12 13 14 NA NA NA  7  8  9 10 11 12 13 14
[45145] NA NA 11 12 13 14 15 16 17 18 19 NA NA NA NA NA NA NA 60 61 62 63 NA NA
[45169] NA NA NA 16 17 18 19 20 21 NA NA NA NA 12 13 14 15 16 17 18 NA NA NA NA
[45193] NA NA NA 17 18 19 20 NA NA NA NA NA NA 17 18 19 20 21 NA NA NA NA NA 35
[45217] 36 37 38 39 40 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA 12 13
[45241] 14 15 16 NA NA NA NA NA NA 13 14 15 16 17 NA NA NA NA NA NA NA 21 22 23
[45265] 24 NA NA NA NA NA 12 13 14 15 16 17 NA NA NA NA NA NA 17 18 19 20 21 NA
[45289] NA NA NA 34 35 36 37 38 39 40 NA NA NA NA NA NA NA NA  9 10 11 NA NA NA
[45313] NA NA NA 10 11 12 13 14 NA NA NA NA NA NA 18 19 20 21 22 NA NA NA NA NA
[45337] NA 17 18 19 20 21 NA NA NA NA NA 18 19 20 21 22 23 NA NA NA NA NA 20 21
[45361] 22 23 24 25 NA NA NA NA NA NA 19 20 21 22 23 NA NA NA NA 12 13 14 15 16
[45385] 17 18 NA NA NA NA NA NA 16 17 18 19 20 NA NA NA NA NA NA NA 17 18 19 20
[45409] NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 11 12 13 14 15 16 NA NA
[45433] NA NA NA NA NA 13 14 15 16 NA NA NA NA NA NA NA NA 18 19 20 NA NA NA NA
[45457] NA NA 19 20 21 22 23 NA NA NA NA NA NA 22 23 24 25 26 NA NA NA NA NA NA
[45481] NA NA NA 12 13 NA  5  6  7  8  9 10 11 12 13 14 NA NA NA NA NA NA 12 13
[45505] 14 15 16 NA NA NA NA NA NA 13 14 15 16 17 NA NA NA NA NA NA NA  9 10 11
[45529] 12 NA NA NA NA NA NA  7  8  9 10 11 NA NA NA NA NA 14 15 16 17 18 19 NA
[45553] NA NA NA 13 14 15 16 17 18 19 NA NA NA NA NA  9 10 11 12 13 14 NA NA NA
[45577] NA NA NA NA NA 16 17 18 NA NA NA NA NA NA 10 11 12 13 14 NA NA NA 15 16
[45601] 17 18 19 20 21 22 NA NA NA NA 11 12 13 14 15 16 17 NA NA NA NA NA NA NA
[45625] 19 20 21 22 NA NA NA NA NA NA 11 12 13 14 15 NA NA NA NA NA 13 14 15 16
[45649] 17 18 NA NA NA 12 13 14 15 16 17 18 19 NA NA NA NA 14 15 16 17 18 19 20
[45673] NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA 18 19 20 21 22 NA NA
[45697] NA 11 12 13 14 15 16 17 18 NA NA NA NA 16 17 18 19 20 21 22 NA NA NA NA
[45721] NA NA 11 12 13 14 15 NA NA NA NA NA NA NA 22 23 24 25 NA NA NA NA NA NA
[45745] 16 17 18 19 20 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA NA NA
[45769] 26 27 28 NA NA NA NA NA 10 11 12 13 14 15 NA NA NA NA NA 14 15 16 17 18
[45793] 19 NA NA NA NA 22 23 24 25 26 27 28 NA NA NA NA NA 18 19 20 21 22 23 NA
[45817] NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 21 22 23 24 25 26 NA NA NA
[45841] NA NA NA 23 24 25 26 27 NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA 15
[45865] 16 17 18 19 20 21 NA NA NA NA NA NA  7  8  9 10 11 NA NA NA NA NA NA 23
[45889] 24 25 26 27 NA NA NA NA NA NA NA NA NA NA 12 NA NA NA NA 18 19 20 21 22
[45913] 23 24 NA NA NA NA NA  8  9 10 11 12 13 NA NA NA NA NA 23 24 25 26 27 28
[45937] NA NA NA NA NA NA 12 13 14 15 16 NA NA NA NA NA NA  7  8  9 10 11 NA NA
[45961] NA NA NA NA  9 10 11 12 13 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA
[45985] NA 10 11 12 13 14 15 NA NA NA NA NA NA 10 11 12 13 14 NA NA NA NA NA 27
[46009] 28 29 30 31 32 NA NA NA NA NA NA NA NA 11 12 13 NA NA NA NA 18 19 20 21
[46033] 22 23 24 NA NA NA NA NA NA NA NA 22 23 24 NA NA NA NA NA 10 11 12 13 14
[46057] 15 NA NA NA NA 29 30 31 32 33 34 35 NA NA NA NA NA NA 12 13 14 15 16 NA
[46081] NA NA NA NA NA NA 15 16 17 18 NA NA NA NA NA 15 16 17 18 19 20 NA NA NA
[46105] NA NA 15 16 17 18 19 20 NA NA NA NA NA NA  6  7  8  9 10 NA NA NA NA NA
[46129] NA 27 28 29 30 31 NA NA NA NA NA  7  8  9 10 11 12 NA NA NA NA NA NA 11
[46153] 12 13 14 15 NA NA NA NA 10 11 12 13 14 15 16 NA NA NA NA  9 10 11 12 13
[46177] 14 15 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA 19 20 21 22 23 24
[46201] NA NA NA NA NA 16 17 18 19 20 21 NA NA NA 19 20 21 22 23 24 25 26 NA NA
[46225] NA NA NA NA  6  7  8  9 10 NA NA NA NA NA NA NA NA 18 19 20 NA NA NA NA
[46249] NA NA 21 22 23 24 25 NA NA NA NA NA NA 20 21 22 23 24 NA NA NA NA NA NA
[46273] NA 11 12 13 14 NA NA NA NA  8  9 10 11 12 13 14 NA NA NA  6  7  8  9 10
[46297] 11 12 13 NA NA NA NA NA 17 18 19 20 21 22 NA NA NA NA 13 14 15 16 17 18
[46321] 19 NA NA NA NA NA NA  8  9 10 11 12 NA NA NA NA NA NA 14 15 16 17 18 NA
[46345] NA NA NA NA NA 11 12 13 14 15 NA NA NA NA NA 21 22 23 24 25 26 NA NA NA
[46369] NA NA NA 14 15 16 17 18 NA NA NA NA NA NA NA NA  6  7  8 NA NA NA NA NA
[46393] NA 13 14 15 16 17 NA NA NA NA NA NA 17 18 19 20 21 NA NA NA NA 21 22 23
[46417] 24 25 26 27 NA NA NA NA NA  7  8  9 10 11 12 NA NA NA NA NA NA 12 13 14
[46441] 15 16 NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA NA NA  8  9 10 11 12
[46465] NA NA NA NA NA NA NA NA 23 24 25 NA NA NA NA NA 20 21 22 23 24 25 NA NA
[46489] NA NA NA 14 15 16 17 18 19 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA
[46513] NA NA 10 11 12 13 14 NA NA NA NA 10 11 12 13 14 15 16 NA NA NA NA NA NA
[46537] 13 14 15 16 17 NA NA NA NA NA NA 11 12 13 14 15 NA NA NA NA  7  8  9 10
[46561] 11 12 13 NA NA NA NA NA NA NA NA NA 21 22 NA NA NA NA NA NA NA NA NA 18
[46585] 19 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA NA NA 29 30 31 32 33 NA
[46609] NA NA NA NA NA NA  7  8  9 10 NA NA NA NA NA 10 11 12 13 14 15 NA NA NA
[46633] NA NA 24 25 26 27 28 29 NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA NA
[46657] NA 21 22 23 24 25 NA NA NA NA NA  9 10 11 12 13 14 NA NA NA NA NA NA NA
[46681] NA 13 14 15 NA NA NA NA 14 15 16 17 18 19 20 NA NA NA 16 17 18 19 20 21
[46705] 22 23 NA NA NA NA NA NA  8  9 10 11 12 NA NA NA NA NA NA 28 29 30 31 32
[46729] NA NA NA NA NA 10 11 12 13 14 15 NA NA NA NA NA NA 25 26 27 28 29 NA NA
[46753] NA NA  7  8  9 10 11 12 13 NA NA NA NA NA NA 14 15 16 17 18 NA NA NA NA
[46777] NA NA NA NA  7  8  9 NA NA NA NA NA NA NA NA 17 18 19 NA NA NA NA NA NA
[46801] 14 15 16 17 18 NA NA NA NA NA 11 12 13 14 15 16 NA NA NA NA NA 18 19 20
[46825] 21 22 23 NA NA NA 11 12 13 14 15 16 17 18 NA NA NA NA 22 23 24 25 26 27
[46849] 28 NA NA NA NA NA  7  8  9 10 11 12 NA NA NA NA NA NA 13 14 15 16 17 NA
[46873] NA NA NA NA NA NA NA 15 16 17 NA 13 14 15 16 17 18 19 20 21 22 NA NA NA
[46897] NA NA NA NA NA 16 17 18 NA NA NA 11 12 13 14 15 16 17 18 NA NA NA 14 15
[46921] 16 17 18 19 20 21 NA NA NA NA  6  7  8  9 10 11 12 NA NA NA NA NA NA NA
[46945] NA NA 13 14 NA NA NA NA NA NA NA NA NA 26 27 NA NA NA NA NA NA NA NA NA
[46969] 14 15 NA NA NA NA NA NA NA NA NA 12 13 NA NA NA NA NA NA NA NA NA 12 13
[46993] NA NA NA NA NA NA NA NA NA  8  9 NA NA NA NA NA NA NA NA NA 12 13 NA NA
[47017] NA NA NA NA NA NA NA NA 24 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA
[47041] NA NA NA NA NA 27 28 NA NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA NA
[47065] NA NA NA NA 18 NA NA NA NA NA NA NA NA NA NA 23 NA NA NA NA NA NA NA NA
[47089] NA 16 17 NA NA NA NA NA NA NA NA NA 12 13 NA NA NA NA NA NA NA NA NA NA
[47113] 13 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA 19 20 NA
[47137] NA NA NA NA NA NA NA NA 21 22 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA
[47161] NA NA NA NA NA NA NA 24 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA NA
[47185] NA NA NA NA NA 21 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA
[47209] NA NA NA 18 NA NA NA NA NA NA NA NA NA 21 22 NA NA NA NA NA NA NA NA NA
[47233] NA 13 NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA NA NA NA NA NA 15
[47257] NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA 16 17 NA NA
[47281] NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA  7  8 NA NA NA NA
[47305] NA NA NA NA NA 22 23 NA NA NA NA NA NA NA NA NA NA 21 NA NA NA NA NA NA
[47329] NA NA NA 18 19 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA
[47353] NA 14 15 NA NA NA NA NA NA NA NA NA 21 22 NA NA NA NA NA NA NA NA NA 20
[47377] 21 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA NA 14 NA
[47401] NA NA NA NA NA NA NA NA 18 19 NA NA NA NA NA NA NA NA NA 23 24 NA NA NA
[47425] NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA NA 24 NA NA NA NA NA
[47449] NA NA NA NA  7  8 NA NA NA NA NA NA NA NA NA NA 15 NA NA NA NA NA NA NA
[47473] NA NA 17 18 NA NA NA NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA NA NA
[47497] 22 23 NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA 15 16
[47521] NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA NA 17 18 NA NA
[47545] NA NA NA NA NA NA NA  6  7 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA
[47569] NA NA NA NA NA NA 19 NA NA NA NA NA NA NA NA NA NA 19 NA NA NA NA NA NA
[47593] NA NA NA 24 25 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA
[47617] NA 18 19 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA 26
[47641] 27 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA 11 12 NA
[47665] NA NA NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA NA NA NA 19 NA NA NA
[47689] NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA
[47713] NA NA NA NA 33 34 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA
[47737] NA NA NA 22 NA NA NA NA NA NA NA NA NA 28 29 NA NA NA NA NA NA NA NA NA
[47761] 21 22 NA NA NA NA NA NA NA NA NA NA 22 NA NA NA NA NA NA NA NA NA 18 19
[47785] NA NA NA NA NA NA NA NA NA 24 25 NA NA NA NA NA NA NA NA NA NA 20 NA NA
[47809] NA NA NA NA NA NA NA 28 29 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA
[47833] NA NA NA NA NA 13 14 NA NA NA NA NA NA NA NA NA NA 15 NA NA NA NA NA NA
[47857] NA NA NA 23 24 NA NA NA NA NA NA NA NA NA 22 23 NA NA NA NA NA NA NA NA
[47881] NA 26 27 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA NA
[47905] 11 NA NA NA NA NA NA NA NA NA 18 19 NA NA NA NA NA NA NA NA NA NA  6 NA
[47929] NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA 18 19 NA NA NA
[47953] NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA
[47977] NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA 24 25 NA NA NA NA NA NA NA
[48001] NA NA 22 23 NA NA NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA
[48025]  6  7 NA NA NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA NA 12
[48049] NA NA NA NA NA NA NA NA NA NA 27 NA NA NA NA NA NA NA NA NA 22 23 NA NA
[48073] NA NA NA NA NA NA NA NA 25 NA NA NA NA NA NA NA NA NA NA 19 NA NA NA NA
[48097] NA NA NA NA NA NA 23 NA NA NA NA NA NA NA NA NA NA  9 NA NA NA NA NA NA
[48121] NA NA NA NA  7 NA NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA NA NA NA
[48145] NA NA 19 NA NA NA NA NA NA NA NA NA NA 27 NA NA NA NA NA NA NA NA NA NA
[48169] 21 NA NA NA NA NA NA NA NA NA NA 27 NA NA NA NA NA NA NA NA 14 15 16 NA
[48193] NA NA NA NA NA NA NA 10 11 12 NA NA NA NA NA NA NA NA 10 11 12 NA NA NA
[48217] NA NA NA NA NA NA 27 28 NA NA NA NA NA NA NA NA  5  6  7 NA NA NA NA NA
[48241] NA NA NA  8  9 10 NA NA NA NA NA NA NA NA 13 14 15 NA NA NA NA NA NA NA
[48265] NA 15 16 17 NA NA NA NA NA NA NA NA  9 10 11 NA NA NA NA NA NA NA NA 15
[48289] 16 17 NA NA NA NA NA NA NA NA 12 13 14 NA NA NA NA NA NA NA NA 15 16 17
[48313] NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA 10 11 12 NA NA
[48337] NA NA NA NA NA NA  7  8  9 NA NA NA NA NA NA NA NA NA  9 10 NA NA NA NA
[48361] NA NA NA NA 11 12 13 NA NA NA NA NA NA NA NA 13 14 15 NA NA NA NA NA NA
[48385] NA NA  9 10 11 NA NA NA NA NA NA NA NA 10 11 12 NA NA NA NA NA NA NA NA
[48409]  7  8  9 NA NA NA NA NA NA NA NA 12 13 14 NA NA NA NA NA NA NA NA 13 14
[48433] 15 NA NA NA NA NA NA NA NA 18 19 20 NA NA NA NA NA NA NA NA NA 11 12 NA
[48457] NA NA NA NA NA NA NA 13 14 15 NA NA NA NA NA NA NA NA 19 20 21 NA NA NA
[48481] NA NA NA NA NA  8  9 10 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[48505] NA NA NA NA NA  9 NA NA NA NA NA NA NA NA 10 11 12 NA NA NA NA NA NA NA
[48529] NA NA NA NA NA NA NA NA NA NA NA NA  6  7  8 NA NA NA NA NA NA NA NA 17
[48553] 18 19 NA NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA NA NA NA  9 10 11
[48577] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[48601] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[48625] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA  7 NA NA NA NA NA NA
[48649] NA NA NA 10 11 NA NA NA NA NA NA NA NA NA 13 14 NA NA NA NA NA NA NA NA
[48673] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 15
[48697] 16 NA NA NA NA NA NA NA NA NA 18 19 NA NA NA NA NA NA NA NA NA 16 17 NA
[48721] NA NA NA NA NA NA NA 11 12 13 NA NA NA NA NA NA NA NA NA 13 14 NA NA NA
[48745] NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[48769] NA NA NA NA NA  4 NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA
[48793] NA NA 20 21 NA NA NA NA NA NA NA NA 11 12 13 NA NA NA NA NA NA NA NA NA
[48817] NA 21 NA NA NA NA NA NA NA NA 14 15 16 NA NA NA NA NA NA NA NA NA 11 12
[48841] NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA NA 23 NA NA
[48865] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[48889] NA NA NA NA NA NA 13 NA NA NA NA NA NA NA NA NA NA 12 NA NA NA NA NA NA
[48913] NA NA NA 17 18 NA NA NA NA NA NA NA NA 17 18 19 NA NA NA NA NA NA NA NA
[48937] NA 10 11 NA NA NA NA NA NA NA NA 18 19 20 NA NA NA NA NA NA NA NA 15 16
[48961] 17 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA NA 22 23 NA
[48985] NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA  9 10 11 NA NA NA
[49009] NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA 16 17 18 NA NA NA NA NA
[49033] NA NA NA 15 16 17 NA NA NA NA NA NA NA NA  7  8  9 NA NA NA NA NA NA NA
[49057] NA NA NA 12 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA
[49081] 10 11 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA NA NA NA NA NA 21 22
[49105] NA NA NA NA NA NA NA NA NA NA 15 NA NA NA NA NA NA NA NA 18 19 20 NA NA
[49129] NA NA NA NA NA NA 16 17 18 NA NA NA NA NA NA NA NA 15 16 17 NA NA NA NA
[49153] NA NA NA NA 14 15 16 NA NA NA NA NA NA NA NA NA NA 26 NA NA NA NA NA NA
[49177] NA NA NA NA 21 NA NA NA NA NA NA NA NA NA NA  9 NA NA NA NA NA NA NA NA
[49201] NA 21 22 NA NA NA NA NA NA NA NA NA NA 10 NA NA NA NA NA NA NA NA 12 13
[49225] 14 NA NA NA NA NA NA NA NA NA  9 10 NA NA NA NA NA NA NA NA NA NA  7 NA
[49249] NA NA NA NA NA NA NA  8  9 10 NA NA NA NA NA NA NA NA NA NA 14 NA NA NA
[49273] NA NA NA NA NA NA NA 19 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[49297] NA NA NA NA NA NA NA NA NA NA NA NA NA NA  8  9 10 NA NA NA NA NA NA NA
[49321] NA NA NA 20 NA NA NA NA NA NA NA NA 16 17 18 NA NA NA NA NA NA NA NA NA
[49345] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 19
[49369] NA NA NA NA NA NA NA NA 16 17 18 NA NA NA NA NA NA NA NA 10 11 12 NA NA
[49393] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA  4  5  6 NA NA NA NA
[49417] NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA 32 33 34 NA NA NA NA NA NA
[49441] NA NA NA 15 16 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[49465] NA NA 15 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 13
[49489] 14 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA 23 24 NA
[49513] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 22 23 24 NA NA NA
[49537] NA NA NA NA NA 14 15 16 NA NA NA NA NA NA NA NA NA NA 19 NA NA NA NA NA
[49561] NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA NA 20 NA NA NA NA NA NA NA
[49585] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[49609] 13 14 NA NA NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA 11 12
[49633] NA NA NA NA NA NA NA NA  7  8  9 NA NA NA NA NA NA NA NA 13 14 15 NA NA
[49657] NA NA NA NA NA NA NA 12 13 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA
[49681] NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA NA NA 22 NA NA NA NA NA NA
[49705] NA NA NA 13 14 NA NA NA NA NA NA NA NA 18 19 20 NA NA NA NA NA NA NA NA
[49729] NA NA 14 NA NA NA NA NA NA NA NA 12 13 14 NA NA NA NA NA NA NA NA  5  6
[49753]  7 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA  9 NA
[49777] NA NA NA NA NA NA NA NA NA  9 NA NA NA NA NA NA NA NA NA NA 22 NA NA NA
[49801] NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA 16 17 18 NA NA NA NA NA
[49825] NA NA NA 12 13 14 NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA
[49849] NA NA  8  9 NA NA NA NA NA NA NA NA 15 16 17 NA NA NA NA NA NA NA NA NA
[49873] NA 17 NA NA NA NA NA NA NA NA NA 35 36 NA NA NA NA NA NA NA NA NA NA NA
[49897] NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA 13 14 15 NA NA
[49921] NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA 12 13 14 NA NA NA NA
[49945] NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA NA  6 NA NA NA NA NA NA
[49969] NA NA NA NA 15 NA NA NA NA NA NA NA NA NA NA  4 NA NA NA NA NA NA NA NA
[49993] NA NA NA NA NA NA NA NA NA NA NA NA NA  8 NA NA NA NA NA NA NA NA 18 19
[50017] 20 NA NA NA NA NA NA NA NA NA NA  5 NA NA NA NA NA NA NA NA 14 15 16 NA
[50041] NA NA NA NA NA NA NA 21 22 23 NA NA NA NA NA NA NA NA 20 21 22 NA NA NA
[50065] NA NA NA NA NA NA NA 20 NA NA NA NA NA NA NA NA 15 16 17 NA NA NA NA NA
[50089] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50113] NA NA NA 11 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 15
[50137] 16 17 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA  5
[50161] NA NA NA NA NA NA NA NA NA NA  5 NA NA NA NA NA NA NA NA NA NA 27 NA NA
[50185] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA
[50209] NA NA NA NA NA NA 15 NA NA NA NA NA NA NA NA NA NA  9 NA NA NA NA NA NA
[50233] NA NA  8  9 10 NA NA NA NA NA NA NA NA NA 12 13 NA NA NA NA NA NA NA NA
[50257] NA NA  6 NA NA NA NA NA NA NA NA NA  7  8 NA NA NA NA NA NA NA NA NA  8
[50281]  9 NA NA NA NA NA NA NA NA NA NA 15 NA NA NA NA NA NA NA NA NA NA  7 NA
[50305] NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA NA NA NA NA NA 18 NA NA NA
[50329] NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA NA NA 12 13 NA NA NA NA NA
[50353] NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50377] NA NA NA 23 NA NA NA NA NA NA NA NA  6  7  8 NA NA NA NA NA NA NA NA NA
[50401] NA 11 NA NA NA NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA NA 23
[50425] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50449] NA NA NA NA NA NA NA NA 15 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50473] NA NA NA NA NA NA 15 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50497] NA NA NA NA 33 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50521] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50545] 12 NA NA NA NA NA NA NA NA NA  4  5 NA NA NA NA NA NA NA NA NA NA 16 NA
[50569] NA NA NA NA NA NA NA 11 12 13 NA NA NA NA NA NA NA NA NA NA 15 NA NA NA
[50593] NA NA NA NA NA NA NA  8 NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA
[50617] NA NA NA NA NA 20 NA NA NA NA NA NA NA NA NA NA 21 NA NA NA NA NA NA NA
[50641] NA 10 11 12 NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA NA NA NA 20
[50665] 21 22 NA NA NA NA NA NA NA NA NA NA 19 NA NA NA NA NA NA NA NA NA NA  6
[50689] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 10 NA NA
[50713] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50737] NA NA NA NA NA NA 17 NA NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA NA
[50761] NA NA 23 24 25 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50785] NA NA 13 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50809] NA NA NA NA NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA NA 14 NA
[50833] NA NA NA NA NA NA NA NA 13 14 NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50857] NA NA NA NA NA 10 11 12 NA NA NA NA NA NA NA NA NA 12 13 NA NA NA NA NA
[50881] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50905] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[50929] NA NA NA NA NA NA NA NA NA NA NA NA 21 NA NA NA NA NA NA NA NA NA NA NA
[50953] NA NA NA NA NA NA NA NA 14 15 16 NA NA NA NA NA NA NA NA  9 10 11 NA NA
[50977] NA NA NA NA NA NA 17 18 19 NA NA NA NA NA NA NA NA NA  9 10 NA NA NA NA
[51001] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 10 NA NA NA NA NA NA
[51025] NA NA NA NA 13 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51049] NA 24 25 NA NA NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA 16 17
[51073] 18 NA NA NA NA NA NA NA NA NA  7  8 NA NA NA NA NA NA NA NA NA  8  9 NA
[51097] NA NA NA NA NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA
[51121] NA NA NA NA NA NA NA 10 NA NA NA NA NA NA NA NA NA  8  9 NA NA NA NA NA
[51145] NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA 13 14 NA NA NA NA NA NA NA
[51169] NA NA NA 12 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 18
[51193] 19 20 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 17
[51217] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51241] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA
[51265] NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA  9 10 NA NA NA NA NA NA
[51289] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51313]  8  9 10 NA NA NA NA NA NA NA NA NA  9 10 NA NA NA NA NA NA NA NA NA 18
[51337] 19 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 17 18 NA
[51361] NA NA NA NA NA NA NA NA  8  9 NA NA NA NA NA NA NA NA NA NA 11 NA NA NA
[51385] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51409] NA NA NA NA 10 11 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51433] NA NA NA NA NA NA NA NA NA NA NA NA NA NA 18 NA NA NA NA NA NA NA NA NA
[51457] 19 20 NA NA NA NA NA NA NA NA 21 22 23 NA NA NA NA NA NA NA NA 23 24 25
[51481] NA NA NA NA NA NA NA NA NA NA 12 NA NA NA NA NA NA NA NA NA 19 20 NA NA
[51505] NA NA NA NA NA NA 19 20 21 NA NA NA NA NA NA NA NA NA  7  8 NA NA NA NA
[51529] NA NA NA NA 12 13 14 NA NA NA NA NA NA NA NA NA NA 21 NA NA NA NA NA NA
[51553] NA NA 11 12 13 NA NA NA NA NA NA NA NA 17 18 19 NA NA NA NA NA NA NA NA
[51577] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51601] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51625] NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA
[51649] NA NA NA NA NA NA 23 24 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51673] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51697] NA NA NA 15 NA NA NA NA NA NA NA NA NA NA 23 NA NA NA NA NA NA NA NA NA
[51721] 25 26 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 18 19 20
[51745] NA NA NA NA NA NA NA NA 11 12 13 NA NA NA NA NA NA NA NA NA 10 11 NA NA
[51769] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51793] NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA NA 18 NA NA NA NA NA NA
[51817] NA NA 16 17 18 NA NA NA NA NA NA NA NA 12 13 14 NA NA NA NA NA NA NA NA
[51841]  9 10 11 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51865] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 11 12 NA
[51889] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51913] NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA NA NA 15 NA NA NA NA NA
[51937] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51961] NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA
[51985] NA NA NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA NA NA
[52009] NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA NA 14 NA NA
[52033] NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA NA  7 NA NA NA NA
[52057] NA NA NA NA NA 12 13 NA NA NA NA NA NA NA NA NA NA 15 NA NA NA NA NA NA
[52081] NA NA NA 24 25 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA NA NA
[52105] NA NA 22 NA NA NA NA NA NA NA NA NA  7  8 NA NA NA NA NA NA NA NA NA NA
[52129] 20 NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA 10 11 NA
[52153] NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA
[52177] NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA NA NA 13 14 NA NA NA NA NA
[52201] NA NA NA NA 19 20 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA NA NA NA
[52225] NA NA 19 20 NA NA NA NA NA NA NA NA NA 18 19 NA NA NA NA NA NA NA NA NA
[52249] NA 29 NA NA NA NA NA NA NA NA NA  8  9 NA NA NA NA NA NA NA NA NA NA  9
[52273] NA NA NA NA NA NA NA NA NA 21 22 NA NA NA NA NA NA NA NA NA  9 10 NA NA
[52297] NA NA NA NA NA NA NA NA 11 NA NA NA NA NA NA NA NA NA  7  8 NA NA NA NA
[52321] NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA NA NA
[52345] NA NA NA 21 22 NA NA NA NA NA NA NA NA NA 12 13 NA NA NA NA NA NA NA NA
[52369] NA 13 14 NA NA NA NA NA NA NA NA NA  6  7 NA NA NA NA NA NA NA NA NA NA
[52393] 22 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA NA NA NA NA NA 11 12 NA
[52417] NA NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA 14 15 NA NA NA
[52441] NA NA NA NA NA NA 32 33 NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA
[52465] NA NA NA NA 27 28 NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA
[52489] NA NA 28 29 NA NA NA NA NA NA NA NA NA 19 20 NA NA NA NA NA NA NA NA NA
[52513] 20 21 NA NA NA NA NA NA NA NA NA 13 14 NA NA NA NA NA NA NA NA NA 17 18
[52537] NA NA NA NA NA NA NA NA NA 18 19 NA NA NA NA NA NA NA NA NA 26 27 NA NA
[52561] NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA
[52585] NA NA NA NA NA NA 19 NA NA NA NA NA NA NA NA NA 15 16 NA NA NA NA NA NA
[52609] NA NA NA 23 24 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA
[52633] NA NA 19 NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA NA NA NA 13
[52657] 14 NA NA NA NA NA NA NA NA NA  5  6 NA NA NA NA NA NA NA NA NA 19 20 NA
[52681] NA NA NA NA NA NA NA NA NA 19 NA NA NA NA NA NA NA NA NA NA 17 NA NA NA
[52705] NA NA NA NA NA NA 21 22 NA NA NA NA NA NA NA NA NA 18 19 NA NA NA NA NA
[52729] NA NA NA NA 19 20 NA NA NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA
[52753] NA NA NA 12 NA NA NA NA NA NA NA NA NA NA 17 NA NA NA NA NA NA NA NA NA
[52777] NA 19 NA NA NA NA NA NA NA NA NA 18 19 NA NA NA NA NA NA NA NA NA 17 18
[52801] NA NA NA NA NA NA NA NA NA  5  6 NA NA NA NA NA NA NA NA NA 19 20 NA NA
[52825] NA NA NA NA NA NA NA NA 19 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA
[52849] NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA NA 11 12 NA NA NA NA NA NA
[52873] NA NA NA 19 20 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA
[52897] NA NA 15 NA NA NA NA NA NA NA NA NA NA  9 NA NA NA NA NA NA NA NA NA NA
[52921] 18 NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA 17 18 NA
[52945] NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA
[52969] NA NA NA NA NA NA 13 14 NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA
[52993] NA NA NA NA NA 24 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA
[53017] NA NA 14 15 NA NA NA NA NA NA NA NA NA 21 22 NA NA NA NA NA NA NA NA NA
[53041] 15 16 NA NA NA NA NA NA NA NA NA NA 28 NA NA NA NA NA NA NA NA NA NA 14
[53065] NA NA NA NA NA NA NA NA NA 16 17 NA NA NA NA NA NA NA NA NA NA 11 NA NA
[53089] NA NA NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA 12 13 NA NA NA NA
[53113] NA NA NA NA NA 14 15 NA NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA NA
[53137] NA NA NA  7  8 NA NA NA NA NA NA NA NA NA NA 24 NA NA NA NA NA NA NA NA
[53161] NA  7  8 NA NA NA NA NA NA NA NA NA 17 18 NA NA NA NA NA NA NA NA NA NA
[53185] 16 NA NA NA NA NA NA NA NA NA NA 18 NA NA NA NA NA NA NA NA NA NA 17 NA
[53209] NA NA NA NA NA NA NA NA NA 15 NA NA NA NA NA NA NA NA NA NA 20 NA NA NA
[53233] NA NA NA NA NA NA NA 22 NA NA NA NA NA NA NA NA NA NA 18 NA NA NA NA NA
[53257] NA NA NA NA NA 15 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA NA
[53281] NA NA NA 19 NA NA NA NA NA NA NA NA NA NA  5 NA NA NA NA NA NA NA NA NA
[53305] NA 13 NA NA NA NA NA NA NA NA NA NA 14 NA NA NA NA NA NA NA NA NA NA 14
[53329] NA NA NA NA NA NA NA NA NA NA 40 NA NA NA NA NA NA NA NA NA 21 22 NA NA
[53353] NA NA NA NA NA NA NA NA  5 NA NA NA NA NA NA NA NA NA NA 19 NA NA NA NA
[53377] NA NA NA NA NA NA 17 NA NA NA NA NA NA NA NA NA NA 16 NA NA NA NA NA NA
[53401] NA NA NA 16 17 NA NA NA NA NA NA NA NA NA NA 29 NA NA NA NA NA NA NA NA
[53425] NA NA 21 NA NA NA NA NA NA NA NA NA NA 13 NA NA NA NA NA NA NA NA NA NA
[53449] 20 NA NA NA NA NA NA NA NA NA NA 15 NA NA NA NA NA NA NA NA NA 17 18 NA
[53473] NA NA NA NA NA NA NA NA 20 21 NA NA NA NA NA NA NA NA NA  6  7 NA NA NA
[53497] NA NA  7  8  9 10 11 12 NA NA NA NA NA 12 13 14 15 16 17 NA NA NA NA NA
[53521] 12 13 14 15 16 17 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA  5  6
[53545]  7  8  9 10 NA NA NA NA NA  8  9 10 11 12 13 NA NA NA NA NA 21 22 23 24
[53569] 25 26 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 13 14 15 16 17 18
[53593] NA NA NA NA NA 15 16 17 18 19 20 NA NA NA NA NA 15 16 17 18 19 20 NA NA
[53617] NA NA NA  5  6  7  8  9 10 NA NA NA NA NA  6  7  8  9 10 11 NA NA NA NA
[53641] NA 15 16 17 18 19 20 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA 21
[53665] 22 23 24 25 26 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 12 13 14
[53689] 15 16 17 NA NA NA NA NA 10 11 12 13 14 15 NA NA NA NA NA 11 12 13 14 15
[53713] 16 NA NA NA NA NA 10 11 12 13 14 15 NA NA NA NA NA 17 18 19 20 21 22 NA
[53737] NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 12 13 14 15 16 17 NA NA NA
[53761] NA NA 13 14 15 16 17 18 NA NA NA NA NA 11 12 13 14 15 16 NA NA NA NA NA
[53785]  5  6  7  8  9 10 NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA NA 11 12
[53809] 13 14 15 16 NA NA NA NA NA 12 13 14 15 16 17 NA NA NA NA NA  8  9 10 11
[53833] 12 13 NA NA NA NA NA 17 18 19 20 21 22 NA NA NA NA NA 18 19 20 21 22 23
[53857] NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 15 16 17 18 19 20 NA NA
[53881] NA NA NA 17 18 19 20 21 22 NA NA NA NA NA  4  5  6  7  8  9 NA NA NA NA
[53905] NA 11 12 13 14 15 16 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA 11
[53929] 12 13 14 15 16 NA NA NA NA NA 12 13 14 15 16 17 NA NA NA NA NA 15 16 17
[53953] 18 19 20 NA NA NA NA NA  5  6  7  8  9 10 NA NA NA NA NA 12 13 14 15 16
[53977] 17 NA NA NA NA NA  9 10 11 12 13 14 NA NA NA NA NA 14 15 16 17 18 19 NA
[54001] NA NA NA NA 11 12 13 14 15 16 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA
[54025] NA NA 10 11 12 13 14 15 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA
[54049]  6  7  8  9 10 11 NA NA NA NA NA 19 20 21 22 23 24 NA NA NA NA NA 11 12
[54073] 13 14 15 16 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA 11 12 13 14
[54097] 15 16 NA NA NA NA NA  5  6  7  8  9 10 NA NA NA NA NA  9 10 11 12 13 14
[54121] NA NA NA NA NA 12 13 14 15 16 17 NA NA NA NA NA 18 19 20 21 22 23 NA NA
[54145] NA NA NA 14 15 16 17 18 19 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA
[54169] NA 10 11 12 13 14 15 NA NA NA NA NA  8  9 10 11 12 13 NA NA NA NA NA 10
[54193] 11 12 13 14 15 NA NA NA NA NA 10 11 12 13 14 15 NA NA NA NA NA  7  8  9
[54217] 10 11 12 NA NA NA NA NA  5  6  7  8  9 10 NA NA NA NA NA 16 17 18 19 20
[54241] 21 NA NA NA NA NA  8  9 10 11 12 13 NA NA NA NA NA 24 25 26 27 28 29 NA
[54265] NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA
[54289] NA NA 14 15 16 17 18 19 NA NA NA NA NA  8  9 10 11 12 13 NA NA NA NA NA
[54313] 13 14 15 16 17 18 NA NA NA NA NA  8  9 10 11 12 13 NA NA NA NA NA 15 16
[54337] 17 18 19 20 NA NA NA NA NA 10 11 12 13 14 15 NA NA NA NA NA 12 13 14 15
[54361] 16 17 NA NA NA NA NA 24 25 26 27 28 29 NA NA NA NA NA 13 14 15 16 17 18
[54385] NA NA NA NA NA  6  7  8  9 10 11 NA NA NA NA NA 13 14 15 16 17 18 NA NA
[54409] NA NA NA  8  9 10 11 12 13 NA NA NA NA NA  4  5  6  7  8  9 NA NA NA NA
[54433] NA  4  5  6  7  8  9 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA  4
[54457]  5  6  7  8  9 NA NA NA NA NA 16 17 18 19 20 21 NA NA NA NA NA  6  7  8
[54481]  9 10 11 NA NA NA NA NA 13 14 15 16 17 18 NA NA NA NA NA  5  6  7  8  9
[54505] 10 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA NA 18 19 20 21 22 NA
[54529] NA NA NA NA 22 23 24 25 26 27 NA NA NA NA NA 21 22 23 24 25 26 NA NA NA
[54553] NA NA  6  7  8  9 10 11 NA NA NA NA NA 14 15 16 17 18 19 NA NA NA NA NA
[54577] 13 14 15 16 17 18 NA NA NA NA NA 37 38 39 40 41 42 NA NA NA NA NA NA 14
[54601] 15 16 17 18 NA NA NA NA NA NA  9 10 11 12 13 NA NA NA NA NA NA  7  8  9
[54625] 10 11 NA NA NA NA NA NA NA  4  5  6  7 NA NA NA NA NA 11 12 13 14 15 16
[54649] NA NA NA NA NA NA NA  7  8  9 10 NA NA NA NA NA NA NA 14 15 16 17 NA NA
[54673] NA NA NA NA NA 17 18 19 20 NA NA NA NA NA NA NA 16 17 18 19 NA NA NA NA
[54697] NA NA NA NA NA NA NA</code></pre>
</div>
</div>
</section>
<section id="robustness-test" class="level1">
<h1>Robustness test</h1>
<section id="short-term-impact-of-inspection" class="level2">
<h2 class="anchored" data-anchor-id="short-term-impact-of-inspection">Short-term Impact of Inspection</h2>
<p>Inspected 后三年标记为1, inspection当年标记为0 We assign a code of ‘1’ to firms within the three-year period following their initial inspection to assess the short-term effects of regulatory pressure. Table B2. Short-term Impact of Inspection</p>
<div class="cell">
<div class="sourceCode cell-code" id="cb15"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb15-1"><a href="#cb15-1" aria-hidden="true" tabindex="-1"></a><span class="co"># # 创建一个新的数据框，只包含所需的列</span></span>
<span id="cb15-2"><a href="#cb15-2" aria-hidden="true" tabindex="-1"></a><span class="co"># new_df &lt;- select(dta1, Symbol, EndYear, inspection_year, first_inspection, after_first_inspection, extended_inspection)</span></span>
<span id="cb15-3"><a href="#cb15-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-4"><a href="#cb15-4" aria-hidden="true" tabindex="-1"></a><span class="co"># na_rows &lt;- dta1 %&gt;% filter(is.na(first_inspection))</span></span>
<span id="cb15-5"><a href="#cb15-5" aria-hidden="true" tabindex="-1"></a><span class="co"># new_df &lt;- select(na_rows, Symbol, EndYear, inspection_year, first_inspection, after_first_inspection, extended_inspection)</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</div>
<section id="p3-connection-continuous" class="level3">
<h3 class="anchored" data-anchor-id="p3-connection-continuous">P3 connection (continuous)</h3>
<div class="cell">
<div class="sourceCode cell-code" id="cb16"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb16-1"><a href="#cb16-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 加载必要的库</span></span>
<span id="cb16-2"><a href="#cb16-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lubridate)</span>
<span id="cb16-3"><a href="#cb16-3" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(dplyr)</span>
<span id="cb16-4"><a href="#cb16-4" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(tidyr)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'tidyr'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:Matrix':

    expand, pack, unpack</code></pre>
</div>
<div class="sourceCode cell-code" id="cb19"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb19-1"><a href="#cb19-1" aria-hidden="true" tabindex="-1"></a>dta3 <span class="ot">=</span> dta1</span>
<span id="cb19-2"><a href="#cb19-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-3"><a href="#cb19-3" aria-hidden="true" tabindex="-1"></a><span class="co"># 创建新变量 extended_inspection</span></span>
<span id="cb19-4"><a href="#cb19-4" aria-hidden="true" tabindex="-1"></a>dta3 <span class="ot">&lt;-</span> dta3 <span class="sc">%&gt;%</span></span>
<span id="cb19-5"><a href="#cb19-5" aria-hidden="true" tabindex="-1"></a>  <span class="fu">group_by</span>(Symbol) <span class="sc">%&gt;%</span>  <span class="co"># 按照单位分组</span></span>
<span id="cb19-6"><a href="#cb19-6" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(</span>
<span id="cb19-7"><a href="#cb19-7" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 找到每个单位 first_inspection 为1的年份</span></span>
<span id="cb19-8"><a href="#cb19-8" aria-hidden="true" tabindex="-1"></a>    <span class="at">inspection_year =</span> <span class="fu">ifelse</span>(first_inspection <span class="sc">==</span> <span class="dv">1</span>, Year, <span class="cn">NA</span>),</span>
<span id="cb19-9"><a href="#cb19-9" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 使用 coalesce 处理 NA 值，确保每个单位都有一个 inspection_year</span></span>
<span id="cb19-10"><a href="#cb19-10" aria-hidden="true" tabindex="-1"></a>    <span class="at">inspection_year =</span> <span class="fu">coalesce</span>(inspection_year, <span class="fu">lag</span>(inspection_year, <span class="at">default =</span> <span class="cn">NA</span>))</span>
<span id="cb19-11"><a href="#cb19-11" aria-hidden="true" tabindex="-1"></a>  ) <span class="sc">%&gt;%</span></span>
<span id="cb19-12"><a href="#cb19-12" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 使用 fill 函数确保 inspection_year 被赋值到所有行</span></span>
<span id="cb19-13"><a href="#cb19-13" aria-hidden="true" tabindex="-1"></a>  <span class="fu">fill</span>(inspection_year, <span class="at">.direction =</span> <span class="st">"downup"</span>) <span class="sc">%&gt;%</span></span>
<span id="cb19-14"><a href="#cb19-14" aria-hidden="true" tabindex="-1"></a>  <span class="fu">ungroup</span>()  <span class="co"># 解除分组</span></span>
<span id="cb19-15"><a href="#cb19-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-16"><a href="#cb19-16" aria-hidden="true" tabindex="-1"></a><span class="co"># 标记 inspection_year 及之后的两年</span></span>
<span id="cb19-17"><a href="#cb19-17" aria-hidden="true" tabindex="-1"></a>dta3<span class="sc">$</span>extended_inspection <span class="ot">&lt;-</span> <span class="fu">as.numeric</span>(dta3<span class="sc">$</span>Year <span class="sc">&gt;</span> dta3<span class="sc">$</span>inspection_year <span class="sc">&amp;</span> dta3<span class="sc">$</span>Year <span class="sc">&lt;=</span> (dta3<span class="sc">$</span>inspection_year <span class="sc">+</span> <span class="dv">3</span>))</span>
<span id="cb19-18"><a href="#cb19-18" aria-hidden="true" tabindex="-1"></a>dta3 <span class="ot">&lt;-</span> dta3 <span class="sc">%&gt;%</span> <span class="fu">mutate</span>(<span class="at">extended_inspection =</span> <span class="fu">ifelse</span>(<span class="fu">is.na</span>(extended_inspection), <span class="dv">0</span>, extended_inspection))</span>
<span id="cb19-19"><a href="#cb19-19" aria-hidden="true" tabindex="-1"></a><span class="co">#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure</span></span>
<span id="cb19-20"><a href="#cb19-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-21"><a href="#cb19-21" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span>
<span id="cb19-22"><a href="#cb19-22" aria-hidden="true" tabindex="-1"></a>p3ext1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> connection_num <span class="sc">+</span> ROA <span class="sc">+</span> ESG_Rate <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span>Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb19-23"><a href="#cb19-23" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-24"><a href="#cb19-24" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-25"><a href="#cb19-25" aria-hidden="true" tabindex="-1"></a>p3ext2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> extended_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span>RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb19-26"><a href="#cb19-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-27"><a href="#cb19-27" aria-hidden="true" tabindex="-1"></a>p3ext3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> extended_inspection <span class="sc">*</span> connection_num <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb19-28"><a href="#cb19-28" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-29"><a href="#cb19-29" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table with detailed mixed model statistics</span></span>
<span id="cb19-30"><a href="#cb19-30" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(sjPlot)</span>
<span id="cb19-31"><a href="#cb19-31" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-32"><a href="#cb19-32" aria-hidden="true" tabindex="-1"></a><span class="co"># Define custom CSS for Organization &amp; Environment journal style</span></span>
<span id="cb19-33"><a href="#cb19-33" aria-hidden="true" tabindex="-1"></a>oe_css <span class="ot">&lt;-</span> <span class="fu">list</span>(</span>
<span id="cb19-34"><a href="#cb19-34" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.table =</span> <span class="st">"border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px; margin-top: 10px;"</span>,</span>
<span id="cb19-35"><a href="#cb19-35" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.thead =</span> <span class="st">"border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; padding: 4px 8px;"</span>,</span>
<span id="cb19-36"><a href="#cb19-36" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.tdata =</span> <span class="st">"border: none; padding: 3px 8px; text-align: center;"</span>,</span>
<span id="cb19-37"><a href="#cb19-37" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.arc =</span> <span class="st">"border-top: 1px solid black;"</span>,</span>
<span id="cb19-38"><a href="#cb19-38" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.caption =</span> <span class="st">"font-weight: bold; text-align: left; padding-bottom: 15px; margin-bottom: 5px;"</span>,</span>
<span id="cb19-39"><a href="#cb19-39" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.subtitle =</span> <span class="st">"font-style: italic; text-align: center; padding-bottom: 8px; margin-bottom: 8px;"</span>,</span>
<span id="cb19-40"><a href="#cb19-40" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.firsttablecol =</span> <span class="st">"text-align: left; padding-left: 0px; border: none;"</span>,</span>
<span id="cb19-41"><a href="#cb19-41" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.leftalign =</span> <span class="st">"text-align: left;"</span>,</span>
<span id="cb19-42"><a href="#cb19-42" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.centeralign =</span> <span class="st">"text-align: center;"</span>,</span>
<span id="cb19-43"><a href="#cb19-43" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.footnote =</span> <span class="st">"font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;"</span>,</span>
<span id="cb19-44"><a href="#cb19-44" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.depvarheader =</span> <span class="st">"text-align: center; font-weight: normal; padding: 8px; border-bottom: 1px solid #ccc; background-color: #f9f9f9;"</span></span>
<span id="cb19-45"><a href="#cb19-45" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb19-46"><a href="#cb19-46" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-47"><a href="#cb19-47" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p3ext1, p3ext2, p3ext3,</span>
<span id="cb19-48"><a href="#cb19-48" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Table B2. Short-term Impact of Inspection"</span>,</span>
<span id="cb19-49"><a href="#cb19-49" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Dependent variable:&lt;br&gt;Environmental Information Disclosure"</span>,</span>
<span id="cb19-50"><a href="#cb19-50" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.aic =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-51"><a href="#cb19-51" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.loglik =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-52"><a href="#cb19-52" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.obs =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-53"><a href="#cb19-53" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ngroups =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-54"><a href="#cb19-54" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.re.var =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-55"><a href="#cb19-55" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-56"><a href="#cb19-56" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.intercept =</span> <span class="cn">FALSE</span>, <span class="co"># Hide intercept</span></span>
<span id="cb19-57"><a href="#cb19-57" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.p =</span> <span class="cn">FALSE</span>,        <span class="co"># Hide p-values column</span></span>
<span id="cb19-58"><a href="#cb19-58" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.se =</span> <span class="cn">TRUE</span>,        <span class="co"># Show standard errors in parentheses</span></span>
<span id="cb19-59"><a href="#cb19-59" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ci =</span> <span class="cn">FALSE</span>,       <span class="co"># Hide confidence intervals</span></span>
<span id="cb19-60"><a href="#cb19-60" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.style =</span> <span class="st">"stars"</span>,     <span class="co"># Display significance as stars</span></span>
<span id="cb19-61"><a href="#cb19-61" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.threshold =</span> <span class="fu">c</span>(<span class="fl">0.1</span>, <span class="fl">0.05</span>, <span class="fl">0.01</span>),  <span class="co"># Set significance thresholds</span></span>
<span id="cb19-62"><a href="#cb19-62" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits =</span> <span class="dv">3</span>,            <span class="co"># 3 decimal places</span></span>
<span id="cb19-63"><a href="#cb19-63" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits.re =</span> <span class="dv">2</span>,         <span class="co"># 2 decimal places for random effects</span></span>
<span id="cb19-64"><a href="#cb19-64" aria-hidden="true" tabindex="-1"></a>          <span class="at">pred.labels =</span> <span class="fu">c</span>(<span class="st">"Age"</span>, <span class="st">"connection"</span>, <span class="st">"inspection influence"</span>, <span class="st">"ROA"</span>, <span class="st">"env_rate"</span>,</span>
<span id="cb19-65"><a href="#cb19-65" aria-hidden="true" tabindex="-1"></a>                         <span class="st">"leverage"</span>, <span class="st">"inspection influence: connection"</span>, <span class="st">"register capital (log)"</span>),</span>
<span id="cb19-66"><a href="#cb19-66" aria-hidden="true" tabindex="-1"></a>          <span class="at">CSS =</span> oe_css,</span>
<span id="cb19-67"><a href="#cb19-67" aria-hidden="true" tabindex="-1"></a>          <span class="at">file =</span> <span class="st">"B2_p3_extended_inspection_OE_style.html"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>Length of `pred.labels` does not equal number of predictors, no labelling applied.</code></pre>
</div>
<div class="cell-output-display">
<table style="border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px; margin-top: 10px;">
<caption style="font-weight: bold; text-align: left; padding-bottom: 15px; margin-bottom: 5px;">Table B2. Short-term Impact of Inspection</caption>
<tbody><tr>
<th style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; padding: 4px 8px; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">&nbsp;</th>
<th colspan="2" style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; padding: 4px 8px; border-bottom:1px solid black;">Dependent variable:<br>Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col7">std. Error</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Age</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.316 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.305 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.303 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.020</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">connection_num</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.038 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.049</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.112 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.055</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ROA</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.022 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.099</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.020 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.099</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.020 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.099</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ESG_Rate</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.283 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.015</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.282 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.015</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.283 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.015</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)仓储业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.691 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.051</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.751 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.047</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.208 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">4.051</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)畜牧业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.957 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.167</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.710 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.166</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.337 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.168</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)道路运输业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.056 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.626</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.260 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.626</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.500 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.626</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)电力、热力生产和供应业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.427 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.543</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.580 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.541</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.920 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.544</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)电气机械及器材制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.012 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.538</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.158 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.534</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.539 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.539</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)电信、广播电视和卫星传输服务</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.533 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.784</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.801 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.784</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.123 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.786</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)房地产业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-6.362 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.517</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-6.484 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.515</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-6.832 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.518</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)房屋建筑业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.257 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">5.428</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.170 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">5.426</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.968 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">5.425</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)纺织服装、服饰业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.153 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.781</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.311 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.778</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.706 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.782</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)纺织业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.644 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.736</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.518 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.733</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.147 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.737</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)非金属矿物制品业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.379 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.593</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.225 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.591</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.883 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.594</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)废弃资源综合利用业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">9.950 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.667</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">9.717 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.663</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">9.367 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.667</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)公共设施管理业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.912 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.354</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.110 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.352</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.339 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.353</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)广播、电视、电影和影视录音制作业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.807 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.873</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.985 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.870</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.365 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.874</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)航空运输业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.180 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.751</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.469 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.752</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.903 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.754</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)黑色金属矿采选业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.858 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.306</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.796 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.304</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.470 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.305</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)黑色金属冶炼及压延加工业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">6.409 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.621</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">6.277 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.617</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">5.865 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.622</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)互联网和相关服务</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.262 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.679</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.467 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.675</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.844 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.681</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)化学纤维制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">6.339 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.782</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">6.182 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.779</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">5.861 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.782</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)化学原料及化学制品制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.441 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.535</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.258 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.532</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.894 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.536</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)货币金融服务</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-8.913 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.865</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-9.084 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.843</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-8.269 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.869</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)计算机、通信和其他电子设备制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.554 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.512</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.384 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.508</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.008 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.513</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)家具制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.974 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.097</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.150 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.094</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.523 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.098</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)建筑装饰和其他建筑业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.016 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.904</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.226 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.902</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.542 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.905</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)教育</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">10.958 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">8.132</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">10.477 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">8.129</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">10.444 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">8.130</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)金属制品业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.882 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.707</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.699 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.704</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.339 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.708</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)酒、饮料和精制茶制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.052 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.615</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.845 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.614</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.538 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.616</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)居民服务业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-6.311 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">11.021</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-6.354 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">11.017</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-6.683 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">11.014</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)开采辅助活动</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">9.252 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.168</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">9.095 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.165</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">8.729 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.168</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)科技推广和应用服务业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">10.872 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">6.686</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">10.880 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">6.682</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">10.389 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">6.683</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)林业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-7.826 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.044</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-7.891 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.041</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-8.310 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">4.043</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)零售业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.836 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.554</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.984 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.551</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.328 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.555</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)煤炭开采和洗选业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">5.352 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.650</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">5.148 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.649</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.809 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.651</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)农副食品加工业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.107 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.693</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.303 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.690</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.655 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.694</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)农业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.257 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.925</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.447 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.923</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.797 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.926</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)批发业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.509 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.577</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.679 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.575</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.069 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.579</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)皮革、毛皮、羽毛及其制品和制鞋业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.998 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.710</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.096 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.706</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.501 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.709</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)其他服务业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.407 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">11.046</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.498 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">11.042</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.746 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">11.038</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)其他金融业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-7.247 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.944</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-7.465 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.944</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-7.643 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.943</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)其他制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.312 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.216</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.406 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.212</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.786 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.215</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)汽车制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.462 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.572</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.343 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.569</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.964 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.573</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)燃气生产和供应业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.469 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.834</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.647 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.832</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.079 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.836</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)软件和信息技术服务业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.556 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.546</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.733 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.542</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.108 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.548</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)商务服务业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.721 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.690</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.963 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.689</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.327 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.692</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)生态保护和环境治理业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.072 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.909</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.914 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.906</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.617 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.909</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)石油和天然气开采业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.428 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.015</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.252 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.015</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.929 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.016</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)石油加工、炼焦及核燃料加工业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.072 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.847</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.945 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.844</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.578 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.848</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)食品制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.642 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.695</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.518 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.690</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.117 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.696</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)水的生产和供应业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.643 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.802</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.481 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.801</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.151 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.803</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)水上运输业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.518 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.643</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.300 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.644</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.018 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.644</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)体育</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.107 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">11.045</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.191 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">11.041</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.085 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">11.038</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)铁路、船舶、航空航天和其它运输设备制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.062 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.608</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.905 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.604</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.531 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.609</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)铁路运输业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.126 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.193</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.038 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.188</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.549 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.194</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)通用设备制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.392 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.588</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.254 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.584</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.148 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.589</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)土木工程建筑业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.832 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.570</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.030 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.569</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.435 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.572</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)卫生</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">7.814 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.995</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">7.636 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.992</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">7.256 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.996</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)文化艺术业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.615 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.618</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.825 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.615</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.165 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.618</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)文教、工美、体育和娱乐用品制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.717 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.178</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.584 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.175</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.174 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.179</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)橡胶和塑料制品业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.787 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.748</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.938 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.744</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.317 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.749</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)新闻和出版业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.958 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.707</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.182 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.707</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.573 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.710</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)研究和试验发展</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">16.532 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.315</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">16.515 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.311</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">16.056 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.315</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)医药制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">5.742 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.524</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">5.578 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.522</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">5.216 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.526</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)仪器仪表制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.291 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.901</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.421 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.896</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.805 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.902</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)印刷和记录媒介复制业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.714 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.113</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.884 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.112</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-3.199 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.113</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)邮政业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.022 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.357</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.863 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.352</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.517 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.357</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)有色金属矿采选业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">6.597 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.661</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">6.430 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.659</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">6.092 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.662</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)有色金属冶炼及压延加工业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">5.100 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.586</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.950 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.583</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.580 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.587</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)渔业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-5.538 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.162</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-5.707 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.156</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-6.110 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">4.160</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)造纸及纸制品业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.605 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.812</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.752 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.808</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.113 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.813</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)住宿业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.568 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.203</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.794 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.202</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.129 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.204</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)专业技术服务业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.221 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.055</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.048 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.054</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.434 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.057</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)专用设备制造业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.495 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.542</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.311 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.539</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.046 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.543</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)装卸搬运和运输代理业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.160 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.175</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.287 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.174</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.367 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">3.173</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)资本市场服务</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-9.247 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.676</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-9.378 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.675</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-9.368 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.674</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)综合</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.763 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.746</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-4.875 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.744</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-5.161 <sup>*</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">2.746</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">as.factor(IndustryName)租赁业</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-8.687 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.323</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-8.735 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">4.321</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-8.943 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">4.321</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Leverage</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.000 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.000 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.000 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.002</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">RegisterCapital_log</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.071 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.118</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.014 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.117</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">3.038 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.119</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">extended_inspection</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.695 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.239</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.138 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.302</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">extended_inspection:connection_num</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.290 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.097</td>
</tr>
<tr>
<td colspan="7" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">115.15</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">115.08</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">115.00</td>
</tr>

<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">13.20 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">13.08 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">13.13 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">3.91 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">3.85 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">3.85 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.13</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.13</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.13</td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.209 / 0.311</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.209 / 0.310</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.210 / 0.311</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">AIC</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">81932.495</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">81921.494</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">81923.012</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">log-Likelihood</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-40880.248</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-40874.747</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-40873.506</td>
</tr>
<tr>
<td colspan="7" style="font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;">* p&lt;0.1&nbsp;&nbsp;&nbsp;** p&lt;0.05&nbsp;&nbsp;&nbsp;*** p&lt;0.01</td>
</tr>

</tbody></table>

</div>
<div class="sourceCode cell-code" id="cb21"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb21-1"><a href="#cb21-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Fix table header alignment</span></span>
<span id="cb21-2"><a href="#cb21-2" aria-hidden="true" tabindex="-1"></a>fix_table_header <span class="ot">&lt;-</span> <span class="cf">function</span>(filename) {</span>
<span id="cb21-3"><a href="#cb21-3" aria-hidden="true" tabindex="-1"></a>  <span class="cf">if</span> (<span class="fu">file.exists</span>(filename)) {</span>
<span id="cb21-4"><a href="#cb21-4" aria-hidden="true" tabindex="-1"></a>    content <span class="ot">&lt;-</span> <span class="fu">readLines</span>(filename, <span class="at">warn =</span> <span class="cn">FALSE</span>)</span>
<span id="cb21-5"><a href="#cb21-5" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Fix colspan for dependent variable header</span></span>
<span id="cb21-6"><a href="#cb21-6" aria-hidden="true" tabindex="-1"></a>    content <span class="ot">&lt;-</span> <span class="fu">gsub</span>(<span class="st">'colspan="2"([^&gt;]*&gt;Dependent variable)'</span>, <span class="st">'colspan="6"</span><span class="sc">\\</span><span class="st">1'</span>, content)</span>
<span id="cb21-7"><a href="#cb21-7" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Ensure center alignment for the header</span></span>
<span id="cb21-8"><a href="#cb21-8" aria-hidden="true" tabindex="-1"></a>    content <span class="ot">&lt;-</span> <span class="fu">gsub</span>(<span class="st">'(colspan="6"[^&gt;]*style="[^"]*)("&gt;Dependent variable)'</span>, <span class="st">'</span><span class="sc">\\</span><span class="st">1 text-align: center;</span><span class="sc">\\</span><span class="st">2'</span>, content)</span>
<span id="cb21-9"><a href="#cb21-9" aria-hidden="true" tabindex="-1"></a>    <span class="fu">writeLines</span>(content, filename)</span>
<span id="cb21-10"><a href="#cb21-10" aria-hidden="true" tabindex="-1"></a>  }</span>
<span id="cb21-11"><a href="#cb21-11" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb21-12"><a href="#cb21-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-13"><a href="#cb21-13" aria-hidden="true" tabindex="-1"></a><span class="fu">fix_table_header</span>(<span class="st">"B2_p3_extended_inspection_OE_style.html"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</div>
</section>
<section id="p4-contrallocal-connection-continuous" class="level3">
<h3 class="anchored" data-anchor-id="p4-contrallocal-connection-continuous">P4 contral/local connection (continuous)</h3>
<p>Table B3. Short-term Impact of Inspection (central and local connections)</p>
<div class="cell">
<div class="sourceCode cell-code" id="cb22"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb22-1"><a href="#cb22-1" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span>
<span id="cb22-2"><a href="#cb22-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb22-3"><a href="#cb22-3" aria-hidden="true" tabindex="-1"></a>p4ext1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> central_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb22-4"><a href="#cb22-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb22-5"><a href="#cb22-5" aria-hidden="true" tabindex="-1"></a>p4ext2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> central_connection <span class="sc">*</span> extended_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb22-6"><a href="#cb22-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb22-7"><a href="#cb22-7" aria-hidden="true" tabindex="-1"></a>p4ext3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> local_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb22-8"><a href="#cb22-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb22-9"><a href="#cb22-9" aria-hidden="true" tabindex="-1"></a>p4ext4 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> local_connection <span class="sc">*</span> extended_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb22-10"><a href="#cb22-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb22-11"><a href="#cb22-11" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table for central/local connections</span></span>
<span id="cb22-12"><a href="#cb22-12" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p4ext1, p4ext2, p4ext3, p4ext4,</span>
<span id="cb22-13"><a href="#cb22-13" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Table B3. Short-term Impact of Inspection (Central and Local Connections)"</span>,</span>
<span id="cb22-14"><a href="#cb22-14" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Dependent variable:&lt;br&gt;Environmental Information Disclosure"</span>,</span>
<span id="cb22-15"><a href="#cb22-15" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.aic =</span> <span class="cn">TRUE</span>,</span>
<span id="cb22-16"><a href="#cb22-16" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.loglik =</span> <span class="cn">TRUE</span>,</span>
<span id="cb22-17"><a href="#cb22-17" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.obs =</span> <span class="cn">TRUE</span>,</span>
<span id="cb22-18"><a href="#cb22-18" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ngroups =</span> <span class="cn">TRUE</span>,</span>
<span id="cb22-19"><a href="#cb22-19" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.re.var =</span> <span class="cn">TRUE</span>,</span>
<span id="cb22-20"><a href="#cb22-20" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb22-21"><a href="#cb22-21" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.intercept =</span> <span class="cn">FALSE</span>, <span class="co"># Hide intercept</span></span>
<span id="cb22-22"><a href="#cb22-22" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.p =</span> <span class="cn">FALSE</span>,        <span class="co"># Hide p-values column</span></span>
<span id="cb22-23"><a href="#cb22-23" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.se =</span> <span class="cn">TRUE</span>,        <span class="co"># Show standard errors in parentheses</span></span>
<span id="cb22-24"><a href="#cb22-24" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ci =</span> <span class="cn">FALSE</span>,       <span class="co"># Hide confidence intervals</span></span>
<span id="cb22-25"><a href="#cb22-25" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.style =</span> <span class="st">"stars"</span>,     <span class="co"># Display significance as stars</span></span>
<span id="cb22-26"><a href="#cb22-26" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.threshold =</span> <span class="fu">c</span>(<span class="fl">0.1</span>, <span class="fl">0.05</span>, <span class="fl">0.01</span>),  <span class="co"># Set significance thresholds</span></span>
<span id="cb22-27"><a href="#cb22-27" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits =</span> <span class="dv">3</span>,            <span class="co"># 3 decimal places</span></span>
<span id="cb22-28"><a href="#cb22-28" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits.re =</span> <span class="dv">2</span>,         <span class="co"># 2 decimal places for random effects</span></span>
<span id="cb22-29"><a href="#cb22-29" aria-hidden="true" tabindex="-1"></a>          <span class="at">CSS =</span> oe_css,</span>
<span id="cb22-30"><a href="#cb22-30" aria-hidden="true" tabindex="-1"></a>          <span class="at">file =</span> <span class="st">"B3_p4_extended_inspection_OE_style.html"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px; margin-top: 10px;">
<caption style="font-weight: bold; text-align: left; padding-bottom: 15px; margin-bottom: 5px;">Table B3. Short-term Impact of Inspection (Central and Local Connections)</caption>
<tbody><tr>
<th style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; padding: 4px 8px; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">&nbsp;</th>
<th colspan="2" style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; padding: 4px 8px; border-bottom:1px solid black;">Dependent variable:<br>Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col7">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col8">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col9">std. Error</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Age</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.253 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.237 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.256 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.020</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">0.238 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.020</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">central connection</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.755 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.176</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.946 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.193</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9"></td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ESG Rate</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.268 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.266 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.265 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.014</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">0.264 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.014</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">RegisterCapital log</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.914 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.110</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.877 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.111</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.966 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.110</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">2.941 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.111</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ROA</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.019 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.020 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.018 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.104</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">0.020 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.103</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Leverage</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.002</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">0.001 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.002</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">extended inspection</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.753 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.263</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">0.278 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.313</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">central connection ×<br>extended inspection</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.265 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.414</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9"></td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">local connection</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-0.299 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.052</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">-0.400 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.059</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">local connection ×<br>extended inspection</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">0.430 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.110</td>
</tr>
<tr>
<td colspan="9" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.57</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.30</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.41</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.06</td>
</tr>

<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.92 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.79 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.66 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.58 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.23 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.26 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.46 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.42 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.127 / 0.215</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.129 / 0.216</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.129 / 0.216</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.131 / 0.218</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">AIC</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83041.522</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83020.298</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83029.624</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83004.713</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">log-Likelihood</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41510.761</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41498.149</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41504.812</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41490.356</td>
</tr>
<tr>
<td colspan="9" style="font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;">* p&lt;0.1&nbsp;&nbsp;&nbsp;** p&lt;0.05&nbsp;&nbsp;&nbsp;*** p&lt;0.01</td>
</tr>

</tbody></table>

</div>
</div>
</section>
</section>
<section id="state-owned-enterprises" class="level2">
<h2 class="anchored" data-anchor-id="state-owned-enterprises">State-owned Enterprises</h2>
<section id="p3-soe-new-dataset" class="level3">
<h3 class="anchored" data-anchor-id="p3-soe-new-dataset">P3 SOE new dataset</h3>
<p>Table B4. State-owned Enterprises We labelled firms where the actual controllers are state-owned institutions or state-owned enterprises as politically connected.</p>
<div class="cell">
<div class="sourceCode cell-code" id="cb23"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb23-1"><a href="#cb23-1" aria-hidden="true" tabindex="-1"></a><span class="co">#P3</span></span>
<span id="cb23-2"><a href="#cb23-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-3"><a href="#cb23-3" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span>
<span id="cb23-4"><a href="#cb23-4" aria-hidden="true" tabindex="-1"></a>p1soe1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> SOE_new <span class="sc">+</span> ROA <span class="sc">+</span> ESG_Rate <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb23-5"><a href="#cb23-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-6"><a href="#cb23-6" aria-hidden="true" tabindex="-1"></a>p1soe2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb23-7"><a href="#cb23-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-8"><a href="#cb23-8" aria-hidden="true" tabindex="-1"></a>p1soe3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">*</span> SOE_new <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb23-9"><a href="#cb23-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-10"><a href="#cb23-10" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table for SOE analysis</span></span>
<span id="cb23-11"><a href="#cb23-11" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p1soe1, p1soe2, p1soe3,</span>
<span id="cb23-12"><a href="#cb23-12" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Table B4. State-owned Enterprises and Environmental Disclosure"</span>,</span>
<span id="cb23-13"><a href="#cb23-13" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Dependent variable:&lt;br&gt;Environmental Information Disclosure"</span>,</span>
<span id="cb23-14"><a href="#cb23-14" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.aic =</span> <span class="cn">TRUE</span>,</span>
<span id="cb23-15"><a href="#cb23-15" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.loglik =</span> <span class="cn">TRUE</span>,</span>
<span id="cb23-16"><a href="#cb23-16" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.obs =</span> <span class="cn">TRUE</span>,</span>
<span id="cb23-17"><a href="#cb23-17" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ngroups =</span> <span class="cn">TRUE</span>,</span>
<span id="cb23-18"><a href="#cb23-18" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.re.var =</span> <span class="cn">TRUE</span>,</span>
<span id="cb23-19"><a href="#cb23-19" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb23-20"><a href="#cb23-20" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.intercept =</span> <span class="cn">FALSE</span>, <span class="co"># Hide intercept</span></span>
<span id="cb23-21"><a href="#cb23-21" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.p =</span> <span class="cn">FALSE</span>,        <span class="co"># Hide p-values column</span></span>
<span id="cb23-22"><a href="#cb23-22" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.se =</span> <span class="cn">TRUE</span>,        <span class="co"># Show standard errors in parentheses</span></span>
<span id="cb23-23"><a href="#cb23-23" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ci =</span> <span class="cn">FALSE</span>,       <span class="co"># Hide confidence intervals</span></span>
<span id="cb23-24"><a href="#cb23-24" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.style =</span> <span class="st">"stars"</span>,     <span class="co"># Display significance as stars</span></span>
<span id="cb23-25"><a href="#cb23-25" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.threshold =</span> <span class="fu">c</span>(<span class="fl">0.1</span>, <span class="fl">0.05</span>, <span class="fl">0.01</span>),  <span class="co"># Set significance thresholds</span></span>
<span id="cb23-26"><a href="#cb23-26" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits =</span> <span class="dv">3</span>,            <span class="co"># 3 decimal places</span></span>
<span id="cb23-27"><a href="#cb23-27" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits.re =</span> <span class="dv">2</span>,         <span class="co"># 2 decimal places for random effects</span></span>
<span id="cb23-28"><a href="#cb23-28" aria-hidden="true" tabindex="-1"></a>          <span class="at">CSS =</span> oe_css,</span>
<span id="cb23-29"><a href="#cb23-29" aria-hidden="true" tabindex="-1"></a>          <span class="at">file =</span> <span class="st">"B4_SOE_result_p1_OE_style.html"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px; margin-top: 10px;">
<caption style="font-weight: bold; text-align: left; padding-bottom: 15px; margin-bottom: 5px;">Table B4. State-owned Enterprises and Environmental Disclosure</caption>
<tbody><tr>
<th style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; padding: 4px 8px; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">&nbsp;</th>
<th colspan="2" style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; padding: 4px 8px; border-bottom:1px solid black;">Dependent variable:<br>Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col7">std. Error</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Age</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.273 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.045 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.021</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">SOE new</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.261 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.249</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-1.008 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.345</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ROA</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.011 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.037 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.100</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.031 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.100</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ESG Rate</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.270 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.261 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.261 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.014</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Leverage</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.002</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">RegisterCapital log</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.866 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.107</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.501 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.103</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.489 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.105</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">after first inspection</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">6.609 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.218</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">5.715 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.325</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">after first inspection ×<br>SOE new</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.265 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.430</td>
</tr>
<tr>
<td colspan="7" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.49</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">118.99</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">118.67</td>
</tr>

<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.82 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">11.28 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">11.33 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.30 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.51 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.48 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10777</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.127 / 0.215</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.179 / 0.258</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.178 / 0.258</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">AIC</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83033.670</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">82407.560</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">82343.522</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">log-Likelihood</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41506.835</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41194.780</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41159.761</td>
</tr>
<tr>
<td colspan="7" style="font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;">* p&lt;0.1&nbsp;&nbsp;&nbsp;** p&lt;0.05&nbsp;&nbsp;&nbsp;*** p&lt;0.01</td>
</tr>

</tbody></table>

</div>
</div>
</section>
<section id="p4-centrallocal-soe-level" class="level3">
<h3 class="anchored" data-anchor-id="p4-centrallocal-soe-level">P4 central/local SOE Level</h3>
<div class="cell">
<div class="sourceCode cell-code" id="cb24"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb24-1"><a href="#cb24-1" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span>
<span id="cb24-2"><a href="#cb24-2" aria-hidden="true" tabindex="-1"></a>p2soe1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> SOE_new_central <span class="sc">+</span> Age <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb24-3"><a href="#cb24-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb24-4"><a href="#cb24-4" aria-hidden="true" tabindex="-1"></a>p2soe2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> SOE_new_central <span class="sc">*</span> after_first_inspection <span class="sc">+</span> Age <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb24-5"><a href="#cb24-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb24-6"><a href="#cb24-6" aria-hidden="true" tabindex="-1"></a>p2soe3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> SOE_new_local <span class="sc">+</span> Age <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb24-7"><a href="#cb24-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb24-8"><a href="#cb24-8" aria-hidden="true" tabindex="-1"></a>p2soe4 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> SOE_new_local <span class="sc">*</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb24-9"><a href="#cb24-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb24-10"><a href="#cb24-10" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table for central/local SOE analysis</span></span>
<span id="cb24-11"><a href="#cb24-11" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p2soe1, p2soe2, p2soe3, p2soe4,</span>
<span id="cb24-12"><a href="#cb24-12" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Table B5. Central vs Local State-owned Enterprises"</span>,</span>
<span id="cb24-13"><a href="#cb24-13" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Dependent variable:&lt;br&gt;Environmental Information Disclosure"</span>,</span>
<span id="cb24-14"><a href="#cb24-14" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.aic =</span> <span class="cn">TRUE</span>,</span>
<span id="cb24-15"><a href="#cb24-15" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.loglik =</span> <span class="cn">TRUE</span>,</span>
<span id="cb24-16"><a href="#cb24-16" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.obs =</span> <span class="cn">TRUE</span>,</span>
<span id="cb24-17"><a href="#cb24-17" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ngroups =</span> <span class="cn">TRUE</span>,</span>
<span id="cb24-18"><a href="#cb24-18" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.re.var =</span> <span class="cn">TRUE</span>,</span>
<span id="cb24-19"><a href="#cb24-19" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb24-20"><a href="#cb24-20" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.intercept =</span> <span class="cn">FALSE</span>, <span class="co"># Hide intercept</span></span>
<span id="cb24-21"><a href="#cb24-21" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.p =</span> <span class="cn">FALSE</span>,        <span class="co"># Hide p-values column</span></span>
<span id="cb24-22"><a href="#cb24-22" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.se =</span> <span class="cn">TRUE</span>,        <span class="co"># Show standard errors in parentheses</span></span>
<span id="cb24-23"><a href="#cb24-23" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ci =</span> <span class="cn">FALSE</span>,       <span class="co"># Hide confidence intervals</span></span>
<span id="cb24-24"><a href="#cb24-24" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.style =</span> <span class="st">"stars"</span>,     <span class="co"># Display significance as stars</span></span>
<span id="cb24-25"><a href="#cb24-25" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.threshold =</span> <span class="fu">c</span>(<span class="fl">0.1</span>, <span class="fl">0.05</span>, <span class="fl">0.01</span>),  <span class="co"># Set significance thresholds</span></span>
<span id="cb24-26"><a href="#cb24-26" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits =</span> <span class="dv">3</span>,            <span class="co"># 3 decimal places</span></span>
<span id="cb24-27"><a href="#cb24-27" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits.re =</span> <span class="dv">2</span>,         <span class="co"># 2 decimal places for random effects</span></span>
<span id="cb24-28"><a href="#cb24-28" aria-hidden="true" tabindex="-1"></a>          <span class="at">CSS =</span> oe_css,</span>
<span id="cb24-29"><a href="#cb24-29" aria-hidden="true" tabindex="-1"></a>          <span class="at">file =</span> <span class="st">"B5_SOE_result_p2_OE_style.html"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px; margin-top: 10px;">
<caption style="font-weight: bold; text-align: left; padding-bottom: 15px; margin-bottom: 5px;">Table B5. Central vs Local State-owned Enterprises</caption>
<tbody><tr>
<th style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; padding: 4px 8px; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">&nbsp;</th>
<th colspan="2" style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; padding: 4px 8px; border-bottom:1px solid black;">Dependent variable:<br>Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col7">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col8">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col9">std. Error</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">SOE new central</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.452 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.306</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.944 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.423</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9"></td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Age</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.263 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.042 <sup>**</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.021</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.287 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.020</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">0.066 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.021</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ESG Rate</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.259 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.252 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.264 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.014</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">0.260 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.014</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">RegisterCapital log</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.718 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.107</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.378 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">2.829 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.106</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">2.493 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.103</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ROA</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.022 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.039 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.100</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.011 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.103</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">0.030 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.100</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Leverage</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.002</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">0.001 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.002</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">after first inspection</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">6.171 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.259</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">6.142 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.277</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">SOE new central × after<br>first inspection</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">1.404 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">0.531</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9"></td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">SOE new local</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  ">-2.434 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7">0.260</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">-1.722 <sup>***</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.354</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">SOE new local × after<br>first inspection</td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col8">0.242 <sup></sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: center;  col9">0.461</td>
</tr>
<tr>
<td colspan="9" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.51</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">118.28</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">125.70</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">118.32</td>
</tr>

<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.98 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">11.61 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">13.12 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">11.39 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.57 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.55 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.71 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.128 / 0.217</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.181 / 0.264</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.133 / 0.224</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.181 / 0.262</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">AIC</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83036.410</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">82311.324</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">82971.874</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">82313.827</td>
</tr>
<tr>
<td style=" border: none; padding: 3px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">log-Likelihood</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41508.205</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41143.662</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41475.937</td>
<td style=" border: none; padding: 3px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41144.914</td>
</tr>
<tr>
<td colspan="9" style="font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;">* p&lt;0.1&nbsp;&nbsp;&nbsp;** p&lt;0.05&nbsp;&nbsp;&nbsp;*** p&lt;0.01</td>
</tr>

</tbody></table>

</div>
</div>
</section>
</section>
<section id="plm-model" class="level2">
<h2 class="anchored" data-anchor-id="plm-model">Plm model</h2>
<section id="p3-connection-continuous-1" class="level3">
<h3 class="anchored" data-anchor-id="p3-connection-continuous-1">P3 connection (continuous)</h3>
<div class="cell">
<div class="sourceCode cell-code" id="cb25"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb25-1"><a href="#cb25-1" aria-hidden="true" tabindex="-1"></a><span class="co">#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure</span></span>
<span id="cb25-2"><a href="#cb25-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(plm)</span>
<span id="cb25-3"><a href="#cb25-3" aria-hidden="true" tabindex="-1"></a>p3plm1 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> connection_num <span class="sc">+</span> ROA <span class="sc">+</span> ESG_Rate <span class="sc">+</span> Leverage <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb25-4"><a href="#cb25-4" aria-hidden="true" tabindex="-1"></a>              <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>, <span class="st">"EndYear"</span>),<span class="at">model=</span><span class="st">"within"</span>,<span class="at">effect=</span><span class="st">"twoways"</span>)</span>
<span id="cb25-5"><a href="#cb25-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb25-6"><a href="#cb25-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb25-7"><a href="#cb25-7" aria-hidden="true" tabindex="-1"></a>p3plm2 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span>ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb25-8"><a href="#cb25-8" aria-hidden="true" tabindex="-1"></a>              <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>),<span class="at">model=</span><span class="st">"within"</span>)</span>
<span id="cb25-9"><a href="#cb25-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb25-10"><a href="#cb25-10" aria-hidden="true" tabindex="-1"></a>p3plm3 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">*</span> connection_num <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb25-11"><a href="#cb25-11" aria-hidden="true" tabindex="-1"></a>              <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>),<span class="at">model=</span><span class="st">"within"</span>)</span>
<span id="cb25-12"><a href="#cb25-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb25-13"><a href="#cb25-13" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table for PLM models using modelsummary</span></span>
<span id="cb25-14"><a href="#cb25-14" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(modelsummary)</span>
<span id="cb25-15"><a href="#cb25-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb25-16"><a href="#cb25-16" aria-hidden="true" tabindex="-1"></a><span class="co"># Define coefficient names for cleaner display</span></span>
<span id="cb25-17"><a href="#cb25-17" aria-hidden="true" tabindex="-1"></a>coef_map_plm <span class="ot">&lt;-</span> <span class="fu">c</span>(</span>
<span id="cb25-18"><a href="#cb25-18" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Age"</span> <span class="ot">=</span> <span class="st">"Age"</span>,</span>
<span id="cb25-19"><a href="#cb25-19" aria-hidden="true" tabindex="-1"></a>  <span class="st">"connection_num"</span> <span class="ot">=</span> <span class="st">"Connections"</span>,</span>
<span id="cb25-20"><a href="#cb25-20" aria-hidden="true" tabindex="-1"></a>  <span class="st">"after_first_inspection"</span> <span class="ot">=</span> <span class="st">"After First Inspection"</span>,</span>
<span id="cb25-21"><a href="#cb25-21" aria-hidden="true" tabindex="-1"></a>  <span class="st">"ESG_Rate"</span> <span class="ot">=</span> <span class="st">"ESG Rating"</span>,</span>
<span id="cb25-22"><a href="#cb25-22" aria-hidden="true" tabindex="-1"></a>  <span class="st">"ROA"</span> <span class="ot">=</span> <span class="st">"Return on Assets"</span>,</span>
<span id="cb25-23"><a href="#cb25-23" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Leverage"</span> <span class="ot">=</span> <span class="st">"Leverage"</span>,</span>
<span id="cb25-24"><a href="#cb25-24" aria-hidden="true" tabindex="-1"></a>  <span class="st">"RegisterCapital_log"</span> <span class="ot">=</span> <span class="st">"Log(Registered Capital)"</span>,</span>
<span id="cb25-25"><a href="#cb25-25" aria-hidden="true" tabindex="-1"></a>  <span class="st">"connection_num:after_first_inspection"</span> <span class="ot">=</span> <span class="st">"Connections × After Inspection"</span></span>
<span id="cb25-26"><a href="#cb25-26" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb25-27"><a href="#cb25-27" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb25-28"><a href="#cb25-28" aria-hidden="true" tabindex="-1"></a><span class="co"># Create rows for fixed effects that will appear before statistics</span></span>
<span id="cb25-29"><a href="#cb25-29" aria-hidden="true" tabindex="-1"></a>fe_rows <span class="ot">&lt;-</span> <span class="fu">tribble</span>(</span>
<span id="cb25-30"><a href="#cb25-30" aria-hidden="true" tabindex="-1"></a>  <span class="sc">~</span>term, <span class="sc">~</span><span class="st">`</span><span class="at">Two-way FE</span><span class="st">`</span>, <span class="sc">~</span><span class="st">`</span><span class="at">Individual FE</span><span class="st">`</span>, <span class="sc">~</span><span class="st">`</span><span class="at">Interaction Model</span><span class="st">`</span>,</span>
<span id="cb25-31"><a href="#cb25-31" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Firm fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb25-32"><a href="#cb25-32" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Industry fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb25-33"><a href="#cb25-33" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Province fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb25-34"><a href="#cb25-34" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Year fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"N"</span>, <span class="st">"N"</span></span>
<span id="cb25-35"><a href="#cb25-35" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb25-36"><a href="#cb25-36" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb25-37"><a href="#cb25-37" aria-hidden="true" tabindex="-1"></a><span class="co"># Define custom goodness-of-fit map</span></span>
<span id="cb25-38"><a href="#cb25-38" aria-hidden="true" tabindex="-1"></a>gof_map_plm <span class="ot">&lt;-</span> <span class="fu">tribble</span>(</span>
<span id="cb25-39"><a href="#cb25-39" aria-hidden="true" tabindex="-1"></a>  <span class="sc">~</span>raw, <span class="sc">~</span>clean, <span class="sc">~</span>fmt,</span>
<span id="cb25-40"><a href="#cb25-40" aria-hidden="true" tabindex="-1"></a>  <span class="st">"nobs"</span>, <span class="st">"Num.Obs."</span>, <span class="dv">0</span>,</span>
<span id="cb25-41"><a href="#cb25-41" aria-hidden="true" tabindex="-1"></a>  <span class="st">"r.squared"</span>, <span class="st">"R²"</span>, <span class="dv">3</span>,</span>
<span id="cb25-42"><a href="#cb25-42" aria-hidden="true" tabindex="-1"></a>  <span class="st">"adj.r.squared"</span>, <span class="st">"Adj.R²"</span>, <span class="dv">3</span></span>
<span id="cb25-43"><a href="#cb25-43" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb25-44"><a href="#cb25-44" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb25-45"><a href="#cb25-45" aria-hidden="true" tabindex="-1"></a><span class="co"># Create modelsummary table with Organization &amp; Environment style</span></span>
<span id="cb25-46"><a href="#cb25-46" aria-hidden="true" tabindex="-1"></a>plm_p3_table <span class="ot">&lt;-</span> <span class="fu">modelsummary</span>(</span>
<span id="cb25-47"><a href="#cb25-47" aria-hidden="true" tabindex="-1"></a>  <span class="fu">list</span>(<span class="st">"Two-way FE"</span> <span class="ot">=</span> p3plm1, <span class="st">"Individual FE"</span> <span class="ot">=</span> p3plm2, <span class="st">"Interaction Model"</span> <span class="ot">=</span> p3plm3),</span>
<span id="cb25-48"><a href="#cb25-48" aria-hidden="true" tabindex="-1"></a>  <span class="at">output =</span> <span class="st">"gt"</span>,</span>
<span id="cb25-49"><a href="#cb25-49" aria-hidden="true" tabindex="-1"></a>  <span class="at">stars =</span> <span class="fu">c</span>(<span class="st">'*'</span> <span class="ot">=</span> .<span class="dv">1</span>, <span class="st">'**'</span> <span class="ot">=</span> .<span class="dv">05</span>, <span class="st">'***'</span> <span class="ot">=</span> .<span class="dv">01</span>),</span>
<span id="cb25-50"><a href="#cb25-50" aria-hidden="true" tabindex="-1"></a>  <span class="at">coef_map =</span> coef_map_plm,</span>
<span id="cb25-51"><a href="#cb25-51" aria-hidden="true" tabindex="-1"></a>  <span class="at">gof_map =</span> gof_map_plm,</span>
<span id="cb25-52"><a href="#cb25-52" aria-hidden="true" tabindex="-1"></a>  <span class="at">title =</span> <span class="st">"Table B6. Fixed Effects Models: Political Connections"</span>,</span>
<span id="cb25-53"><a href="#cb25-53" aria-hidden="true" tabindex="-1"></a>  <span class="at">notes =</span> <span class="fu">list</span>(<span class="st">"Standard errors in parentheses."</span>,</span>
<span id="cb25-54"><a href="#cb25-54" aria-hidden="true" tabindex="-1"></a>              <span class="st">"* p &lt; 0.1, ** p &lt; 0.05, *** p &lt; 0.01"</span>),</span>
<span id="cb25-55"><a href="#cb25-55" aria-hidden="true" tabindex="-1"></a>  <span class="at">fmt =</span> <span class="dv">3</span>,</span>
<span id="cb25-56"><a href="#cb25-56" aria-hidden="true" tabindex="-1"></a>  <span class="at">estimate =</span> <span class="st">"{estimate}{stars}"</span>,</span>
<span id="cb25-57"><a href="#cb25-57" aria-hidden="true" tabindex="-1"></a>  <span class="at">statistic =</span> <span class="st">"({std.error})"</span>,</span>
<span id="cb25-58"><a href="#cb25-58" aria-hidden="true" tabindex="-1"></a>  <span class="at">add_rows =</span> fe_rows</span>
<span id="cb25-59"><a href="#cb25-59" aria-hidden="true" tabindex="-1"></a>) <span class="sc">%&gt;%</span></span>
<span id="cb25-60"><a href="#cb25-60" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_style</span>(</span>
<span id="cb25-61"><a href="#cb25-61" aria-hidden="true" tabindex="-1"></a>    <span class="at">style =</span> <span class="fu">cell_text</span>(<span class="at">weight =</span> <span class="st">"bold"</span>),</span>
<span id="cb25-62"><a href="#cb25-62" aria-hidden="true" tabindex="-1"></a>    <span class="at">locations =</span> <span class="fu">cells_column_labels</span>()</span>
<span id="cb25-63"><a href="#cb25-63" aria-hidden="true" tabindex="-1"></a>  ) <span class="sc">%&gt;%</span></span>
<span id="cb25-64"><a href="#cb25-64" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_style</span>(</span>
<span id="cb25-65"><a href="#cb25-65" aria-hidden="true" tabindex="-1"></a>    <span class="at">style =</span> <span class="fu">cell_text</span>(<span class="at">weight =</span> <span class="st">"bold"</span>),</span>
<span id="cb25-66"><a href="#cb25-66" aria-hidden="true" tabindex="-1"></a>    <span class="at">locations =</span> <span class="fu">cells_stub</span>()</span>
<span id="cb25-67"><a href="#cb25-67" aria-hidden="true" tabindex="-1"></a>  ) <span class="sc">%&gt;%</span></span>
<span id="cb25-68"><a href="#cb25-68" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_options</span>(</span>
<span id="cb25-69"><a href="#cb25-69" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.font.size =</span> <span class="dv">11</span>,</span>
<span id="cb25-70"><a href="#cb25-70" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.title.font.size =</span> <span class="dv">12</span>,</span>
<span id="cb25-71"><a href="#cb25-71" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.subtitle.font.size =</span> <span class="dv">11</span>,</span>
<span id="cb25-72"><a href="#cb25-72" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.border.top.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb25-73"><a href="#cb25-73" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb25-74"><a href="#cb25-74" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb25-75"><a href="#cb25-75" aria-hidden="true" tabindex="-1"></a>    <span class="at">column_labels.border.top.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb25-76"><a href="#cb25-76" aria-hidden="true" tabindex="-1"></a>    <span class="at">column_labels.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb25-77"><a href="#cb25-77" aria-hidden="true" tabindex="-1"></a>    <span class="at">stub.border.style =</span> <span class="st">"solid"</span></span>
<span id="cb25-78"><a href="#cb25-78" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb25-79"><a href="#cb25-79" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb25-80"><a href="#cb25-80" aria-hidden="true" tabindex="-1"></a><span class="co"># Save to file</span></span>
<span id="cb25-81"><a href="#cb25-81" aria-hidden="true" tabindex="-1"></a><span class="fu">gtsave</span>(plm_p3_table, <span class="st">"B6_plm_p3_OE_style.html"</span>)</span>
<span id="cb25-82"><a href="#cb25-82" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(plm_p3_table)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>&lt;div id="mkrdlvgjzw" style="padding-left:0px;padding-right:0px;padding-top:10px;padding-bottom:10px;overflow-x:auto;overflow-y:auto;width:auto;height:auto;"&gt;
  &lt;style&gt;#mkrdlvgjzw table {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#mkrdlvgjzw thead, #mkrdlvgjzw tbody, #mkrdlvgjzw tfoot, #mkrdlvgjzw tr, #mkrdlvgjzw td, #mkrdlvgjzw th {
  border-style: none;
}

#mkrdlvgjzw p {
  margin: 0;
  padding: 0;
}

#mkrdlvgjzw .gt_table {
  display: table;
  border-collapse: collapse;
  line-height: normal;
  margin-left: auto;
  margin-right: auto;
  color: #333333;
  font-size: 11px;
  font-weight: normal;
  font-style: normal;
  background-color: #FFFFFF;
  width: auto;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #A8A8A8;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #A8A8A8;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
}

#mkrdlvgjzw .gt_caption {
  padding-top: 4px;
  padding-bottom: 4px;
}

#mkrdlvgjzw .gt_title {
  color: #333333;
  font-size: 12px;
  font-weight: initial;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-color: #FFFFFF;
  border-bottom-width: 0;
}

#mkrdlvgjzw .gt_subtitle {
  color: #333333;
  font-size: 11px;
  font-weight: initial;
  padding-top: 3px;
  padding-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-color: #FFFFFF;
  border-top-width: 0;
}

#mkrdlvgjzw .gt_heading {
  background-color: #FFFFFF;
  text-align: center;
  border-bottom-color: #FFFFFF;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#mkrdlvgjzw .gt_bottom_border {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#mkrdlvgjzw .gt_col_headings {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#mkrdlvgjzw .gt_col_heading {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 6px;
  padding-left: 5px;
  padding-right: 5px;
  overflow-x: hidden;
}

#mkrdlvgjzw .gt_column_spanner_outer {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 4px;
}

#mkrdlvgjzw .gt_column_spanner_outer:first-child {
  padding-left: 0;
}

#mkrdlvgjzw .gt_column_spanner_outer:last-child {
  padding-right: 0;
}

#mkrdlvgjzw .gt_column_spanner {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 5px;
  overflow-x: hidden;
  display: inline-block;
  width: 100%;
}

#mkrdlvgjzw .gt_spanner_row {
  border-bottom-style: hidden;
}

#mkrdlvgjzw .gt_group_heading {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  text-align: left;
}

#mkrdlvgjzw .gt_empty_group_heading {
  padding: 0.5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: middle;
}

#mkrdlvgjzw .gt_from_md &gt; :first-child {
  margin-top: 0;
}

#mkrdlvgjzw .gt_from_md &gt; :last-child {
  margin-bottom: 0;
}

#mkrdlvgjzw .gt_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  margin: 10px;
  border-top-style: solid;
  border-top-width: 1px;
  border-top-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  overflow-x: hidden;
}

#mkrdlvgjzw .gt_stub {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
}

#mkrdlvgjzw .gt_stub_row_group {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
  vertical-align: top;
}

#mkrdlvgjzw .gt_row_group_first td {
  border-top-width: 2px;
}

#mkrdlvgjzw .gt_row_group_first th {
  border-top-width: 2px;
}

#mkrdlvgjzw .gt_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#mkrdlvgjzw .gt_first_summary_row {
  border-top-style: solid;
  border-top-color: #D3D3D3;
}

#mkrdlvgjzw .gt_first_summary_row.thick {
  border-top-width: 2px;
}

#mkrdlvgjzw .gt_last_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#mkrdlvgjzw .gt_grand_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#mkrdlvgjzw .gt_first_grand_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-style: double;
  border-top-width: 6px;
  border-top-color: #D3D3D3;
}

#mkrdlvgjzw .gt_last_grand_summary_row_top {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: double;
  border-bottom-width: 6px;
  border-bottom-color: #D3D3D3;
}

#mkrdlvgjzw .gt_striped {
  background-color: rgba(128, 128, 128, 0.05);
}

#mkrdlvgjzw .gt_table_body {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#mkrdlvgjzw .gt_footnotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#mkrdlvgjzw .gt_footnote {
  margin: 0px;
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#mkrdlvgjzw .gt_sourcenotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#mkrdlvgjzw .gt_sourcenote {
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#mkrdlvgjzw .gt_left {
  text-align: left;
}

#mkrdlvgjzw .gt_center {
  text-align: center;
}

#mkrdlvgjzw .gt_right {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

#mkrdlvgjzw .gt_font_normal {
  font-weight: normal;
}

#mkrdlvgjzw .gt_font_bold {
  font-weight: bold;
}

#mkrdlvgjzw .gt_font_italic {
  font-style: italic;
}

#mkrdlvgjzw .gt_super {
  font-size: 65%;
}

#mkrdlvgjzw .gt_footnote_marks {
  font-size: 75%;
  vertical-align: 0.4em;
  position: initial;
}

#mkrdlvgjzw .gt_asterisk {
  font-size: 100%;
  vertical-align: 0;
}

#mkrdlvgjzw .gt_indent_1 {
  text-indent: 5px;
}

#mkrdlvgjzw .gt_indent_2 {
  text-indent: 10px;
}

#mkrdlvgjzw .gt_indent_3 {
  text-indent: 15px;
}

#mkrdlvgjzw .gt_indent_4 {
  text-indent: 20px;
}

#mkrdlvgjzw .gt_indent_5 {
  text-indent: 25px;
}

#mkrdlvgjzw .katex-display {
  display: inline-flex !important;
  margin-bottom: 0.75em !important;
}

#mkrdlvgjzw div.Reactable &gt; div.rt-table &gt; div.rt-thead &gt; div.rt-tr.rt-tr-group-header &gt; div.rt-th-group:after {
  height: 0px !important;
}
&lt;/style&gt;
  &lt;table class="gt_table" data-quarto-disable-processing="false" data-quarto-bootstrap="false"&gt;
  &lt;caption&gt;Table B6. Fixed Effects Models: Political Connections&lt;/caption&gt;
  &lt;thead&gt;
    &lt;tr class="gt_col_headings"&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_left" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="a-"&gt; &lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Two-way-FE"&gt;Two-way FE&lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Individual-FE"&gt;Individual FE&lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Interaction-Model"&gt;Interaction Model&lt;/th&gt;
    &lt;/tr&gt;
  &lt;/thead&gt;
  &lt;tbody class="gt_table_body"&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Connections&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;-0.175***&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;-0.297***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;(0.057)&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;(0.071)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;After First Inspection&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;6.799***&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;6.051***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;(0.200)&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;(0.252)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;ESG Rating&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;0.151***&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;0.216***&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;0.217***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;(0.016)&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;(0.017)&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;(0.017)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Return on Assets&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;-0.374***&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;-0.332***&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;-0.326***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;(0.088)&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;(0.096)&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;(0.096)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Leverage&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;-0.002&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;-0.001&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;-0.001&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;(0.006)&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;(0.007)&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;(0.007)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Log(Registered Capital)&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;0.553***&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;2.165***&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;2.218***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;(0.190)&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;(0.198)&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;(0.198)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Num.Obs.&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;10777&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;10777&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;10777&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;R²&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;0.033&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;0.232&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;0.235&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Adj.R²&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;-0.129&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;0.104&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;0.107&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Firm fixed effects&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Industry fixed effects&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Province fixed effects&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Year fixed effects&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;N&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;N&lt;/td&gt;&lt;/tr&gt;
  &lt;/tbody&gt;
  &lt;tfoot class="gt_sourcenotes"&gt;
    &lt;tr&gt;
      &lt;td class="gt_sourcenote" colspan="4"&gt;Standard errors in parentheses.&lt;/td&gt;
    &lt;/tr&gt;
    &lt;tr&gt;
      &lt;td class="gt_sourcenote" colspan="4"&gt;* p &amp;lt; 0.1, ** p &amp;lt; 0.05, *** p &amp;lt; 0.01&lt;/td&gt;
    &lt;/tr&gt;
  &lt;/tfoot&gt;
  
&lt;/table&gt;
&lt;/div&gt;</code></pre>
</div>
</div>
</section>
<section id="p4-contrallocal-connection-continuous-1" class="level3">
<h3 class="anchored" data-anchor-id="p4-contrallocal-connection-continuous-1">P4 contral/local connection (continuous)</h3>
<div class="cell">
<div class="sourceCode cell-code" id="cb27"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb27-1"><a href="#cb27-1" aria-hidden="true" tabindex="-1"></a><span class="co">#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure</span></span>
<span id="cb27-2"><a href="#cb27-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(plm)</span>
<span id="cb27-3"><a href="#cb27-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb27-4"><a href="#cb27-4" aria-hidden="true" tabindex="-1"></a>p4plm1 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span>ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb27-5"><a href="#cb27-5" aria-hidden="true" tabindex="-1"></a>            <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>, <span class="st">"EndYear"</span>),<span class="at">model=</span><span class="st">"within"</span>,<span class="at">effect=</span><span class="st">"twoways"</span>)</span>
<span id="cb27-6"><a href="#cb27-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb27-7"><a href="#cb27-7" aria-hidden="true" tabindex="-1"></a>p4plm2 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span>ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb27-8"><a href="#cb27-8" aria-hidden="true" tabindex="-1"></a>            <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>),<span class="at">model=</span><span class="st">"within"</span>)</span>
<span id="cb27-9"><a href="#cb27-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb27-10"><a href="#cb27-10" aria-hidden="true" tabindex="-1"></a>p4plm3 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span>ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb27-11"><a href="#cb27-11" aria-hidden="true" tabindex="-1"></a>            <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>, <span class="st">"EndYear"</span>),<span class="at">model=</span><span class="st">"within"</span>,<span class="at">effect=</span><span class="st">"twoways"</span>)</span>
<span id="cb27-12"><a href="#cb27-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb27-13"><a href="#cb27-13" aria-hidden="true" tabindex="-1"></a>p4plm4 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span>ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb27-14"><a href="#cb27-14" aria-hidden="true" tabindex="-1"></a>            <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>),<span class="at">model=</span><span class="st">"within"</span>)</span>
<span id="cb27-15"><a href="#cb27-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb27-16"><a href="#cb27-16" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table for PLM central/local models using modelsummary</span></span>
<span id="cb27-17"><a href="#cb27-17" aria-hidden="true" tabindex="-1"></a><span class="co"># Define coefficient names for central/local connections</span></span>
<span id="cb27-18"><a href="#cb27-18" aria-hidden="true" tabindex="-1"></a>coef_map_plm_p4 <span class="ot">&lt;-</span> <span class="fu">c</span>(</span>
<span id="cb27-19"><a href="#cb27-19" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Age"</span> <span class="ot">=</span> <span class="st">"Age"</span>,</span>
<span id="cb27-20"><a href="#cb27-20" aria-hidden="true" tabindex="-1"></a>  <span class="st">"central_connection"</span> <span class="ot">=</span> <span class="st">"Central Connections"</span>,</span>
<span id="cb27-21"><a href="#cb27-21" aria-hidden="true" tabindex="-1"></a>  <span class="st">"local_connection"</span> <span class="ot">=</span> <span class="st">"Local Connections"</span>,</span>
<span id="cb27-22"><a href="#cb27-22" aria-hidden="true" tabindex="-1"></a>  <span class="st">"after_first_inspection"</span> <span class="ot">=</span> <span class="st">"After First Inspection"</span>,</span>
<span id="cb27-23"><a href="#cb27-23" aria-hidden="true" tabindex="-1"></a>  <span class="st">"ESG_Rate"</span> <span class="ot">=</span> <span class="st">"ESG Rating"</span>,</span>
<span id="cb27-24"><a href="#cb27-24" aria-hidden="true" tabindex="-1"></a>  <span class="st">"ROA"</span> <span class="ot">=</span> <span class="st">"Return on Assets"</span>,</span>
<span id="cb27-25"><a href="#cb27-25" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Leverage"</span> <span class="ot">=</span> <span class="st">"Leverage"</span>,</span>
<span id="cb27-26"><a href="#cb27-26" aria-hidden="true" tabindex="-1"></a>  <span class="st">"RegisterCapital_log"</span> <span class="ot">=</span> <span class="st">"Log(Registered Capital)"</span>,</span>
<span id="cb27-27"><a href="#cb27-27" aria-hidden="true" tabindex="-1"></a>  <span class="st">"central_connection:after_first_inspection"</span> <span class="ot">=</span> <span class="st">"Central Connections × After Inspection"</span>,</span>
<span id="cb27-28"><a href="#cb27-28" aria-hidden="true" tabindex="-1"></a>  <span class="st">"local_connection:after_first_inspection"</span> <span class="ot">=</span> <span class="st">"Local Connections × After Inspection"</span></span>
<span id="cb27-29"><a href="#cb27-29" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb27-30"><a href="#cb27-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb27-31"><a href="#cb27-31" aria-hidden="true" tabindex="-1"></a><span class="co"># Create additional rows for fixed effects information for P4 models</span></span>
<span id="cb27-32"><a href="#cb27-32" aria-hidden="true" tabindex="-1"></a>fe_rows_p4 <span class="ot">&lt;-</span> <span class="fu">tribble</span>(</span>
<span id="cb27-33"><a href="#cb27-33" aria-hidden="true" tabindex="-1"></a>  <span class="sc">~</span>term, <span class="sc">~</span><span class="st">`</span><span class="at">Central (Two-way)</span><span class="st">`</span>, <span class="sc">~</span><span class="st">`</span><span class="at">Central × Inspection</span><span class="st">`</span>, <span class="sc">~</span><span class="st">`</span><span class="at">Local (Two-way)</span><span class="st">`</span>, <span class="sc">~</span><span class="st">`</span><span class="at">Local × Inspection</span><span class="st">`</span>,</span>
<span id="cb27-34"><a href="#cb27-34" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Firm fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb27-35"><a href="#cb27-35" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Industry fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb27-36"><a href="#cb27-36" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Province fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb27-37"><a href="#cb27-37" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Year fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"N"</span>, <span class="st">"Y"</span>, <span class="st">"N"</span></span>
<span id="cb27-38"><a href="#cb27-38" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb27-39"><a href="#cb27-39" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb27-40"><a href="#cb27-40" aria-hidden="true" tabindex="-1"></a><span class="co"># Create modelsummary table with Organization &amp; Environment style</span></span>
<span id="cb27-41"><a href="#cb27-41" aria-hidden="true" tabindex="-1"></a>plm_p4_table <span class="ot">&lt;-</span> <span class="fu">modelsummary</span>(</span>
<span id="cb27-42"><a href="#cb27-42" aria-hidden="true" tabindex="-1"></a>  <span class="fu">list</span>(<span class="st">"Central (Two-way)"</span> <span class="ot">=</span> p4plm1, <span class="st">"Central × Inspection"</span> <span class="ot">=</span> p4plm2,</span>
<span id="cb27-43"><a href="#cb27-43" aria-hidden="true" tabindex="-1"></a>       <span class="st">"Local (Two-way)"</span> <span class="ot">=</span> p4plm3, <span class="st">"Local × Inspection"</span> <span class="ot">=</span> p4plm4),</span>
<span id="cb27-44"><a href="#cb27-44" aria-hidden="true" tabindex="-1"></a>  <span class="at">output =</span> <span class="st">"gt"</span>,</span>
<span id="cb27-45"><a href="#cb27-45" aria-hidden="true" tabindex="-1"></a>  <span class="at">stars =</span> <span class="fu">c</span>(<span class="st">'*'</span> <span class="ot">=</span> .<span class="dv">1</span>, <span class="st">'**'</span> <span class="ot">=</span> .<span class="dv">05</span>, <span class="st">'***'</span> <span class="ot">=</span> .<span class="dv">01</span>),</span>
<span id="cb27-46"><a href="#cb27-46" aria-hidden="true" tabindex="-1"></a>  <span class="at">coef_map =</span> coef_map_plm_p4,</span>
<span id="cb27-47"><a href="#cb27-47" aria-hidden="true" tabindex="-1"></a>  <span class="at">gof_map =</span> gof_map_plm,</span>
<span id="cb27-48"><a href="#cb27-48" aria-hidden="true" tabindex="-1"></a>  <span class="at">title =</span> <span class="st">"Table B7. Fixed Effects Models: Central vs Local Political Connections"</span>,</span>
<span id="cb27-49"><a href="#cb27-49" aria-hidden="true" tabindex="-1"></a>  <span class="at">notes =</span> <span class="fu">list</span>(<span class="st">"Standard errors in parentheses."</span>,</span>
<span id="cb27-50"><a href="#cb27-50" aria-hidden="true" tabindex="-1"></a>              <span class="st">"* p &lt; 0.1, ** p &lt; 0.05, *** p &lt; 0.01"</span>),</span>
<span id="cb27-51"><a href="#cb27-51" aria-hidden="true" tabindex="-1"></a>  <span class="at">fmt =</span> <span class="dv">3</span>,</span>
<span id="cb27-52"><a href="#cb27-52" aria-hidden="true" tabindex="-1"></a>  <span class="at">estimate =</span> <span class="st">"{estimate}{stars}"</span>,</span>
<span id="cb27-53"><a href="#cb27-53" aria-hidden="true" tabindex="-1"></a>  <span class="at">statistic =</span> <span class="st">"({std.error})"</span>,</span>
<span id="cb27-54"><a href="#cb27-54" aria-hidden="true" tabindex="-1"></a>  <span class="at">add_rows =</span> fe_rows_p4</span>
<span id="cb27-55"><a href="#cb27-55" aria-hidden="true" tabindex="-1"></a>) <span class="sc">%&gt;%</span></span>
<span id="cb27-56"><a href="#cb27-56" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_style</span>(</span>
<span id="cb27-57"><a href="#cb27-57" aria-hidden="true" tabindex="-1"></a>    <span class="at">style =</span> <span class="fu">cell_text</span>(<span class="at">weight =</span> <span class="st">"bold"</span>),</span>
<span id="cb27-58"><a href="#cb27-58" aria-hidden="true" tabindex="-1"></a>    <span class="at">locations =</span> <span class="fu">cells_column_labels</span>()</span>
<span id="cb27-59"><a href="#cb27-59" aria-hidden="true" tabindex="-1"></a>  ) <span class="sc">%&gt;%</span></span>
<span id="cb27-60"><a href="#cb27-60" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_style</span>(</span>
<span id="cb27-61"><a href="#cb27-61" aria-hidden="true" tabindex="-1"></a>    <span class="at">style =</span> <span class="fu">cell_text</span>(<span class="at">weight =</span> <span class="st">"bold"</span>),</span>
<span id="cb27-62"><a href="#cb27-62" aria-hidden="true" tabindex="-1"></a>    <span class="at">locations =</span> <span class="fu">cells_stub</span>()</span>
<span id="cb27-63"><a href="#cb27-63" aria-hidden="true" tabindex="-1"></a>  ) <span class="sc">%&gt;%</span></span>
<span id="cb27-64"><a href="#cb27-64" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_options</span>(</span>
<span id="cb27-65"><a href="#cb27-65" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.font.size =</span> <span class="dv">11</span>,</span>
<span id="cb27-66"><a href="#cb27-66" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.title.font.size =</span> <span class="dv">12</span>,</span>
<span id="cb27-67"><a href="#cb27-67" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.subtitle.font.size =</span> <span class="dv">11</span>,</span>
<span id="cb27-68"><a href="#cb27-68" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.border.top.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb27-69"><a href="#cb27-69" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb27-70"><a href="#cb27-70" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb27-71"><a href="#cb27-71" aria-hidden="true" tabindex="-1"></a>    <span class="at">column_labels.border.top.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb27-72"><a href="#cb27-72" aria-hidden="true" tabindex="-1"></a>    <span class="at">column_labels.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb27-73"><a href="#cb27-73" aria-hidden="true" tabindex="-1"></a>    <span class="at">stub.border.style =</span> <span class="st">"solid"</span></span>
<span id="cb27-74"><a href="#cb27-74" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb27-75"><a href="#cb27-75" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb27-76"><a href="#cb27-76" aria-hidden="true" tabindex="-1"></a><span class="co"># Save to file</span></span>
<span id="cb27-77"><a href="#cb27-77" aria-hidden="true" tabindex="-1"></a><span class="fu">gtsave</span>(plm_p4_table, <span class="st">"B7_plm_p4_OE_style.html"</span>)</span>
<span id="cb27-78"><a href="#cb27-78" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(plm_p4_table)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>&lt;div id="ytlahzlinp" style="padding-left:0px;padding-right:0px;padding-top:10px;padding-bottom:10px;overflow-x:auto;overflow-y:auto;width:auto;height:auto;"&gt;
  &lt;style&gt;#ytlahzlinp table {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#ytlahzlinp thead, #ytlahzlinp tbody, #ytlahzlinp tfoot, #ytlahzlinp tr, #ytlahzlinp td, #ytlahzlinp th {
  border-style: none;
}

#ytlahzlinp p {
  margin: 0;
  padding: 0;
}

#ytlahzlinp .gt_table {
  display: table;
  border-collapse: collapse;
  line-height: normal;
  margin-left: auto;
  margin-right: auto;
  color: #333333;
  font-size: 11px;
  font-weight: normal;
  font-style: normal;
  background-color: #FFFFFF;
  width: auto;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #A8A8A8;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #A8A8A8;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
}

#ytlahzlinp .gt_caption {
  padding-top: 4px;
  padding-bottom: 4px;
}

#ytlahzlinp .gt_title {
  color: #333333;
  font-size: 12px;
  font-weight: initial;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-color: #FFFFFF;
  border-bottom-width: 0;
}

#ytlahzlinp .gt_subtitle {
  color: #333333;
  font-size: 11px;
  font-weight: initial;
  padding-top: 3px;
  padding-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-color: #FFFFFF;
  border-top-width: 0;
}

#ytlahzlinp .gt_heading {
  background-color: #FFFFFF;
  text-align: center;
  border-bottom-color: #FFFFFF;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#ytlahzlinp .gt_bottom_border {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#ytlahzlinp .gt_col_headings {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#ytlahzlinp .gt_col_heading {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 6px;
  padding-left: 5px;
  padding-right: 5px;
  overflow-x: hidden;
}

#ytlahzlinp .gt_column_spanner_outer {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 4px;
}

#ytlahzlinp .gt_column_spanner_outer:first-child {
  padding-left: 0;
}

#ytlahzlinp .gt_column_spanner_outer:last-child {
  padding-right: 0;
}

#ytlahzlinp .gt_column_spanner {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 5px;
  overflow-x: hidden;
  display: inline-block;
  width: 100%;
}

#ytlahzlinp .gt_spanner_row {
  border-bottom-style: hidden;
}

#ytlahzlinp .gt_group_heading {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  text-align: left;
}

#ytlahzlinp .gt_empty_group_heading {
  padding: 0.5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: middle;
}

#ytlahzlinp .gt_from_md &gt; :first-child {
  margin-top: 0;
}

#ytlahzlinp .gt_from_md &gt; :last-child {
  margin-bottom: 0;
}

#ytlahzlinp .gt_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  margin: 10px;
  border-top-style: solid;
  border-top-width: 1px;
  border-top-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  overflow-x: hidden;
}

#ytlahzlinp .gt_stub {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
}

#ytlahzlinp .gt_stub_row_group {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
  vertical-align: top;
}

#ytlahzlinp .gt_row_group_first td {
  border-top-width: 2px;
}

#ytlahzlinp .gt_row_group_first th {
  border-top-width: 2px;
}

#ytlahzlinp .gt_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#ytlahzlinp .gt_first_summary_row {
  border-top-style: solid;
  border-top-color: #D3D3D3;
}

#ytlahzlinp .gt_first_summary_row.thick {
  border-top-width: 2px;
}

#ytlahzlinp .gt_last_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#ytlahzlinp .gt_grand_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#ytlahzlinp .gt_first_grand_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-style: double;
  border-top-width: 6px;
  border-top-color: #D3D3D3;
}

#ytlahzlinp .gt_last_grand_summary_row_top {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: double;
  border-bottom-width: 6px;
  border-bottom-color: #D3D3D3;
}

#ytlahzlinp .gt_striped {
  background-color: rgba(128, 128, 128, 0.05);
}

#ytlahzlinp .gt_table_body {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#ytlahzlinp .gt_footnotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#ytlahzlinp .gt_footnote {
  margin: 0px;
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#ytlahzlinp .gt_sourcenotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#ytlahzlinp .gt_sourcenote {
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#ytlahzlinp .gt_left {
  text-align: left;
}

#ytlahzlinp .gt_center {
  text-align: center;
}

#ytlahzlinp .gt_right {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

#ytlahzlinp .gt_font_normal {
  font-weight: normal;
}

#ytlahzlinp .gt_font_bold {
  font-weight: bold;
}

#ytlahzlinp .gt_font_italic {
  font-style: italic;
}

#ytlahzlinp .gt_super {
  font-size: 65%;
}

#ytlahzlinp .gt_footnote_marks {
  font-size: 75%;
  vertical-align: 0.4em;
  position: initial;
}

#ytlahzlinp .gt_asterisk {
  font-size: 100%;
  vertical-align: 0;
}

#ytlahzlinp .gt_indent_1 {
  text-indent: 5px;
}

#ytlahzlinp .gt_indent_2 {
  text-indent: 10px;
}

#ytlahzlinp .gt_indent_3 {
  text-indent: 15px;
}

#ytlahzlinp .gt_indent_4 {
  text-indent: 20px;
}

#ytlahzlinp .gt_indent_5 {
  text-indent: 25px;
}

#ytlahzlinp .katex-display {
  display: inline-flex !important;
  margin-bottom: 0.75em !important;
}

#ytlahzlinp div.Reactable &gt; div.rt-table &gt; div.rt-thead &gt; div.rt-tr.rt-tr-group-header &gt; div.rt-th-group:after {
  height: 0px !important;
}
&lt;/style&gt;
  &lt;table class="gt_table" data-quarto-disable-processing="false" data-quarto-bootstrap="false"&gt;
  &lt;caption&gt;Table B7. Fixed Effects Models: Central vs Local Political Connections&lt;/caption&gt;
  &lt;thead&gt;
    &lt;tr class="gt_col_headings"&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_left" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="a-"&gt; &lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Central-(Two-way)"&gt;Central (Two-way)&lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Central-×-Inspection"&gt;Central × Inspection&lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Local-(Two-way)"&gt;Local (Two-way)&lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Local-×-Inspection"&gt;Local × Inspection&lt;/th&gt;
    &lt;/tr&gt;
  &lt;/thead&gt;
  &lt;tbody class="gt_table_body"&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Central Connections&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;-0.261&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;-0.873***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;(0.187)&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.244)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Local Connections&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;-0.186***&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;-0.300***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;(0.063)&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.080)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;After First Inspection&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;6.481***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;6.154***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.212)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.250)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;ESG Rating&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;0.151***&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;0.216***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;0.150***&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;0.217***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;(0.016)&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.017)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;(0.016)&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.017)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Return on Assets&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;-0.375***&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;-0.329***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;-0.374***&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;-0.327***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;(0.088)&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.096)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;(0.088)&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.096)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Leverage&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;-0.002&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;-0.001&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;-0.002&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;-0.001&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;(0.006)&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.007)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;(0.006)&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.007)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Log(Registered Capital)&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;0.546***&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;2.186***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;0.551***&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;2.211***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;(0.190)&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.198)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;(0.190)&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.198)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Central Connections × After Inspection&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;1.285***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.304)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Local Connections × After Inspection&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;0.353***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;(0.082)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Num.Obs.&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;10777&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;10777&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;10777&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;10777&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;R²&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;0.032&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;0.234&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;0.033&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;0.234&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Adj.R²&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;-0.130&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;0.106&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;-0.129&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;0.106&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Firm fixed effects&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Industry fixed effects&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Province fixed effects&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Year fixed effects&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;N&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;N&lt;/td&gt;&lt;/tr&gt;
  &lt;/tbody&gt;
  &lt;tfoot class="gt_sourcenotes"&gt;
    &lt;tr&gt;
      &lt;td class="gt_sourcenote" colspan="5"&gt;Standard errors in parentheses.&lt;/td&gt;
    &lt;/tr&gt;
    &lt;tr&gt;
      &lt;td class="gt_sourcenote" colspan="5"&gt;* p &amp;lt; 0.1, ** p &amp;lt; 0.05, *** p &amp;lt; 0.01&lt;/td&gt;
    &lt;/tr&gt;
  &lt;/tfoot&gt;
  
&lt;/table&gt;
&lt;/div&gt;</code></pre>
</div>
</div>
</section>
</section>
</section>

</main>
<!-- /main column -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
</div> <!-- /content -->




</body></html>