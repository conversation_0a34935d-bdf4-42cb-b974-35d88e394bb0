#!/usr/bin/env python3
"""
Final script to reformat HTML tables properly
"""

import re
from pathlib import Path

def process_html_file(file_path):
    """Process a single HTML file"""
    print(f"Processing {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split into lines
    lines = content.split('\n')
    new_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Fix header row - replace Estimates/std. Error with (1), (2), (3), etc.
        if 'Estimates' in line and 'std. Error' in line:
            # Count how many Estimates we have to determine number of models
            estimates_count = line.count('Estimates')
            
            # Replace header
            new_header = line
            for model_num in range(1, estimates_count + 1):
                # Replace first occurrence of Estimates/std. Error pair
                pattern = r'<td[^>]*>Estimates</td>\s*<td[^>]*>std\. Error</td>'
                replacement = f'<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">({model_num})</td>'
                new_header = re.sub(pattern, replacement, new_header, count=1)
            
            new_lines.append(new_header)
            
        # Process data rows
        elif '<tr>' in line and i + 1 < len(lines):
            next_line = lines[i + 1]
            
            # Check if this is a data row with coefficients
            if (re.search(r'<sup>', next_line) or 
                re.search(r'>&#45;\d+\.\d+', next_line) or
                re.search(r'>\d+\.\d+', next_line)):
                
                # Extract all cell contents using regex
                cell_pattern = r'<td[^>]*>([^<]*(?:<sup>[^<]*</sup>)?[^<]*)</td>'
                cells = re.findall(cell_pattern, next_line)
                
                if len(cells) > 1:
                    var_name = cells[0]
                    data_cells = cells[1:]
                    
                    # Create coefficient row
                    coef_row = '<tr>\n'
                    coef_row += f'<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">{var_name}</td>\n'
                    
                    # Create std error row
                    std_row = '<tr>\n'
                    std_row += '<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; "></td>\n'
                    
                    # Process coefficient/std error pairs
                    for j in range(0, len(data_cells), 2):
                        if j + 1 < len(data_cells):
                            coef = data_cells[j]
                            std_err = data_cells[j + 1]
                            
                            # Add coefficient
                            coef_row += f'<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">{coef}</td>\n'
                            
                            # Add std error with parentheses
                            if std_err.strip():
                                std_row += f'<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">({std_err})</td>\n'
                            else:
                                std_row += '<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>\n'
                    
                    coef_row += '</tr>'
                    std_row += '</tr>'
                    
                    new_lines.append(line)  # <tr>
                    new_lines.append(coef_row)
                    new_lines.append(std_row)
                    
                    # Skip the original data row
                    i += 2
                    continue
        
        new_lines.append(line)
        i += 1
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print(f"Completed {file_path}")

def main():
    """Main function"""
    # Process all HTML files
    html_files = [
        'B2_p3_extended_inspection_OE_style.html',
        'B3_p4_extended_inspection_OE_style.html', 
        'B4_SOE_result_p1_OE_style.html',
        'B5_SOE_result_p2_OE_style.html',
        'B6_plm_p3_OE_style.html',
        'B7_plm_p4_OE_style.html'
    ]
    
    for html_file in html_files:
        file_path = Path(html_file)
        if file_path.exists():
            try:
                process_html_file(file_path)
            except Exception as e:
                print(f"Error processing {html_file}: {e}")
        else:
            print(f"File not found: {html_file}")

if __name__ == "__main__":
    main()
