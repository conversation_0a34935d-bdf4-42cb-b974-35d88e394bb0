# 验证dta1$Age是否随着endyear变化
# 检查同一个公司在不同年份的Age值是否正确递增

# 加载数据
load("dta1_20240903.RData")

# 加载必要的包
library(dplyr)
library(lubridate)

# 重新计算Age变量（确保使用最新的计算方法）
dta1 <- dta1 %>%
  mutate(
    EndYear = ymd(EndYear),
    Year = year(EndYear),
    Age = Year - EstablishYear
  )

# 1. 检查Age变量的基本信息
cat("=== Age变量基本信息 ===\n")
cat("Age变量的范围:", range(dta1$Age, na.rm = TRUE), "\n")
cat("Age变量的平均值:", mean(dta1$Age, na.rm = TRUE), "\n")
cat("Age变量的中位数:", median(dta1$Age, na.rm = TRUE), "\n")
cat("Age变量的缺失值数量:", sum(is.na(dta1$Age)), "\n\n")

# 2. 选择几个有多年数据的公司进行验证
cat("=== 选择有多年数据的公司进行验证 ===\n")
company_counts <- dta1 %>%
  group_by(Symbol) %>%
  summarise(year_count = n(), .groups = 'drop') %>%
  arrange(desc(year_count))

# 显示前10个有最多年份数据的公司
cat("前10个有最多年份数据的公司:\n")
print(head(company_counts, 10))

# 3. 选择前5个公司进行详细验证
top_companies <- head(company_counts$Symbol, 5)

cat("\n=== 验证前5个公司的Age变化情况 ===\n")
for(company in top_companies) {
  cat("\n公司:", company, "\n")
  company_data <- dta1 %>%
    filter(Symbol == company) %>%
    select(Symbol, Year, EstablishYear, Age) %>%
    arrange(Year)
  
  print(company_data)
  
  # 检查Age是否随年份递增
  if(nrow(company_data) > 1) {
    age_diff <- diff(company_data$Age)
    if(all(age_diff == 1, na.rm = TRUE)) {
      cat("✓ Age正确递增（每年+1）\n")
    } else {
      cat("✗ Age递增异常，差值为:", age_diff, "\n")
    }
  }
}

# 4. 全面检查所有公司的Age变化是否正确
cat("\n=== 全面检查所有公司的Age变化 ===\n")
age_check <- dta1 %>%
  group_by(Symbol) %>%
  arrange(Year) %>%
  mutate(
    age_diff = Age - lag(Age),
    year_diff = Year - lag(Year),
    correct_age = (age_diff == year_diff) | is.na(age_diff)
  ) %>%
  ungroup()

# 统计检查结果
incorrect_age_count <- sum(!age_check$correct_age, na.rm = TRUE)
total_transitions <- sum(!is.na(age_check$age_diff))

cat("总的年份转换数:", total_transitions, "\n")
cat("Age变化不正确的转换数:", incorrect_age_count, "\n")
cat("Age变化正确率:", (total_transitions - incorrect_age_count) / total_transitions * 100, "%\n")

# 5. 显示Age变化不正确的案例
if(incorrect_age_count > 0) {
  cat("\n=== Age变化不正确的案例 ===\n")
  incorrect_cases <- age_check %>%
    filter(!correct_age) %>%
    select(Symbol, Year, EstablishYear, Age, age_diff, year_diff)
  
  print(head(incorrect_cases, 20))
}

# 6. 验证Age计算公式是否正确
cat("\n=== 验证Age计算公式 ===\n")
formula_check <- dta1 %>%
  mutate(
    calculated_age = Year - EstablishYear,
    formula_correct = (Age == calculated_age) | (is.na(Age) & is.na(calculated_age))
  )

formula_incorrect_count <- sum(!formula_check$formula_correct, na.rm = TRUE)
total_records <- nrow(formula_check)

cat("总记录数:", total_records, "\n")
cat("Age计算公式不正确的记录数:", formula_incorrect_count, "\n")
cat("Age计算公式正确率:", (total_records - formula_incorrect_count) / total_records * 100, "%\n")

if(formula_incorrect_count > 0) {
  cat("\n=== Age计算公式不正确的案例 ===\n")
  incorrect_formula_cases <- formula_check %>%
    filter(!formula_correct) %>%
    select(Symbol, Year, EstablishYear, Age, calculated_age)
  
  print(head(incorrect_formula_cases, 10))
}

cat("\n=== 验证完成 ===\n")
