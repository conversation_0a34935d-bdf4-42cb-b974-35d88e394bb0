<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>P3 Connection (Continuous) - PLM Model</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
        }
        .code-block {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .r-code {
            color: #d73027;
            font-weight: bold;
        }
        .comment {
            color: #008000;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PLM Model</h1>
        <h2>P3 Connection (Continuous)</h2>
        
        <div class="code-block">
            <h3>P3 connection (continuous)</h3>
            <pre><code class="r-code">
#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure

# p3 model 1

library(plm)
p3way1 <- plm(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ESG_Rate, data=dta1,
              index=c("Symbol", "EndYear"),model="within",effect="twoways")


p3way2 <- plm(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta1,
              index=c("Symbol"),model="within")

p3way3 <- plm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage + ESG_Rate, data=dta1,
              index=c("Symbol"),model="within")

library(stargazer)
stargazer(p3way1, p3way2, p3way3,
          type="html",
          out="B5.html",
          column.labels = c("Year", "Individual", "Individual"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
            </code></pre>
        </div>
    </div>
</body>
</html>
