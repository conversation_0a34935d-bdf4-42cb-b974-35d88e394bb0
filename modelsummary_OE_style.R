# Organization & Environment Journal Style Tables using modelsummary
# This script creates publication-ready tables for lmer and plm models

# Install required packages if not already installed
# install.packages(c("modelsummary", "gt", "flextable", "kableExtra"))

library(modelsummary)
library(gt)
library(flextable)
library(kableExtra)
library(dplyr)
library(tibble)

# Load your data (assuming it's already loaded)
# load("dta1_20240903.RData")

# Function to create Organization & Environment style tables
create_OE_table <- function(models, model_names = NULL, title = "", 
                           output_format = "gt", filename = NULL) {
  
  # Define coefficient names for cleaner display
  coef_map <- c(
    "Age" = "Age",
    "connection_num" = "Connections",
    "central_connection" = "Central Connections",
    "local_connection" = "Local Connections",
    "after_first_inspection" = "After First Inspection",
    "extended_inspection" = "Inspection Influence",
    "SOE_new" = "State-owned Enterprise",
    "SOE_new_central" = "Central SOE",
    "SOE_new_local" = "Local SOE",
    "ESG_Rate" = "ESG Rating",
    "ROA" = "Return on Assets",
    "Leverage" = "Leverage",
    "RegisterCapital_log" = "Log(Registered Capital)",
    "connection_num:after_first_inspection" = "Connections × After Inspection",
    "connection_num:extended_inspection" = "Connections × Inspection Influence",
    "central_connection:after_first_inspection" = "Central Connections × After Inspection",
    "central_connection:extended_inspection" = "Central Connections × Inspection Influence",
    "local_connection:after_first_inspection" = "Local Connections × After Inspection",
    "local_connection:extended_inspection" = "Local Connections × Inspection Influence",
    "SOE_new:after_first_inspection" = "SOE × After Inspection",
    "SOE_new_central:after_first_inspection" = "Central SOE × After Inspection",
    "SOE_new_local:after_first_inspection" = "Local SOE × After Inspection"
  )
  
  # Goodness of fit statistics to include (matching your reference table)
  gof_map <- tribble(
    ~raw, ~clean, ~fmt,
    "AIC", "AIC", 2,
    "BIC", "BIC", 2,
    "logLik", "Log Likelihood", 2,
    "nobs", "Observations", 0,
    "ngrps", "Num. groups: City: Province", 0,
    "ngrps_Province", "Num. groups: Province", 0,
    "ngrps_CITY", "Num. groups: City", 0,
    "sigma2_City:Province", "Var: City: Province (Intercept)", 3,
    "sigma2_Province", "Var: Province (Intercept)", 3,
    "sigma2", "Var: Residual", 3,
    "icc", "ICC", 3,
    "r.squared", "R²", 3,
    "marginal.r.squared", "Marginal R²", 3,
    "conditional.r.squared", "Conditional R²", 3
  )
  
  # Create the table
  table <- modelsummary(
    models,
    output = output_format,
    stars = c('*' = .1, '**' = .05, '***' = .01),
    coef_map = coef_map,
    gof_map = gof_map,
    title = title,
    notes = list("Standard errors in parentheses.",
                "* p < 0.1, ** p < 0.05, *** p < 0.01"),
    fmt = 3,
    estimate = "{estimate}{stars}",
    statistic = "({std.error})"
  )
  
  # Apply Organization & Environment styling if using gt
  if (output_format == "gt") {
    table <- table %>%
      tab_style(
        style = cell_text(weight = "bold"),
        locations = cells_column_labels()
      ) %>%
      tab_style(
        style = cell_text(weight = "bold"),
        locations = cells_stub()
      ) %>%
      tab_options(
        table.font.size = 11,
        heading.title.font.size = 12,
        heading.subtitle.font.size = 11,
        table.border.top.style = "solid",
        table.border.bottom.style = "solid",
        heading.border.bottom.style = "solid",
        column_labels.border.top.style = "solid",
        column_labels.border.bottom.style = "solid",
        stub.border.style = "solid"
      )
  }
  
  # Save to file if filename provided
  if (!is.null(filename)) {
    if (output_format == "gt") {
      gtsave(table, filename)
    } else if (output_format == "flextable") {
      save_as_docx(table, path = filename)
    } else if (output_format == "html") {
      writeLines(as.character(table), filename)
    }
  }
  
  return(table)
}

# Simplified function for mixed effects models with enhanced statistics
create_OE_lmer_table <- function(models, model_names = NULL, title = "",
                                output_format = "gt", filename = NULL) {

  # Define coefficient names for cleaner display
  coef_map <- c(
    "Age" = "Firm Age",
    "connection_num" = "Political Connections",
    "central_connection" = "Central Government Connections",
    "local_connection" = "Local Government Connections",
    "after_first_inspection" = "After First Inspection",
    "extended_inspection" = "Extended Inspection Period",
    "SOE_new" = "State-owned Enterprise",
    "SOE_new_central" = "Central SOE",
    "SOE_new_local" = "Local SOE",
    "ESG_Rate" = "ESG Rating",
    "ROA" = "Return on Assets",
    "Leverage" = "Leverage",
    "RegisterCapital_log" = "Log(Registered Capital)",
    "connection_num:after_first_inspection" = "Connections × After Inspection",
    "connection_num:extended_inspection" = "Connections × Extended Inspection",
    "central_connection:after_first_inspection" = "Central Connections × After Inspection",
    "central_connection:extended_inspection" = "Central Connections × Extended Inspection",
    "local_connection:after_first_inspection" = "Local Connections × After Inspection",
    "local_connection:extended_inspection" = "Local Connections × Extended Inspection",
    "SOE_new:after_first_inspection" = "SOE × After Inspection",
    "SOE_new_central:after_first_inspection" = "Central SOE × After Inspection",
    "SOE_new_local:after_first_inspection" = "Local SOE × After Inspection"
  )

  # Create the table with default statistics first
  table <- modelsummary(
    models,
    output = output_format,
    stars = c('*' = .1, '**' = .05, '***' = .01),
    coef_map = coef_map,
    title = title,
    notes = list("Standard errors in parentheses.",
                "* p < 0.1, ** p < 0.05, *** p < 0.01"),
    fmt = 3,
    estimate = "{estimate}{stars}",
    statistic = "({std.error})"
  )

  # Apply Organization & Environment styling if using gt
  if (output_format == "gt") {
    table <- table %>%
      tab_style(
        style = cell_text(weight = "bold"),
        locations = cells_column_labels()
      ) %>%
      tab_style(
        style = cell_text(weight = "bold"),
        locations = cells_stub()
      ) %>%
      tab_options(
        table.font.size = 11,
        heading.title.font.size = 12,
        heading.subtitle.font.size = 11,
        table.border.top.style = "solid",
        table.border.bottom.style = "solid",
        heading.border.bottom.style = "solid",
        column_labels.border.top.style = "solid",
        column_labels.border.bottom.style = "solid",
        stub.border.style = "solid"
      )
  }

  # Save to file if filename provided
  if (!is.null(filename)) {
    if (output_format == "gt") {
      gtsave(table, filename)
    } else if (output_format == "flextable") {
      save_as_docx(table, path = filename)
    } else if (output_format == "html") {
      writeLines(as.character(table), filename)
    }
  }

  return(table)
}

# Specialized function for mixed effects models with detailed statistics
create_OE_lmer_table <- function(models, model_names = NULL, title = "",
                                output_format = "gt", filename = NULL) {

  # Define coefficient names for cleaner display
  coef_map <- c(
    "Age" = "Firm Age",
    "connection_num" = "Political Connections",
    "central_connection" = "Central Government Connections",
    "local_connection" = "Local Government Connections",
    "after_first_inspection" = "After First Inspection",
    "extended_inspection" = "Extended Inspection Period",
    "SOE_new" = "State-owned Enterprise",
    "SOE_new_central" = "Central SOE",
    "SOE_new_local" = "Local SOE",
    "ESG_Rate" = "ESG Rating",
    "ROA" = "Return on Assets",
    "Leverage" = "Leverage",
    "RegisterCapital_log" = "Log(Registered Capital)",
    "connection_num:after_first_inspection" = "Connections × After Inspection",
    "connection_num:extended_inspection" = "Connections × Extended Inspection",
    "central_connection:after_first_inspection" = "Central Connections × After Inspection",
    "central_connection:extended_inspection" = "Central Connections × Extended Inspection",
    "local_connection:after_first_inspection" = "Local Connections × After Inspection",
    "local_connection:extended_inspection" = "Local Connections × Extended Inspection",
    "SOE_new:after_first_inspection" = "SOE × After Inspection",
    "SOE_new_central:after_first_inspection" = "Central SOE × After Inspection",
    "SOE_new_local:after_first_inspection" = "Local SOE × After Inspection"
  )

  # Custom function to extract mixed model statistics
  extract_lmer_stats <- function(model) {
    if (inherits(model, "lmerMod")) {
      # Extract variance components
      vc <- as.data.frame(VarCorr(model))

      # Get group information
      ngrps <- summary(model)$ngrps

      # Create custom statistics
      stats <- list(
        "AIC" = AIC(model),
        "BIC" = BIC(model),
        "logLik" = as.numeric(logLik(model)),
        "nobs" = nobs(model)
      )

      # Add group counts
      if ("PROVINCE:CITY" %in% names(ngrps)) {
        stats[["ngrps_City_Province"]] <- ngrps[["PROVINCE:CITY"]]
      }
      if ("PROVINCE" %in% names(ngrps)) {
        stats[["ngrps_Province"]] <- ngrps[["PROVINCE"]]
      }
      if ("CITY" %in% names(ngrps)) {
        stats[["ngrps_City"]] <- ngrps[["CITY"]]
      }

      # Add variance components
      for (i in 1:nrow(vc)) {
        if (vc$grp[i] == "PROVINCE:CITY") {
          stats[["Var_City_Province_Intercept"]] <- vc$vcov[i]
        } else if (vc$grp[i] == "PROVINCE") {
          stats[["Var_Province_Intercept"]] <- vc$vcov[i]
        } else if (vc$grp[i] == "Residual") {
          stats[["Var_Residual"]] <- vc$vcov[i]
        }
      }

      return(stats)
    }
    return(NULL)
  }

  # Create the table with custom statistics
  table <- modelsummary(
    models,
    output = output_format,
    stars = c('*' = .1, '**' = .05, '***' = .01),
    coef_map = coef_map,
    title = title,
    notes = list("Standard errors in parentheses.",
                "* p < 0.1, ** p < 0.05, *** p < 0.01"),
    fmt = 3,
    estimate = "{estimate}{stars}",
    statistic = "({std.error})",
    gof_function = extract_lmer_stats
  )

  # Apply Organization & Environment styling if using gt
  if (output_format == "gt") {
    table <- table %>%
      tab_style(
        style = cell_text(weight = "bold"),
        locations = cells_column_labels()
      ) %>%
      tab_style(
        style = cell_text(weight = "bold"),
        locations = cells_stub()
      ) %>%
      tab_options(
        table.font.size = 11,
        heading.title.font.size = 12,
        heading.subtitle.font.size = 11,
        table.border.top.style = "solid",
        table.border.bottom.style = "solid",
        heading.border.bottom.style = "solid",
        column_labels.border.top.style = "solid",
        column_labels.border.bottom.style = "solid",
        stub.border.style = "solid"
      )
  }

  # Save to file if filename provided
  if (!is.null(filename)) {
    if (output_format == "gt") {
      gtsave(table, filename)
    } else if (output_format == "flextable") {
      save_as_docx(table, path = filename)
    } else if (output_format == "html") {
      writeLines(as.character(table), filename)
    }
  }

  return(table)
}

# Example usage for your lmer models
# Uncomment and modify the following code when you have your models ready:
#
# lmer_models <- list(
#   "Model 1" = p3mix1,
#   "Model 2" = p3mix2,
#   "Model 3" = p3mix3
# )
#
# # Create table for multilevel models
# lmer_table <- create_OE_table(
#   models = lmer_models,
#   title = "Multilevel Models: Political Connections and Environmental Disclosure",
#   output_format = "gt",
#   filename = "lmer_results_OE_style.html"
# )
#
# # Example usage for your plm models
# plm_models <- list(
#   "Year FE" = p3way1,
#   "Individual FE" = p3way2,
#   "Two-way FE" = p3way3
# )
#
# # Create table for fixed effects models
# plm_table <- create_OE_table(
#   models = plm_models,
#   title = "Fixed Effects Models: Political Connections and Environmental Disclosure",
#   output_format = "gt",
#   filename = "plm_results_OE_style.html"
# )
#
# # Alternative: Create Word-compatible table using flextable
# plm_table_word <- create_OE_table(
#   models = plm_models,
#   title = "Fixed Effects Models: Political Connections and Environmental Disclosure",
#   output_format = "flextable",
#   filename = "plm_results_OE_style.docx"
# )
#
# # Print tables
# print(lmer_table)
# print(plm_table)
#
# # For LaTeX output (if needed for journal submission)
# latex_table <- modelsummary(
#   plm_models,
#   output = "latex",
#   stars = c('*' = .1, '**' = .05, '***' = .01),
#   title = "Fixed Effects Models: Political Connections and Environmental Disclosure",
#   notes = list("Standard errors in parentheses.",
#               "* p < 0.1, ** p < 0.05, *** p < 0.01")
# )
#
# # Save LaTeX table
# writeLines(latex_table, "plm_results_OE_style.tex")
