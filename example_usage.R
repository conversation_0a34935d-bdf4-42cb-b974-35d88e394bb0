# Example: Using modelsummary for Organization & Environment style tables
# Based on your existing models

# Load required libraries
library(modelsummary)
library(gt)
library(lme4)
library(lmerTest)
library(plm)

# Load your data
load("dta1_20240903.RData")

# Source the styling function
source("modelsummary_OE_style.R")

# ============================================================================
# Example 1: Your existing lmer models (from OE revision v3.qmd)
# ============================================================================

# Recreate your multilevel models
p3mix1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + 
               ROA + ESG_Rate + Leverage + RegisterCapital_log + 
               (1 | PROVINCE/CITY), data = dta1)

p3mix2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + 
               ESG_Rate + RegisterCapital_log + ROA + Leverage + 
               (1 | PROVINCE/CITY), data = dta1)

p3mix3 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + 
               after_first_inspection + connection_num:after_first_inspection + 
               ESG_Rate + RegisterCapital_log + ROA + Leverage + 
               (1 | PROVINCE/CITY), data = dta1)

# Create Organization & Environment style table for multilevel models
lmer_table <- create_OE_table(
  models = list("Model 1" = p3mix1, "Model 2" = p3mix2, "Model 3" = p3mix3),
  title = "Multilevel Models: Political Connections and Environmental Disclosure",
  output_format = "gt",
  filename = "Table1_Multilevel_Models.html"
)

print(lmer_table)

# ============================================================================
# Example 2: Your existing plm models (from OE revision.rmd)
# ============================================================================

# Recreate your fixed effects models
p3way1 <- plm(Environmental_Information_Disclosure ~ Age + connection_num + 
              ROA + ESG_Rate + Leverage + as.factor(IndustryName) + 
              as.factor(PROVINCE) + RegisterCapital_log, 
              data = dta1, index = c("Symbol", "EndYear"), 
              model = "within", effect = "twoways")

p3way2 <- plm(Environmental_Information_Disclosure ~ after_first_inspection + 
              ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
              RegisterCapital_log + ROA + Leverage, 
              data = dta1, index = c("Symbol"), model = "within")

p3way3 <- plm(Environmental_Information_Disclosure ~ Age + 
              after_first_inspection * connection_num + ESG_Rate + 
              as.factor(IndustryName) + as.factor(PROVINCE) + 
              RegisterCapital_log + ROA + Leverage, 
              data = dta1, index = c("Symbol"), model = "within")

# Create Organization & Environment style table for fixed effects models
plm_table <- create_OE_table(
  models = list("Two-way FE" = p3way1, "Individual FE" = p3way2, "Interaction" = p3way3),
  title = "Fixed Effects Models: Political Connections and Environmental Disclosure",
  output_format = "gt", 
  filename = "Table2_Fixed_Effects_Models.html"
)

print(plm_table)

# ============================================================================
# Example 3: Central vs Local connections (from your robustness checks)
# ============================================================================

# Models with central and local connections
p4m1 <- plm(Environmental_Information_Disclosure ~ Age + central_connection + 
            ESG_Rate + ROA + Leverage + RegisterCapital_log + 
            as.factor(IndustryName) + as.factor(PROVINCE), 
            data = dta1, index = c("Symbol", "EndYear"), 
            model = "within", effect = "twoways")

p4m2 <- plm(Environmental_Information_Disclosure ~ central_connection + 
            ESG_Rate + RegisterCapital_log + ROA + Leverage + 
            as.factor(IndustryName) + as.factor(PROVINCE), 
            data = dta1, index = c("Symbol"), model = "within")

p4m3 <- plm(Environmental_Information_Disclosure ~ Age + local_connection + 
            ESG_Rate + ROA + Leverage + RegisterCapital_log + 
            as.factor(IndustryName) + as.factor(PROVINCE), 
            data = dta1, index = c("Symbol", "EndYear"), 
            model = "within", effect = "twoways")

p4m4 <- plm(Environmental_Information_Disclosure ~ local_connection + 
            ESG_Rate + RegisterCapital_log + ROA + Leverage + 
            as.factor(IndustryName) + as.factor(PROVINCE), 
            data = dta1, index = c("Symbol"), model = "within")

# Create table comparing central vs local connections
connection_table <- create_OE_table(
  models = list("Central (Two-way)" = p4m1, "Central (Individual)" = p4m2,
                "Local (Two-way)" = p4m3, "Local (Individual)" = p4m4),
  title = "Central vs Local Political Connections",
  output_format = "gt",
  filename = "Table3_Connection_Types.html"
)

print(connection_table)

# ============================================================================
# Create Word-compatible versions for journal submission
# ============================================================================

# Word version of main results
word_table <- create_OE_table(
  models = list("Model 1" = p3mix1, "Model 2" = p3mix2, "Model 3" = p3mix3),
  title = "Multilevel Models: Political Connections and Environmental Disclosure",
  output_format = "flextable",
  filename = "Main_Results_Table.docx"
)

# LaTeX version for journal submission
latex_output <- modelsummary(
  list("Model 1" = p3mix1, "Model 2" = p3mix2, "Model 3" = p3mix3),
  output = "latex",
  stars = c('*' = .1, '**' = .05, '***' = .01),
  title = "Multilevel Models: Political Connections and Environmental Disclosure",
  coef_map = c(
    "Age" = "Firm Age",
    "connection_num" = "Political Connections", 
    "after_first_inspection" = "After First Inspection",
    "ESG_Rate" = "ESG Rating",
    "ROA" = "Return on Assets",
    "Leverage" = "Leverage",
    "RegisterCapital_log" = "Log(Registered Capital)",
    "connection_num:after_first_inspection" = "Connections × After Inspection"
  ),
  notes = list("Standard errors in parentheses.",
              "* p < 0.1, ** p < 0.05, *** p < 0.01")
)

writeLines(latex_output, "Main_Results_Table.tex")
