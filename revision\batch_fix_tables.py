#!/usr/bin/env python3
"""
批量修复所有HTML表格的脚本
"""

import os
import re

def fix_html_table(filename):
    print(f"修复文件: {filename}")
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建备份
    backup_name = filename + '.backup_batch'
    with open(backup_name, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # 确定列数
    if 'B3_' in filename:
        data_cols = 5  # Predictors + 4 data columns
        header_colspan = 4
    else:
        data_cols = 4  # Predictors + 3 data columns  
        header_colspan = 3
    
    # 修复表头colspan
    content = re.sub(r'<th[^>]*colspan="[^"]*"([^>]*)>Dependent variable:', 
                     f'<th colspan="{header_colspan}"\\1>Dependent variable:', content)
    
    # 修复Random Effects行
    content = re.sub(r'<td[^>]*colspan="[^"]*"([^>]*)>Random Effects</td>', 
                     f'<td colspan="{data_cols}"\\1>Random Effects</td>', content)
    
    # 修复统计数据行的colspan
    # 移除所有统计数据单元格的colspan属性
    stats_pattern = r'<td([^>]*?)colspan="[^"]*"([^>]*?)>([^<]*(?:σ|τ|ICC|Observations|Marginal R|AIC|log-Likelihood|CITY|PROVINCE|[0-9.]+)[^<]*)</td>'
    content = re.sub(stats_pattern, r'<td\1\2>\3</td>', content)
    
    # 修复注释行的colspan
    content = re.sub(r'<td[^>]*colspan="[^"]*"([^>]*)>\* p&lt;0\.1', 
                     f'<td colspan="{data_cols}"\\1>* p&lt;0.1', content)
    
    # 写入修复后的内容
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"修复完成: {filename}")

def main():
    # 获取所有HTML文件（除了已经修复的B2文件）
    html_files = [f for f in os.listdir('.') if f.endswith('.html') and f != 'B2_p3_extended_inspection_OE_style.html']
    
    for html_file in html_files:
        try:
            fix_html_table(html_file)
        except Exception as e:
            print(f"修复 {html_file} 时出错: {e}")

if __name__ == "__main__":
    main()
